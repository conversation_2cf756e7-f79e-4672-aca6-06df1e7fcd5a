<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="StratosApplication" type="Application" factoryName="Application" nameIsGenerated="true">
    <option name="ALTERNATIVE_JRE_PATH" value="17" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <envs>
      <env name="DC_ID" value="nb6" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.phonepe.merchant.platform.stratos.server.StratosApplication" />
    <module name="stratos-server" />
    <option name="PROGRAM_PARAMETERS" value="server stratos-server/config/local.yml" />
    <option name="VM_PARAMETERS" value="-DlocalConfig=true --add-opens java.base/java.lang=ALL-UNNAMED -Xmx1024m" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.phonepe.merchant.platform.stratos.server.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>