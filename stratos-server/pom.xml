<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <parent>
    <groupId>com.phonepe.merchant.platform</groupId>
    <artifactId>stratos</artifactId>
      <version>2.0.69-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>stratos-server</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.phonepe.merchant.platform</groupId>
      <artifactId>stratos-models</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>io.hypersistence</groupId>
      <artifactId>hypersistence-utils-hibernate-55</artifactId>
      <version>3.2.0</version>
    </dependency>

    <!--basics-->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>org.reflections</groupId>
      <artifactId>reflections</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>

    <!--dropwizard-->
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-jackson</artifactId>
    </dependency>
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-core</artifactId>
    </dependency>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna</artifactId>
      <version>5.7.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-metrics</artifactId>
    </dependency>
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-forms</artifactId>
    </dependency>
    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-testing</artifactId>
      <scope>test</scope>
    </dependency>


    <!--dropwizard bundles-->
    <dependency>
      <groupId>ru.vyarus</groupId>
      <artifactId>dropwizard-guicey</artifactId>
    </dependency>
    <dependency>
      <groupId>io.appform.ranger</groupId>
      <artifactId>ranger-discovery-bundle</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <groupId>io.appform.ranger</groupId>
          <artifactId>ranger-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.appform.ranger</groupId>
          <artifactId>ranger-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.appform.ranger</groupId>
          <artifactId>ranger-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.appform.ranger</groupId>
          <artifactId>ranger-core</artifactId>
        </exclusion>
      </exclusions>
      <version>1.1.1</version>
    </dependency>
    <dependency>
      <groupId>com.phonepe.platform.http.v2</groupId>
      <artifactId>http-feign</artifactId>
      <version>${http.feign.version}</version>
    </dependency>

    <dependency>
      <groupId>com.phonepe.dataplatform</groupId>
      <artifactId>kratos-client</artifactId>
      <version>${kratos.client.version}</version>
      <exclusions>
        <exclusion>
          <groupId>io.dropwizard.discovery</groupId>
          <artifactId>dropwizard-service-discovery-bundle</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.avro</groupId>
          <artifactId>*</artifactId>
        </exclusion>
        <exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.sgoertzen</groupId>
          <artifactId>sonar-break-maven-plugin</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>zeus-models</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.http.v2</groupId>
          <artifactId>http-client-all</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.http.v2</groupId>
          <artifactId>http-executor</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>requestinfo-models</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.phonepe.dataplatform</groupId>
      <artifactId>profile-model</artifactId>
      <version>1.7.36</version>
      <exclusions>
        <exclusion>
          <groupId>*</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Zeus Models -->

    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>zeus-models</artifactId>
      <version>${zeus.model.version}</version>
    </dependency>
    <!-- Warden Models -->

    <dependency>
      <groupId>com.phonepe.services.warden</groupId>
      <artifactId>warden-models</artifactId>
      <version>${warden.models.version}</version>
      <exclusions>
        <exclusion>
          <groupId>feign.ranger</groupId>
          <artifactId>feign-ranger</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.raven.dropwizard</groupId>
          <artifactId>dropwizard-primer</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>dropwizard-requestinfo-bundle</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.hystrix</groupId>
      <artifactId>hystrix-configurator</artifactId>
      <version>${hystrix.configurator.version}</version>
      <exclusions>
        <exclusion>
          <groupId>javax.validation</groupId>
          <artifactId>validation-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.phonepe.dataplatform</groupId>
      <artifactId>kratos-stratos-model</artifactId>
      <version>${kratos.stratos.model.version}</version>
    </dependency>

    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>requestinfo-bundle</artifactId>
      <version>2.0.23-23</version>
      <exclusions>
        <exclusion>
          <groupId>feign.ranger</groupId>
          <artifactId>feign-ranger</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>metric-ingestion-bundle</artifactId>
      <version>${dropwizard.metric.bundle.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>zeus-models</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.raven.dropwizard</groupId>
      <artifactId>dropwizard-oor</artifactId>
      <version>${dropwizard.oor.bundle.version}</version>
    </dependency>

    <dependency>
      <groupId>in.vectorpro.dropwizard</groupId>
      <artifactId>dropwizard-swagger</artifactId>
      <version>${dropwizard.swagger.version}</version>
    </dependency>

    <dependency>
      <groupId>io.dropwizard</groupId>
      <artifactId>dropwizard-hibernate</artifactId>
      <version>${dropwizard.version}</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-envers</artifactId>
      <version>${hibernate.version}</version>
    </dependency>

    <dependency>
      <groupId>io.appform.dropwizard.sharding</groupId>
      <artifactId>db-sharding-bundle</artifactId>
      <version>${dropwizard.db.sharding.bundle.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.javassist</groupId>
          <artifactId>javassist</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.reflections</groupId>
          <artifactId>reflections</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.dropwizard</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.phonepe.data.provider</groupId>
      <artifactId>rosey-data-provider-bundle</artifactId>
      <version>${data.provider.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.glassfish.jersey.core</groupId>
          <artifactId>*</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.glassfish.jersey.inject</groupId>
          <artifactId>*</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.appform.ranger</groupId>
          <artifactId>ranger-discovery-bundle</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>tstore-client-bundle</artifactId>
      <version>${tstore.bundle.version}</version>
    </dependency>

<!--    zencast model for sending email-->

    <dependency>
      <groupId>com.phonepe.growth</groupId>
      <artifactId>zencast-model</artifactId>
      <version>${zencast-model.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.phonepe.platform.http</groupId>
          <artifactId>http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.bullhorn</groupId>
          <artifactId>bullhorn-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.dataplatform</groupId>
          <artifactId>yoda-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Queue -->
    <dependency>
      <groupId>io.appform.dropwizard.actors</groupId>
      <artifactId>dropwizard-rabbitmq-actors</artifactId>
      <version>${dropwizard.rabbitmq.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.dataformat</groupId>
          <artifactId>jackson-dataformat-yaml</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.rabbitmq</groupId>
      <artifactId>amqp-client</artifactId>
      <version>${rabbitmq.client.version}</version>
    </dependency>


    <!-- AeroSpike -->
    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>aerospike-bundle</artifactId>
      <version>${dropwizard.aerospike.version}</version>
    </dependency>

    <!--hystrix-->
    <dependency>
      <groupId>com.netflix.hystrix</groupId>
      <artifactId>hystrix-core</artifactId>
    </dependency>
    <dependency>
      <groupId>io.appform.core</groupId>
      <artifactId>hystrix-function-wrapper</artifactId>
    </dependency>
    <dependency>
      <groupId>org.zapodot</groupId>
      <artifactId>hystrix-dropwizard-bundle</artifactId>
      <version>${hystrix.dropwizard.version}</version>
      <exclusions>
        <exclusion>
          <groupId>io.dropwizard</groupId>
          <artifactId>dropwizard-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--phonepe-->
    <dependency>
      <groupId>com.phonepe.models</groupId>
      <artifactId>phonepe-model</artifactId>
      <version>${phonepe.model.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.phonepe.nexus</groupId>
          <artifactId>nexus-models</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>dropwizard-requestinfo-bundle</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.merchant</groupId>
          <artifactId>legion-models</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>zeus-models</artifactId>
        </exclusion>
        <exclusion>
          <groupId>feign.ranger</groupId>
          <artifactId>feign-ranger</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.http</groupId>
          <artifactId>http-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.phonepe.payments</groupId>
      <artifactId>upi-client-model</artifactId>
    </dependency>

    <dependency>
      <groupId>com.phonepe.platform.http.v2</groupId>
      <artifactId>http-client-all</artifactId>
    </dependency>

    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>docstore-common</artifactId>
      <version>${docstore-common.version}</version>
    </dependency>

    <dependency>
      <artifactId>olympus-im-client</artifactId>
      <groupId>com.phonepe.olympus-im</groupId>
      <version>${olympus.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.glassfish.jersey.containers</groupId>
          <artifactId>jersey-container-servlet-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.raven.dropwizard</groupId>
          <artifactId>dropwizard-primer</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>zeus-models</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>rosey-dropwizard-config</artifactId>
      <version>${rosey.dropwizard.config}</version>
      <exclusions>
        <exclusion>
          <groupId>org.hibernate</groupId>
          <artifactId>*</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.phonepe.dataplatform</groupId>
      <artifactId>event-ingestion-client</artifactId>
      <version>${dropwizard.event.ingestion.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.hibernate</groupId>
          <artifactId>hibernate-validator</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.models</groupId>
          <artifactId>phonepe-model</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.http.v2</groupId>
          <artifactId>http-client-all</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>requestinfo-bundle</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.appform.ranger</groupId>
          <artifactId>ranger-discovery-bundle</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.phonepe.platform.http.server.metrics</groupId>
      <artifactId>api-metrics</artifactId>
      <version>${api.metrics.version}</version>
    </dependency>

    <dependency>
      <groupId>com.phonepe.merchants.platform</groupId>
      <artifactId>primus-core</artifactId>
      <version>${primus.version}</version>
      <exclusions>
        <exclusion>
          <groupId>io.raven.dropwizard</groupId>
          <artifactId>dropwizard-primer</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.gandalf</groupId>
          <artifactId>gandalf-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.docstore</groupId>
          <artifactId>docstore-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.raven.dropwizard</groupId>
          <artifactId>dropwizard-primer</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.gandalf</groupId>
          <artifactId>gandalf-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.docstore</groupId>
          <artifactId>docstore-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.appform.dropwizard.discovery</groupId>
          <artifactId>dropwizard-service-discovery-bundle</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform.http</groupId>
          <artifactId>http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>dropwizard-requestinfo-bundle</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.phonepe.platform.filters</groupId>
      <artifactId>api-killer-core</artifactId>
      <version>${api.killer.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.phonepe.olympus-im</groupId>
          <artifactId>olympus-im-models</artifactId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>com.github.jknack</groupId>
      <artifactId>handlebars</artifactId>
      <version>${handlebars.version}</version>
    </dependency>

    <dependency>
      <groupId>com.github.jknack</groupId>
      <artifactId>handlebars-jackson2</artifactId>
      <version>${handlebars.version}</version>
    </dependency>

    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>executors</artifactId>
      <version>0.0.5</version>
    </dependency>

    <dependency>
      <groupId>com.phonepe.growth</groupId>
      <artifactId>neuron-client</artifactId>
      <version>${neuron.client.version}</version>
    <exclusions>
    <exclusion>
        <groupId>com.flipkart.ranger</groupId>
        <artifactId>ranger</artifactId>
      </exclusion>
      <exclusion>
        <groupId>feign.ranger</groupId>
        <artifactId>feign-ranger</artifactId>
      </exclusion>
    </exclusions>
    </dependency>

    <dependency>
      <groupId>com.phonepe.platform.killswitch</groupId>
      <artifactId>killswitch-common</artifactId>
      <version>${killswitch.client.version}</version>
    </dependency>

    <!--libs-->

    <dependency>
      <groupId>com.platform</groupId>
      <artifactId>validation-bundle</artifactId>
      <version>${validation.bundle.version}</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.logging</groupId>
      <artifactId>jboss-logging</artifactId>
      <version>3.3.0.Final</version>
    </dependency>
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjrt</artifactId>
      <version>${aspectj.version}</version>
    </dependency>
    <dependency>
      <groupId>io.appform.functionmetrics</groupId>
      <artifactId>function-metrics</artifactId>
      <version>${fuction.metrics.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-framework</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mariadb.jdbc</groupId>
      <artifactId>mariadb-java-client</artifactId>
      <version>${mariadb.client.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.statemachine</groupId>
      <artifactId>spring-statemachine-core</artifactId>
      <version>${spring.statemachine.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <version>${spring.version}</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-csv</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-core</artifactId>
      <version>${openfeign.version}</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-jackson</artifactId>
      <version>${openfeign.version}</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-httpclient</artifactId>
      <version>${openfeign.version}</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-okhttp</artifactId>
      <version>${openfeign.version}</version>
    </dependency>

    <dependency>
      <groupId>guru.nidi</groupId>
      <artifactId>graphviz-java</artifactId>
      <version>${graphviz.version}</version>
    </dependency>

    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <version>1.10.21</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>${commons.lang.version}</version>
    </dependency>

    <dependency>
      <groupId>uk.org.webcompere</groupId>
      <artifactId>system-stubs-jupiter</artifactId>
      <version>2.1.7</version>
    </dependency>

    <!-- test -->
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <version>${mockito.version}</version> <!-- Use the latest version -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.awaitility</groupId>
      <artifactId>awaitility</artifactId>
      <version>${awaitility.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.tomakehurst</groupId>
      <artifactId>wiremock</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.jayway.jsonpath</groupId>
          <artifactId>json-path</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-all</artifactId>
      <version>${hamcrest.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>${h2.version}</version>
      <scope>test</scope>
    </dependency>

    <!-- Test Containers -->
    <dependency>
      <groupId>io.appform.testcontainer</groupId>
      <artifactId>junit-testcontainer-aerospike</artifactId>
      <version>${junit.testcontainers.version}</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>com.aerospike</groupId>
          <artifactId>aerospike-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.appform.testcontainer</groupId>
      <artifactId>junit-testcontainer-rabbitmq</artifactId>
      <version>${junit.testcontainers.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.appform.testcontainer</groupId>
      <artifactId>junit-testcontainer-mariadb</artifactId>
      <version>${junit.testcontainers.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.phonepe.services</groupId>
      <artifactId>merchant-mandates-model</artifactId>
      <version>${merchant.mandate.model.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-simple</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>dropwizard-requestinfo-bundle</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.phonepe.growth</groupId>
          <artifactId>pegasus-model</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.phonepe</groupId>
      <artifactId>edc-model</artifactId>
      <version>1.0.71</version>
    </dependency>
    <dependency>
      <groupId>com.phonepe.payments</groupId>
      <artifactId>netpe-model</artifactId>
      <version>${netpe.model.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-jcs-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.phonepe.payments</groupId>
      <artifactId>ft-client-model</artifactId>
      <version>4.2.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.phonepe.platform</groupId>
      <artifactId>clockwork-models</artifactId>
      <version>3.1.1</version>
    </dependency>
    <dependency>
      <groupId>com.phonepe.services</groupId>
      <artifactId>refund-orchestrator-models</artifactId>
      <version>${ro.model.version}</version>
      <scope>compile</scope>
    </dependency>
<!--    validation pipeline-->
    <dependency>
      <artifactId>pipeline</artifactId>
      <groupId>com.phonepe.merchant.platform</groupId>
      <version>${pipeline.version}</version>
    </dependency>
    <dependency>
      <groupId>com.phonepe.cp</groupId>
      <artifactId>caishen-api</artifactId>
      <version>3.0.111</version>
    </dependency>
  </dependencies>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <version>2.7</version>
      </plugin>
    </plugins>
  </reporting>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven.compiler.version}</version>
        <configuration>
          <encoding>UTF-8</encoding>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <release>${maven.compiler.release}</release>
          <forceJavacCompilerUse>true</forceJavacCompilerUse>
          <generatedSourcesDirectory>${project.build.directory}/generated-sources/
          </generatedSourcesDirectory>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>7.2</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.8.2</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>${maven.shade.version}</version>
        <configuration>
          <createDependencyReducedPom>true</createDependencyReducedPom>
          <filters>
            <filter>
              <artifact>*:*</artifact>
              <excludes>
                <exclude>META-INF/*.SF</exclude>
                <exclude>META-INF/*.DSA</exclude>
                <exclude>META-INF/*.RSA</exclude>
              </excludes>
            </filter>
          </filters>
        </configuration>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <transformers>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                  <manifestEntries>
                    <Main-Class>com.phonepe.merchant.platform.stratos.server.StratosApplication
                    </Main-Class>
                    <Build-Number>${build.number}</Build-Number>
                    <Git-Commit>${build.revision}</Git-Commit>
                  </manifestEntries>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>7.2</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <!-- Needs to be removed, once issue is fixed: https://github.com/mojohaus/aspectj-maven-plugin/pull/45 -->
        <!--        <groupId>com.nickwongdev</groupId>-->
        <!--        <artifactId>aspectj-maven-plugin</artifactId>-->
        <!--        <version>1.12.1</version>-->
        <!-- https://mvnrepository.com/artifact/dev.aspectj/aspectj-maven-plugin -->
        <groupId>dev.aspectj</groupId>
        <artifactId>aspectj-maven-plugin</artifactId>
        <version>1.13</version>
        <dependencies>
          <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${aspectj.version}</version>
          </dependency>
          <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjtools</artifactId>
            <version>${aspectj.version}</version>
          </dependency>
        </dependencies>
        <configuration>
          <complianceLevel>${maven.compiler.target}</complianceLevel>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <showWeaveInfo>true</showWeaveInfo>
          <forceAjcCompile>true</forceAjcCompile>
          <sources />
          <weaveDirectories>
            <weaveDirectory>${project.build.directory}/classes</weaveDirectory>
          </weaveDirectories>
          <verbose>true</verbose>
          <Xlint>ignore</Xlint>
          <aspectLibraries>
            <aspectLibrary>
              <groupId>io.appform.functionmetrics</groupId>
              <artifactId>function-metrics</artifactId>
            </aspectLibrary>
          </aspectLibraries>
        </configuration>
        <executions>
          <execution>
            <phase>process-classes</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>local</id>
      <properties>
        <project.version>1.0.0</project.version>
        <build.number>0</build.number>
        <build.revision>local-dev</build.revision>
        <maven.deploy.skip>true</maven.deploy.skip>
      </properties>
    </profile>
    <profile>
      <id>docker</id>
      <activation>
        <property>
          <name>docker.build</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-deploy-plugin</artifactId>
            <configuration>
              <skip>true</skip>
            </configuration>
          </plugin>
          <plugin>
            <groupId>com.phonepe</groupId>
            <artifactId>docker-maven-plugin</artifactId>
            <version>0.2</version>
            <configuration>
              <images>
                <image>
                  <name>docker.phonepe.com/${project.artifactId}:${project.version}</name>
                  <build>
                    <contextDir>${project.basedir}</contextDir>
                    <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                    <args>
                      <VERSION>${project.version}</VERSION>
                    </args>
                  </build>
                </image>
              </images>
            </configuration>
            <executions>
              <execution>
                <id>build-image</id>
                <phase>package</phase>
                <goals><goal>build</goal></goals>
              </execution>
              <execution>
                <id>push-image</id>
                <phase>deploy</phase>
                <goals><goal>push</goal></goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>