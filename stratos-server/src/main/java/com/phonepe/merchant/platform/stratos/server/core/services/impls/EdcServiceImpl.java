package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.edc.enums.TransactionStatus;
import com.phonepe.edc.response.EdcTransactionDetailsResponse;
import com.phonepe.edc.response.TransactionGenericResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummaryRow;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.requests.EdcRowSignalContext;
import com.phonepe.merchant.platform.stratos.server.core.clients.EdcServiceClient;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;


@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class EdcServiceImpl implements EdcService {

    private final EdcServiceClient edcServiceClient;

    @Override
    public boolean isFullyReversed(String merchantTransactionId) {
        try {
            final EdcTransactionDetailsResponse response = edcServiceClient.getEdcTransactionDetails(
                merchantTransactionId);
            return !Objects.isNull(response.getReversalStates()) && Objects.equals(
                response.getReversalStates(), Constants.SUCCESS)
                && !Objects.isNull(
                response.getReversalAmounts()) && Objects.equals(
                response.getAmount(),
                response.getReversalAmounts());
        } catch (Exception e) {
            log.error("Error Occurred while checking fully reversed for EDC:", e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.EDC_TRANSACTION_DETAIL_ERROR, Map.of(
                Constants.MERCHANT_TRANSACTION_ID, merchantTransactionId, Constants.MESSAGE,
                e));
        }
    }

    @Override
    public void blockReversals(String merchantTransactionId) {
        final TransactionGenericResponse blockReversalsResponse = edcServiceClient.edcBlockReversals(
            merchantTransactionId);
        handleBlockUnblockReversalsResponse(merchantTransactionId, blockReversalsResponse);
    }

    @Override
    public Void unblockReversals(String merchantTransactionId) {
        final TransactionGenericResponse blockReversalsResponse = edcServiceClient.edcUnblockReversals(
            merchantTransactionId);
        handleBlockUnblockReversalsResponse(merchantTransactionId, blockReversalsResponse);
        return null;
    }

    public void handleBlockUnblockReversalsResponse(final String originalTransactionId,
        final TransactionGenericResponse blockReversalsResponse) {

        if (blockReversalsResponse.isSuccess()) {
            return;
        }
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.EDC_CLIENT_ERROR, Map.of(
                Constants.MESSAGE, "Error occurred while blocking/unblocking reversals",
                Constants.MERCHANT_TRANSACTION_ID, originalTransactionId));
    }

    @Override
    public EdcTransactionDetailsResponse getEdcTransactionDetails(final String tenant,
        final String merchantId, final String terminalId, final String rrn) {
        try {
            var response = edcServiceClient.getEdcTransactionDetails(tenant, merchantId,
                terminalId, rrn);
            if (response.getStatus().equals(TransactionStatus.SUCCESS)) {
                return response;
            } else {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSITION,
                    Map.of(Constants.MESSAGE, " Transaction status in EDC is not success" + response));
            }

        } catch (final Exception e) {
            log.error("Error Occurred while getting transaction details from EDC:", e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.EDC_TRANSACTION_DETAIL_ERROR,
                Map.of(Constants.TENANT, tenant, Constants.MERCHANT_ID, merchantId,
                    Constants.TERMINAL_ID, terminalId, Constants.RRN, rrn));
        }
    }

    @Override
    public EdcTransactionDetailsResponse getEdcTransactionDetailsFromTransactionId(
        String merchantTransactionId) {
        try {
            return edcServiceClient.getEdcTransactionDetails(
                merchantTransactionId);
        } catch (final Exception e) {
            log.error("Error Occurred while getting transaction details from EDC:", e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.EDC_TRANSACTION_DETAIL_ERROR,
                Map.of(Constants.MERCHANT_TRANSACTION_ID, merchantTransactionId));
        }

    }


    @Override
    public void enrichChargebackSummaryFromEdc(
        final ChargebackSummaryRow chargebackSummaryRow) {
        final EdcTransactionDetailsResponse transactionDetail = getEdcTransactionDetailsFromTransactionId(
            chargebackSummaryRow.getMerchantTransactionId());
        populateReversalDetailsForEDC(transactionDetail, chargebackSummaryRow);
        chargebackSummaryRow.setNetwork(transactionDetail.getCardNetwork().name());
        chargebackSummaryRow.setOriginalTransactionState(
            String.valueOf(transactionDetail.getStatus()));
        chargebackSummaryRow.setOriginalTransactionDate(transactionDetail.getTransactionDate());
        chargebackSummaryRow.setMerchantOrderId(transactionDetail.getMerchantOrderId());
        chargebackSummaryRow.setMerchantName(transactionDetail.getMerchantName());
    }
    private void populateReversalDetailsForEDC(
        final EdcTransactionDetailsResponse transactionDetail,
        final ChargebackSummaryRow chargebackSummaryRow) {
        chargebackSummaryRow.setReversalTransactionIds(transactionDetail.getReversalTransactionIds());
        chargebackSummaryRow.setReversalDates(
            String.valueOf(transactionDetail.getReversalDates()));
        chargebackSummaryRow.setReversalAmounts(transactionDetail.getReversalAmounts());
        chargebackSummaryRow.setReversalStates(transactionDetail.getReversalStates());
        chargebackSummaryRow.setReversalUtrIds(transactionDetail.getReversalRrn());
    }

    @Override
    public void validateDebitAmount(EdcRowSignalContext edcRowSignalContext, DisputeWorkflow disputeWorkflow){

        final BigDecimal netAmount = edcRowSignalContext.getChargebackAcceptedAmount();

            var transactionType = edcRowSignalContext.getTransactionType();

            if(transactionType.equals(RowTransactionType.CHARGEBACK) && (!ValidationUtils.isNetAmountEqualToDisputedAmount(disputeWorkflow.getDisputedAmount(), netAmount.longValue()))){
                    throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_DEBIT_AMOUNT,
                        Map.of(
                            "expectedDebitAmount", disputeWorkflow.getDisputedAmount(),
                            "actualDebitAmount", netAmount.longValue()));

            }
    }

}