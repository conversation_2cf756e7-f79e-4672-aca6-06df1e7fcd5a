package com.phonepe.merchant.platform.stratos.server.core.creator.dispute;

import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CreateDisputeResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.TransactionDetails;
import com.phonepe.merchant.platform.stratos.server.core.configs.TtlConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeIssuer;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStageVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StringUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Data
@Singleton
@Slf4j
public abstract class DisputeCreator<T extends CreateDisputeRequest> {

    private final DisputeService disputeService;
    private final IdHelper idHelper;
    private final Map<DisputeType, TtlConfig> ttlConfigMap;
    private final EventIngester eventIngester;

    public abstract TransactionDetails getTransactionDetails(String transactionId);

    protected DisputeCreator(DisputeService disputeService,
        IdHelper idHelper,
        Map<DisputeType, TtlConfig> ttlConfigMap, EventIngester eventIngester) {
        this.disputeService = disputeService;
        this.idHelper = idHelper;
        this.ttlConfigMap = ttlConfigMap;
        this.eventIngester = eventIngester;
    }

    private DisputeCategory getDisputeCategory(CreateDisputeRequest createDisputeRequest) {
        return DtoUtils.disputeCategoryDtoToCategory(
            DtoUtils.requestDisputeCategoryToDisputeCategoryDto(
                createDisputeRequest.getDisputeData()
                    .getDisputeCategory(), createDisputeRequest.getDisputeData().getDisputeType()));
    }

    protected Dispute createAndGetDispute(TransactionDetails transactionDetails,
        CreateDisputeRequest createDisputeRequest) {
        DisputeType disputeType = DtoUtils.transactionTypeToDisputeType(
            createDisputeRequest.getTransactionType(),
            createDisputeRequest.getDisputeData().getDisputeType());
        return DtoUtils.disputeDataStageToDisputeStage(
                createDisputeRequest.getDisputeData().getDisputeStage())
            .accept(new DisputeStageVisitor<>() {
                @Override
                public Dispute visitFirstLevel() {
                    return enrichDisputeDetails(transactionDetails, createDisputeRequest);
                }

                @Override
                public Dispute visitPreArbitration() {
                    final var transactionReferenceId = createDisputeRequest.getTransactionId();
                    final Dispute dispute = disputeService.getDispute(transactionReferenceId,
                        disputeType, DisputeStage.FIRST_LEVEL);
                    dispute.setCurrentDisputeStage(DisputeStage.PRE_ARBITRATION);
                    return dispute;
                }
            });
    }

    @SneakyThrows
    protected Dispute enrichDisputeDetails(final TransactionDetails transactionDetails,
        final CreateDisputeRequest createDisputeRequest) {

        return Dispute.builder().key(StorageUtils.primaryKey())
            .disputeType(DtoUtils.transactionTypeToDisputeType(
                createDisputeRequest.getTransactionType(),
                createDisputeRequest.getDisputeData()
                    .getDisputeType()))
            .disputeId(idHelper.disputeId(transactionDetails.getTransactionId()))
            .transactionReferenceId(transactionDetails.getTransactionId())
            .disputeReferenceId(
                StringUtils.join(transactionDetails.getRrn(),
                    transactionDetails.getInstrumentId()))
            .merchantId(transactionDetails.getMerchantId())
            .currentDisputeStage(DtoUtils.disputeDataStageToDisputeStage(
                createDisputeRequest.getDisputeData().getDisputeStage()))
            .instrumentTransactionId(transactionDetails.getInstrumentId())
            .transactionAmount(transactionDetails.getTransactionAmount())
            .rrn(transactionDetails.getRrn())
            .disputeIssuer(DisputeIssuer.PHONEPE_CUSTOMER)
            .disputeCategory(getDisputeCategory(createDisputeRequest))
            .build();
    }

    protected DisputeWorkflow buildDisputeWorkflow(String disputeId,
        TransactionDetails transactionDetails, CreateDisputeRequest createDisputeRequest,
        String userID) {

        DisputeType disputeType = DtoUtils.transactionTypeToDisputeType(
            createDisputeRequest.getTransactionType(),
            createDisputeRequest.getDisputeData().getDisputeType());
        return FinancialDisputeWorkflow.builder().key(StorageUtils.primaryKey())
            .disputeType(disputeType)
            .disputeSourceId("API").disputeSourceType(
                com.phonepe.merchant.platform.stratos.server.core.models.SourceType.API)
            .disputeWorkflowId(idHelper.disputeWorkflowId(transactionDetails.getTransactionId()))
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .transactionReferenceId(transactionDetails.getTransactionId()).respondBy(
                LocalDateTime.now().plusDays(
                    ttlConfigMap.get(disputeType).getDisputeStageTTLMap()
                        .get(
                            DtoUtils.disputeDataStageToDisputeStage(
                                createDisputeRequest.getDisputeData().getDisputeStage()))))
            .userType(UserType.SYSTEM)
            .gandalfUserId(userID)
            .disputeStage(DtoUtils.disputeDataStageToDisputeStage(
                createDisputeRequest.getDisputeData().getDisputeStage()))
            .currentState(DisputeWorkflowState.RECEIVED)
            .currentEvent(DisputeWorkflowEvent.CREATE_ENTRY).raisedAt(LocalDateTime.now())
            .disputedAmount(createDisputeRequest.getDisputedAmount()).disputeId(disputeId).build();
    }

    protected abstract void createAndPersistDisputeMeta(DisputeWorkflow disputeWorkflow,
        T createDisputeRequest);

    public CreateDisputeResponse createDispute(T createDisputeRequest,
        String userId) {

        TransactionDetails transactionDetails = this.getTransactionDetails(
            createDisputeRequest.getTransactionId());
        log.info("Transaction detail for transactionId {} is {}", createDisputeRequest.getTransactionId(), transactionDetails);
        final var dispute = createAndGetDispute(transactionDetails, createDisputeRequest);
        final var disputeWorkflow = buildDisputeWorkflow(dispute.getDisputeId(),
            transactionDetails, createDisputeRequest, userId);

        log.info("For transactionId {} dispute is {} and disputeWorkflow is {}.", createDisputeRequest.getTransactionId(), dispute, disputeWorkflow);
        disputeService.persistDisputeAndDisputeWorkflow(dispute, disputeWorkflow);
        createAndPersistDisputeMeta(disputeWorkflow, createDisputeRequest);

        eventIngester.generateEvent(
            FoxtrotEventUtils.toDisputeActionSuccessEvent(dispute,
                disputeWorkflow, DisputeWorkflowState.RECEIVED));

        return CreateDisputeResponse.builder()
            .disputeId(dispute.getDisputeId())
            .build();
    }
}