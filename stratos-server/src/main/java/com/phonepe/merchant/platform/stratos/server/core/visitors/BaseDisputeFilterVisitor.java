package com.phonepe.merchant.platform.stratos.server.core.visitors;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeCategoryDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeIssuerDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.*;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.SourceType;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

public abstract class BaseDisputeFilterVisitor  implements DisputeFilterVisitor<List<DisputeWorkflow>> {

    private final DisputeWorkflowRepository disputeWorkflowRepository;

    private final FileRepository fileRepository;

    private static final String DISPUTE_WORK_FLOW = "disputeWorkflow";
    private static final String DISPUTE_WORK_FLOW_DISPUTE = "disputeWorkflow.dispute";
    private static final String DISPUTE = "dispute";

    protected BaseDisputeFilterVisitor(final DisputeWorkflowRepository disputeWorkflowRepository,
        final FileRepository fileRepository) {
        this.disputeWorkflowRepository = disputeWorkflowRepository;
        this.fileRepository = fileRepository;
    }

    @Override
    public List<DisputeWorkflow> visit(final DateRangeFilter filter) {
        DetachedCriteria detachedCriteria =
            getDateRangeCriteria(DetachedCriteria.forClass(DisputeWorkflow.class, DISPUTE_WORK_FLOW)
                .createAlias(DISPUTE_WORK_FLOW_DISPUTE, DISPUTE), filter.getDateRange());

        addAliasBasedCriteriaForDisputeIssuers(detachedCriteria,
            filter.getDisputeIssuers());

        addAliasBasedCriteriaForDisputeCategories(detachedCriteria
            , filter.getDisputeCategories());

        addAliasBasedCriteriaForDisputeStages(
            detachedCriteria,
            filter.getDisputeStages());

        addAliasBasedCriteriaForDisputesTypes(detachedCriteria,
            filter.getDisputeTypes());

        return disputeWorkflowRepository.scatterGather(detachedCriteria);
    }

    @Override
    public List<DisputeWorkflow> visit(final TransactionReferenceIdFilter filter) {
        return disputeWorkflowRepository
            .scatterGather(getAliasedBaseCriteria()
                .add(Restrictions
                    .in(Fields.transactionReferenceId, filter.getTransactionReferenceIds())));
    }

    @Override
    public List<DisputeWorkflow> visit(final StateDateRangeFilter filter) {
        return disputeWorkflowRepository
            .scatterGather(getDateRangeCriteria(getAliasedBaseCriteria(
                filter.getDisputeTypes().stream().map(DtoUtils::disputeDtoToType).collect(
                    Collectors.toSet())), filter.getDateRange())
                .add(Restrictions.in(
                    Fields.currentState,
                    filter.getStates().stream()
                        .map(DisputeWorkflowState::valueOf)
                        .collect(Collectors.toList()))));
    }

    @Override
    public List<DisputeWorkflow> visit(final MerchantIdDateRangeFilter filter) {
        return disputeWorkflowRepository
            .scatterGather(getDateRangeCriteria(getAliasedBaseCriteria(), filter.getDateRange())
                .add(Restrictions.in("dispute.merchantId", filter.getMerchantIds())));
    }

    @Override
    public List<DisputeWorkflow> visit(final MerchantIdMerchantTransactionIdFilter filter) {
        return disputeWorkflowRepository
            .scatterGather(getAliasedBaseCriteria()
                .add(Restrictions.eq("dispute.merchantId", filter.getMerchantId()))
                .add(Restrictions
                    .eq("dispute.merchantTransactionId", filter.getMerchantTransactionId())));
    }

    @Override
    public List<DisputeWorkflow> visit(final FileNameDisputeFilter filter) {

        final var fileOptional = fileRepository.getFileByFileName(filter.getFileName());

        final var fileId = fileOptional.map(File::getFileId)
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.FILE_NOT_FOUND,
                Map.of(
                    Constants.MESSAGE, DisputeExceptionUtil.getMessage(StratosErrorCodeKey.FILE_NOT_FOUND,new HashMap<>()),
                    "fileName", filter.getFileName())));

        return disputeWorkflowRepository.scatterGather(getAliasedBaseCriteria()
            .add(Restrictions.eq(Fields.disputeSourceType, SourceType.FILE))
            .add(Restrictions.eq(Fields.disputeSourceId, fileId)));
    }

    @Override
    public List<DisputeWorkflow> visit(final CommunicationIdFilter filter) {
        return disputeWorkflowRepository.scatterGather(getAliasedBaseCriteria()
            .add(Restrictions.in(Fields.communicationId, filter.getCommunicationIds())));
    }

    @Override
    public List<DisputeWorkflow> visit(final InstrumentTransactionIdFilter filter) {
        return disputeWorkflowRepository.scatterGather(getAliasedBaseCriteria().add(
            Restrictions.in("dispute.instrumentTransactionId",
                filter.getInstrumentTransactionIds())));
    }

    private void addAliasBasedCriteriaForDisputesTypes(
        DetachedCriteria detachedCriteria,
        final Set<DisputeTypeDto> disputeTypes) {
        if (!Objects.isNull(disputeTypes) && !disputeTypes.isEmpty()) {
            detachedCriteria.add(Restrictions.in(Fields.disputeType,
                disputeTypes.stream().map(DtoUtils::disputeDtoToType).collect(
                    Collectors.toSet())));
        }
    }

    private void addAliasBasedCriteriaForDisputeStages(
        DetachedCriteria detachedCriteria,
        final Set<DisputeStageDto> disputeStages) {
        if (!Objects.isNull(disputeStages)  && !disputeStages.isEmpty()) {
            detachedCriteria.add(Restrictions.in(Fields.disputeStage,
                disputeStages.stream().map(DtoUtils::disputeStageDtoToDisputeStage).collect(
                    Collectors.toSet())));
        }

    }

    private void addAliasBasedCriteriaForDisputeCategories(
        final DetachedCriteria detachedCriteria,
        final Set<DisputeCategoryDto> disputeCategories) {
        if (!Objects.isNull(disputeCategories)  && !disputeCategories.isEmpty()) {
            detachedCriteria.add(Restrictions.in("dispute.disputeCategory",
                disputeCategories.stream()
                    .map(DtoUtils::disputeCategoryDtoToCategory).collect(
                        Collectors.toSet())));
        }
    }

    private void addAliasBasedCriteriaForDisputeIssuers(
        final DetachedCriteria detachedCriteria,
        final Set<DisputeIssuerDto> disputeIssuers) {
        if (!Objects.isNull(disputeIssuers) && !disputeIssuers.isEmpty()) {
            detachedCriteria.add(Restrictions.in("dispute.disputeIssuer",
                disputeIssuers.stream().map(DtoUtils::disputeIssuerDtoToIssuer)
                    .collect(
                        Collectors.toSet())));
        }
    }

    private DetachedCriteria getDateRangeCriteria(final DetachedCriteria detachedCriteria,
        final DateRange dateRange) {
        return detachedCriteria
            .add(Restrictions.between(Fields.raisedAt,
                dateRange.getStartDate().atStartOfDay(),
                dateRange.getEndDate().atTime(LocalTime.MAX)));
    }

    private DetachedCriteria getAliasedBaseCriteria() {
        return DetachedCriteria.forClass(DisputeWorkflow.class, DISPUTE_WORK_FLOW)
            .createAlias(DISPUTE_WORK_FLOW_DISPUTE, DISPUTE)
            .add(Restrictions.in(Fields.disputeType, getDisputeType()));
    }

    private DetachedCriteria getAliasedBaseCriteria(final Set<DisputeType> disputeTypes) {
        return DetachedCriteria.forClass(DisputeWorkflow.class, DISPUTE_WORK_FLOW)
            .createAlias(DISPUTE_WORK_FLOW_DISPUTE, DISPUTE)
            .add(Restrictions.in(Fields.disputeType, disputeTypes));
    }
    public abstract Set<DisputeType> getDisputeType();
}
