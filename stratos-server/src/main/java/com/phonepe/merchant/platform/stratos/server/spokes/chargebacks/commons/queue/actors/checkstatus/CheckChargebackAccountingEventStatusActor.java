package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.checkstatus;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.clients.PlutusTransactionStatusClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.AccountingEventStatus;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.utils.AccountingEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Set;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EqualsAndHashCode(callSuper = true)
public abstract class CheckChargebackAccountingEventStatusActor extends
    Actor<ActionType, DisputeWorkflowMessage> {

    private final PlutusTransactionStatusClient plutusTransactionStatusClient;

    private final DataProvider<StratosConfiguration> configDataProvider;

    private final EventIngester eventIngester;

    @SuppressWarnings("java:S107")
    protected CheckChargebackAccountingEventStatusActor(
        final ActionType actionType,
        final ActorConfig config,
        final ConnectionRegistry connectionRegistry,
        final ObjectMapper mapper,
        final RetryStrategyFactory retryStrategyFactory,
        final ExceptionHandlingFactory exceptionHandlingFactory,
        final Class<? extends DisputeWorkflowMessage> clazz,
        final Set<Class<?>> droppedExceptionTypes,
        final PlutusTransactionStatusClient plutusTransactionStatusClient,
        final DataProvider<StratosConfiguration> configDataProvider,
        final EventIngester eventIngester) {
        super(actionType, config, connectionRegistry, mapper, retryStrategyFactory,
            exceptionHandlingFactory, clazz, droppedExceptionTypes);
        this.plutusTransactionStatusClient = plutusTransactionStatusClient;
        this.configDataProvider = configDataProvider;
        this.eventIngester = eventIngester;
    }

    protected void checkAccountingEventStatusAndGenerateFoxtrotEvent(
        final DisputeWorkflow disputeWorkflow,
        final Dispute dispute,
        final EventGenerationType eventGenerationType,
        final long amount) {

        final var eventId = getEventId(disputeWorkflow, eventGenerationType, amount);

        final var eventType = AccountingEventUtils
            .toAccountingEventType(
                configDataProvider.getData(),
                disputeWorkflow.getDisputeType(),
                eventGenerationType);

        plutusTransactionStatusClient.checkAccountingEventStatus(eventId, eventType);

        final var foxtrotPenaltyRecoveryEvent = FoxtrotEventUtils
            .toChargebackAccountingEvent(
                dispute, disputeWorkflow,
                eventType, eventId,
                amount, AccountingEventStatus.ACCEPTED);
        eventIngester.generateEvent(foxtrotPenaltyRecoveryEvent);
    }
    protected abstract String getEventId(
            final DisputeWorkflow disputeWorkflow,
            final EventGenerationType eventGenerationType,
            final long amount);
}
