package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.server.core.clients.PlutusEventIngestionClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.AccountingEventStatus;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.checkstatus.impls.CheckHoldRecoveryAccountingEventStatusActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class RaiseHoldRecoveryAccountingEventAction extends UpdateDisputeStateBaseAction {
    private final DisputeMetadataRepository disputeMetadataRepository;

    private final PlutusEventIngestionClient plutusEventIngestionClient;

    private final Provider<CheckHoldRecoveryAccountingEventStatusActor> checkAccountingEventStatusActor;

    private final DisputeMetadataHelper disputeMetadataHelper;

    @Inject
    @SuppressWarnings("java:S107")
    public RaiseHoldRecoveryAccountingEventAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final EventIngester eventIngester,
            final DisputeMetadataRepository disputeMetadataRepository,
            final PlutusEventIngestionClient plutusEventIngestionClient,
            final Provider<CheckHoldRecoveryAccountingEventStatusActor> checkAccountingEventStatusActor,
            final DisputeMetadataHelper disputeMetadataHelper,
            final CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.disputeMetadataRepository = disputeMetadataRepository;
        this.plutusEventIngestionClient = plutusEventIngestionClient;
        this.checkAccountingEventStatusActor = checkAccountingEventStatusActor;
        this.disputeMetadataHelper = disputeMetadataHelper;
    }

    @Override
    protected void transition(final DisputeWorkflow disputeWorkflow,
                              final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var extendedState = stateContext.getExtendedState();

        final var recoveryAccountingEvent = extendedState
                .get(Constants.HOLD_RECOVERY_EVENT, AccountingEvent.class);
        Objects.requireNonNull(recoveryAccountingEvent);

        plutusEventIngestionClient.sendAccountingEvent(recoveryAccountingEvent);

        final var recoveryAccountingEventDisputeMetadata = disputeMetadataHelper
                .toAccountingEventDisputeMetadata(disputeWorkflow, recoveryAccountingEvent);

        disputeMetadataRepository.save(recoveryAccountingEventDisputeMetadata);

        final var foxtrotRecoveryEvent = FoxtrotEventUtils
                .toChargebackAccountingEvent(disputeWorkflow.getDispute(), disputeWorkflow,
                        recoveryAccountingEvent, AccountingEventStatus.RAISED);
        eventIngester.generateEvent(foxtrotRecoveryEvent);
    }

    @Override
    @SneakyThrows
    protected void postTransition(final DisputeWorkflow disputeWorkflow) {
        checkAccountingEventStatusActor.get().publishWithDelay(DisputeWorkflowMessage.builder()
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .build(), 5_000L);
    }
}
