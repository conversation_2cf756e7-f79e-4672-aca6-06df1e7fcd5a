package com.phonepe.merchant.platform.stratos.server.core.services;

import com.phonepe.kratos.base.ChargeBackAcceptanceCheck;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.ContestPayload;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DisputeFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeDetails;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeSummaries;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CheckStatusRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DownloadReportRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.UpdateCommunicationIdRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CheckStatusResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CreateDisputeResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.RefundEligibilityResponse;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.DisbursementDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.EnumClass;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.DisputeWorkflowUpdateRequest;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;

import javax.ws.rs.core.Response;


public interface DisputeService {

    Set<DisputeWorkflowEvent> getAuthorizedUpcomingEvents(
        final UserAuthDetails userAuthDetails,
        final DisputeType disputeType, final DisputeStage disputeStage,
        final DisputeWorkflowVersion disputeWorkflowVersion,
        final DisputeWorkflowState disputeWorkflowState);

    StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> triggerEvent(
        final UserAuthDetails userAuthDetails,
        final String transactionReferenceId, final String disputeWorkflowId,
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final TransitionContext requestTransitionContext);

    StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> triggerEvent(
        final UserAuthDetails userAuthDetails,
        final String transactionReferenceId, final String disputeWorkflowId,
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final Map<Object, Object> requestTransitionContext);

    StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> createEntry(
        final Dispute dispute, final DisputeWorkflow disputeWorkflow);

    DisputeWorkflow validateAndGetDisputeWorkflow(
        final String transactionReferenceId,
        final String disputeWorkflowId);

    String getStateMachineSvgGraph(
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final DisputeWorkflowVersion disputeWorkflowVersion);

    DisputeWorkflow getDisputeWorkflow(
        final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage disputeStage);

    List<DisputeWorkflow> getDisputeWorkflows(
        final DisputeStateMachineRegistryKey disputeStateMachineRegistryKey,
        final Set<DisputeWorkflowState> disputeWorkflowStates,
        final DateRange dateRange);

    Dispute getDispute(
        final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage disputeStage);

    Optional<Dispute> getDisputeOptional(
        final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage disputeStage);

    void updateCommunicationIdInDisputeWorkflow(
        final UpdateCommunicationIdRequest updateCommunicationIdRequest,
        final String gandalfUserId, UserType userType);

    Dispute getDispute(
        final String transactionReferenceId,
        final DisputeType disputeType);

    Optional<DisputeWorkflow> getDisputeWorkflowOptional(
        final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage disputeStage);

    CheckStatusResponse checkStatus(CheckStatusRequest checkStatusRequest);

    List<DisputeWorkflow> getAllDisputeWorkflows(final String transactionReferenceId,
        final DisputeType disputeType, final DisputeStage disputeStage);

    DisputeWorkflow getDisputeWorkflow(final String disputeWorkflowId,
        final DisputeType disputeType);

    Optional<DisputeMetadata> getDisputeMetadata(final String transactionId,
        final String disputeWorkflowId, final
    DisputeMetadataType disputeMetadataType);

    Optional<DisbursementDisputeMetadata> getDisbursementMetadata(final String disbursementId);

    List<DisputeMetadata> getDisputeMetadata(String disputeWorkflowId, Set<DisputeMetadataType> types);

    Map<String, Map<Integer,String>> getAllEnumsByEnumClassName(List<EnumClass> enumClass);

    Response updateDisputeWorkflows(DisputeWorkflowUpdateRequest updateRequest);

    byte[]  download(DownloadReportRequest downloadReportRequest);
    void creditDisputeAction(final DisputeWorkflow disputeWorkflow, final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext);
    void debitDisputeAction(final DisputeWorkflow disputeWorkflow, final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext);
    void fraudCheck(DisputeWorkflow disputeWorkflow);
    void fraudSilentCheck(DisputeWorkflow disputeWorkflow);
    RefundEligibilityResponse getRefundEligibility(final String transactionReferenceId);

    void reconcile(DisputeReconcileRequest reconcileRequest);

    DisputeSummaries getDisputes(DisputeFilterParams disputeFilterParams);

    DisputeDetails getDisputeDetails(String disputeId, String merchantId);

    ChargeBackAcceptanceCheck buildChargebackPayload(DisputeWorkflow disputeWorkflow);

    DisputeDetails acceptDispute(String disputeId, String merchantId);
    DisputeWorkflow getDisputeWorkflow(final String disputeWorkflowId);
    CreateDisputeResponse createDispute(CreateDisputeRequest createDisputeRequest,
        String userID);
    void persistDisputeAndDisputeWorkflow(Dispute dispute, DisputeWorkflow disputeWorkflow);

    DisputeDetails contest(ContestPayload payload);
    DisputeDetails validateAndGetDispute(String disputeId, String merchantId, DisputeStatus status);
    boolean isChargebackRecoveryDone(String disputeWorkflowId);
    StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> triggerNpciAck(final Dispute dispute, final DisputeWorkflow disputeWorkflow);
    void acquireDisputeCreationLock(String transactionId, TransactionType transactionType,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType disputeType,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage disputeStage);
    void releaseDisputeCreationLock(String transactionId, TransactionType transactionType,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType disputeType,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage disputeStage);
}