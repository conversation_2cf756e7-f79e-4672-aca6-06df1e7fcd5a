package com.phonepe.merchant.platform.stratos.server.core.models;

public enum WardenWorkflowType {
    CHARGEBACK_FILE_UPLOAD {
        @Override
        public <T> T accept(WardenWorkflowTypeVisitor<T> visitor) {
            return visitor.visitChargebackFileUpload();
        }
    },
    PENALTY_CLASS_CREATION {
        @Override
        public <T> T accept(WardenWorkflowTypeVisitor<T> visitor) {
            return visitor.visitPenaltyClassCreation();
        }
    };

    public abstract <T> T accept(WardenWorkflowTypeVisitor<T> visitor);

}
