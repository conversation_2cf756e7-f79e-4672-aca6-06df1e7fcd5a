package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DisputeFilterParams;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.P2pmToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStageVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.EnumClass;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.common.enums.PaymentState;
import com.phonepe.models.payments.common.PaymentFlags.OptionFlag;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.instrument.PaymentInstrument;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentType;
import com.phonepe.models.payments.pay.view.ProcessingRail;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

@UtilityClass
@Slf4j
public class ValidationUtils {

    public void validateEntryDisputeAmount(final DisputeWorkflow disputeWorkflow,
        final Dispute dispute,
        final DisputeService disputeService) {

        dispute.getCurrentDisputeStage().accept(new DisputeStageVisitor<Void>() {
            @Override
            public Void visitFirstLevel() {
                final var originalTxnAmount = dispute.getTransactionAmount();
                final var disputeAmount = disputeWorkflow.getDisputedAmount();
                if (disputeAmount > originalTxnAmount) {
                    throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT, Map.of(
                            Constants.MESSAGE,  DisputeExceptionUtil.getMessage(StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT,new HashMap<>()),
                            "transactionReferenceId", disputeWorkflow.getTransactionReferenceId(),
                            "originalTxnAmount", originalTxnAmount,
                            "disputedAmount", disputeAmount)
                        );
                }
                return null;
            }

            @Override
            public Void visitPreArbitration() {
                long acceptedAmount = 0;
                try {
                    acceptedAmount = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
                            disputeService.getDisputeWorkflow(
                                dispute.getTransactionReferenceId(),
                                dispute.getDisputeType(), DisputeStage.FIRST_LEVEL))
                        .getAcceptedAmount();
                } catch (final DisputeException e) {
                    // Temporary Measure to store PreArbs for which First-Level Not processed via Stratos.
                    if (StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND != e.getErrorCode()) {
                        throw e;
                    }
                    log.info("First Level DisputeWorkflow not found for {}",
                        dispute.getTransactionReferenceId());
                }

                final var originalTxnAmount = dispute.getTransactionAmount();
                if (disputeWorkflow.getDisputedAmount() >
                    (originalTxnAmount - acceptedAmount)) {
                    throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT, Map.of(
                        Constants.MESSAGE,  DisputeExceptionUtil.getMessage(StratosErrorCodeKey.INVALID_DISPUTE_AMOUNT,new HashMap<>()),
                        "transactionReferenceId", disputeWorkflow.getTransactionReferenceId(),
                        "originalTxnAmount", originalTxnAmount,
                        "acceptedAmount", acceptedAmount,
                        "disputedAmount", disputeWorkflow.getDisputedAmount())
                    );
                }
                return null;
            }
        });
    }

    // Will change to instrument specific validation on addition of other chargebacks.
    public void validateAmount(final SentPayment sentPayment, final String instrumentTxnId,
        final long disputeAmount, final DisputeType disputeType) {

        final var upiList = sentPayment.getPaidFrom()
            .stream()
            .filter(paymentInstrument -> Constants.CHARGEBACK_INSTRUMENT_MAP.get(
                disputeType).contains(
                paymentInstrument.getType()))
            .collect(Collectors.toList());

        if (upiList.isEmpty()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION,
                Map.of(Constants.MESSAGE, "No txn for upiTxnId " + instrumentTxnId)
            );
        }

        final Optional<PaymentInstrument> upiPaymentInstrument = upiList.stream()
            .filter(paymentInstrument -> disputeAmount == paymentInstrument.getAmount())
            .findFirst();

        if(upiPaymentInstrument.isEmpty()){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION_AMOUNT,
                    Map.of(Constants.MESSAGE,
                            "No txn for upiTxnId " + instrumentTxnId + " amount " + disputeAmount));
        }

    }

    public void validateDisputeId(final DisputeWorkflow disputeWorkflow, final Dispute dispute) {
        if (!Objects.equals(dispute.getDisputeId(), disputeWorkflow.getDisputeId())) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                Map.of(Constants.MESSAGE, "Invalid dispute id in workflow and dispute",
                    Constants.TRANSACTION_ID, disputeWorkflow.getTransactionReferenceId()));
        }
    }

    public boolean isP2pmToaFailedMaxRetryReached(long retryCount, P2pmToaConfig p2PMToaConfig){
        return  retryCount >= p2PMToaConfig.getP2pmToaFailedMaxRetry();
    }

    public boolean isPostEntryHandlerQueueMaxRetryReached(long retryCount, P2pmToaConfig p2PMToaConfig){
        return  retryCount >= p2PMToaConfig.getPostEntryHandlerQueueMaxRetry();
    }

    public boolean isPayStatusCheckMaxRetryReached(long retryCount, P2pmToaConfig p2PMToaConfig){
        return  retryCount >= p2PMToaConfig.getPayStatusCheckMaxRetry();
    }

    public <T> void validateEquals(final T o1, final T o2) {
        if (!Objects.equals(o1, o2)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_DATA_MISMATCH, Map.of());
        }
    }

    public void validateRetryableP2pmToaState(DisputeWorkflowState currentState){
        if(!Constants.P2PM_TOA_RETRY_STATES.contains(currentState)){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.TOA_RETRY_NOT_SUPPORTED,
                Map.of(Constants.MESSAGE, String.format("P2PM Toa Retry is not supported for this state. Supported states are %s",
                        String.join(",",Constants.P2PM_TOA_RETRY_STATES.stream().map(Enum::name).toList())),
                    Fields.currentState, currentState
                ));
        }
    }

    public void validateExternallyCompletableP2pmToaState(DisputeWorkflowState currentState){
        if(!Constants.P2PM_TOA_COMPLETE_EXTERNALLY_STATES.contains(currentState)){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.TOA_COMPLETE_EXTERNALLY_NOT_SUPPORTED,
                Map.of(Constants.MESSAGE, String.format("P2PM Toa complete externally is not supported for this state. Supported states are %s",
                        String.join(",",Constants.P2PM_TOA_COMPLETE_EXTERNALLY_STATES.stream().map(Enum::name).toList())),
                    Fields.currentState, currentState
                ));
        }
    }

    public void validateExternallyCompletableToaState(final DisputeWorkflow disputeWorkflow){
        final var toaCompleteExternallyStates = Constants.TOA_COMPLETE_EXTERNALLY_STATES;
        final DisputeWorkflowState currentState = disputeWorkflow.getCurrentState();
        if(!toaCompleteExternallyStates.contains(currentState)){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.TOA_COMPLETE_EXTERNALLY_NOT_SUPPORTED,
                    Map.of(Constants.MESSAGE, String.format("Toa complete externally is not supported for this state. Supported states are %s",
                                    String.join(",",toaCompleteExternallyStates.stream().map(Enum::name).toList())),
                            Fields.currentState, currentState
                    ));
        }
    }

    public void validateP2pmToaTransactionBeforeEntry(TransactionDetail transactionDetail){
        if(transactionDetail.getSentPayment().getPaymentState() != PaymentState.COMPLETED){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TOA, Map.of(Constants.MESSAGE, "Transaction is not in completed status, status = "+ transactionDetail.getSentPayment().getPaymentState().name()));
        }

        if(!transactionDetail.getSentPayment().getPaymentFlags().contains(OptionFlag.DEEMED_FAILURE)){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TOA, Map.of(Constants.MESSAGE, "Transaction does not have DEEMED_FAILURE flag set"));
        }
    }

    public void validateEnumList(List<EnumClass> enumClassList) {
        if (enumClassList.contains(null)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.ENUM_CLASS_NULL,
                    Map.of(Constants.ENUM_CLASS_LIST, enumClassList));
        }
    }

    public boolean isJsonNodeEmpty(JsonNode jsonNode) {
        return null == jsonNode || jsonNode.isNull() || jsonNode.isMissingNode();
    }

    public void validateDisputeFilterParams(DisputeFilterParams params){
        if(params.getDateRange()!=null &&
            params.getDateRange().getStartDate().isAfter(
            params.getDateRange().getEndDate())){
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_END_DATE, Map.of(
                "startDate", params.getDateRange().getStartDate(),
                "endDate", params.getDateRange().getEndDate()));
        }
        if(params.getLimit() < 0 || params.getOffset() < 0)
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_PAGINATION_PARAMETERS,
                Collections.emptyMap());
    }


    public static void validateMerchantId(String userAuthToken, String merchantId) {
        if(!getMd5Hash(merchantId).equals(userAuthToken)){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_MERCHANT_ID,
                Map.of(Constants.MESSAGE, "Merchant ID does not match the passed auth key."));
        }
    }
    @SuppressWarnings("java:S4790")
    private String getMd5Hash(String token) {
        return DigestUtils.md5Hex(token);
    }

    public static boolean isNetAmountEqualToDisputedAmount(long disputeAmount, long netAmount){
        return disputeAmount == netAmount;
    }

    public static PaymentInstrument validateWalletExternalMerchantTransaction(TransactionDetail transactionDetail, ProcessingRail rail){
        return transactionDetail.getSentPayment().getPaidFrom()
            .stream()
            .filter(paymentInstrument -> PaymentInstrumentType.WALLET.equals(paymentInstrument.getType()))
            .filter(paymentInstrument -> rail.equals(paymentInstrument.getProcessingRail()))
            .filter(paymentInstrument ->(!Constants.P2P_MCC.equals(transactionDetail.getSentPayment().getTo().get(0).getMcc())))
            .filter(paymentInstrument -> Constants.EXTERNAL_MERCHANT_PAYMENT_FLOW.equals(transactionDetail.getFlow()))
            .findFirst()
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.NON_EXTERNAL_MERCHANT_TRANSACTION,
                Map.of(Constants.MESSAGE, "Unsupported transaction: Invalid PaymentInstrument or MCC")));
    }

    public void validateExternalRefundTransactionIdForWallet(final TransactionDetail transactionDetails){
        if(!transactionDetails.getSentPayment().getPaymentState().equals(PaymentState.COMPLETED)){
            log.error("Transaction detail for validation external refund transaction id for wallet form payment : {} is not is completed state ", transactionDetails);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.VALIDATION_FAILURE, Map.of(Constants.MESSAGE,
                String.format("Provided refund transaction is yet to complete, provided txn id: %s",
                    transactionDetails.getSentPayment().getTransactionId())));
        }

    }

    public static PaymentInstrument validateWalletExternalMerchantTransactionForWalletCbs(TransactionDetail transactionDetail, ProcessingRail rail){
        return transactionDetail.getSentPayment().getPaidFrom()
            .stream()
            .filter(paymentInstrument -> PaymentInstrumentType.WALLET.equals(paymentInstrument.getType()))
            .filter(paymentInstrument -> rail.equals(paymentInstrument.getProcessingRail()))
            .findFirst()
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.VALIDATION_FAILURE,
                Map.of(Constants.MESSAGE, "Unsupported transaction: Invalid PaymentInstrumentType or rail")));
    }

    public boolean validateNpciDisputeAmount(final DisputeWorkflow disputeWorkflow,
        final DisputeWorkflow storedDisputeWorkflow) {
        return disputeWorkflow.getDisputedAmount() == storedDisputeWorkflow.getDisputedAmount();
    }

}
