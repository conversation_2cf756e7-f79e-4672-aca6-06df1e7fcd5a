package com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class InternalMIDWorkflowConfigurator implements WorkflowConfigurator<DisputeWorkflowState, DisputeWorkflowEvent> {

    private final UpdateDisputeStateAction updateDisputeStateAction;

    @Override
    @SneakyThrows
    public void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED)
                .event(DisputeWorkflowEvent.INTERNAL_MID_REQUEST_REPRESENTMENT)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED)
                .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
                .action(updateDisputeStateAction)
                .and();
    }
}
