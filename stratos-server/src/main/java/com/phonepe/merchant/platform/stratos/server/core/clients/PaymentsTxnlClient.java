package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.PaymentsTxnlClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.ErrorUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.common.enums.CurrencyCode;
import com.phonepe.models.common.enums.ResponseCode;
import com.phonepe.models.common.transaction.impl.PhonePeOriginalTransaction;
import com.phonepe.models.payments.pay.PaymentProcessorResult;
import com.phonepe.models.payments.pay.PaymentRequest;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.context.impl.PeerToPeerMerchantTOAPaymentContext;
import com.phonepe.models.payments.pay.destination.impl.MerchantDestination;
import com.phonepe.models.payments.pay.source.impl.MerchantSource;
import com.phonepe.models.payments.upi.UpiTransactionDetailRequest;
import com.phonepe.models.payments.upi.udircomplaint.UdirOutgoingComplaintRequest;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.models.response.PaymentResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.SerializableHttpData;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class PaymentsTxnlClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private static final String PP_MERCHANT_ID = "FXM";
    private final OlympusIMClient olympusIMClient;
    private static final String PAY_V1 = "/v1/transactions/pay";
    private static final String PAY_V2 = "/v2/transactions/toa/pay";

    @Inject
    public PaymentsTxnlClient(
        @PaymentsTxnlClientConfig final HttpConfiguration httpConfiguration,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            PaymentsTxnlClient.class, httpConfiguration,
            serviceEndpointProviderFactory, mapper, metricRegistry);
        this.olympusIMClient = olympusIMClient;
    }

    public GenericResponse<TransactionDetail> getTransactionDetails(
        final String originalTransactionId) {

        final var url = String
            .format("/v2/transactions/transaction/%s/detail", originalTransactionId);

        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory, "getTransactionDetails", url,
            TypeReferences.TRANSACTION_DETAILS,
            olympusIMClient);
    }

    public GenericResponse<List<String>> getRgcsReversalTransactions(
        final String originalTransactionId) {

        final var url = String
            .format("/v1/chargeback/all/rgcs/reversals/originalId/%s", originalTransactionId);

        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory, "getRgcsReversalTransactions", url,
            TypeReferences.LIST_OF_STRING,
            olympusIMClient);
    }

    public GenericResponse<ResponseCode> blockReversals(
        final String originalTransactionId) {

        final var url = String
            .format("/v1/chargeback/update/reversal/flag/%s/block", originalTransactionId);

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "blockReversals", url, Constants.EMPTY_HTTP_DATA,
            TypeReferences.RESPONSE_CODE,
            olympusIMClient);
    }

    public GenericResponse<ResponseCode> unblockReversals(
        final String originalTransactionId) {

        final var url = String
            .format("/v1/chargeback/update/reversal/flag/%s/unblock", originalTransactionId);

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "unblockReversals", url, Constants.EMPTY_HTTP_DATA,
            TypeReferences.RESPONSE_CODE,
            olympusIMClient);
    }

    public GenericResponse<String> getPaymentsIdFromUpiTxnId(
        final String upiTxnId, final String utr, final LocalDateTime txnDate) {

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "paymentsIdFromUpiId",
            "/v1/upi/upiTransaction/detail",
            SerializableHttpData.builder()
                .data(UpiTransactionDetailRequest.builder()
                    .requestDate(Date.from(txnDate.atZone(ZoneId.systemDefault()).toInstant()))
                    .upiTransactionId(upiTxnId)
                    .utr(utr)
                    .build())
                .mediaType(MediaType.APPLICATION_JSON)
                .build(),
            TypeReferences.STRING,
            olympusIMClient);
    }


    public void raiseUdirComplaint(
        UdirOutgoingComplaintRequest udirOutgoingComplaintRequest) {
        final var url = "/v1/udir/raise/outgoing/complaint";

        HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "raiseUdirComplaint", url, new SerializableHttpData(
                MediaType.APPLICATION_JSON, udirOutgoingComplaintRequest),
            String.class, ErrorUtils.exceptionConsumerWithHystrixTimeoutHandler(),
            olympusIMClient);
    }

    public GenericResponse<PaymentResponse>  getMerchantTransactionStatus(
        final String merchantId, final String merchantTransactionId){

        final var url = String
            .format("/v1/merchants/%s/%s/status?details=false", merchantId, merchantTransactionId);

        return HttpClientUtils.executeGet(
            httpExecutorBuilderFactory, "getMerchantTransactionStatus", url,
            TypeReferences.PAYMENT_RESPONSE, olympusIMClient);

    }

    private GenericResponse<PaymentProcessorResult> initiatePay(PaymentRequest paymentRequest,
        String authId, String authSubject, String url) {

        try{
            return HttpClientUtils.executePost(
                    httpExecutorBuilderFactory, "initiatePay", url,
                    new SerializableHttpData(MediaType.APPLICATION_JSON, paymentRequest),
                    TypeReferences.PAYMENT_PROCESSOR_RESULT,
                    List.of(
                            HeaderPair.builder()
                                    .name(Constants.AUTHORIZATION_FOR_ID)
                                    .value(authId)
                                    .build(),
                            HeaderPair.builder()
                                    .name(Constants.AUTHORIZATION_FOR_SUBJECT)
                                    .value(authSubject)
                                    .build(),
                            HeaderPair.builder()
                                    .name(Constants.DEVICE_FINGERPRINT)
                                    .value("STRATOS-SERVER")
                                    .build()
                    ),
                    ErrorUtils.exceptionConsumerWithHystrixTimeoutHandler()
            );
        }catch (Exception exception){
            log.error("Exception in initiatePay for paymentRequest: {}, authId: {}, authSubject: {}",
                paymentRequest, authId, authSubject, exception);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENTS_CLIENT_ERROR,"Error initiating toa payment");
        }


    }

    public GenericResponse<PaymentProcessorResult> createP2pmTransaction(
        final long amount, final String merchantId, final String merchantOrderId,
        final String originalTxnId) {

        return initiatePay(PaymentRequest.builder()
                .from(Collections.singletonList(
                    MerchantSource.builder()
                        .amount(amount)
                        .merchantId(PP_MERCHANT_ID)
                        .build()
                ))
                .to(Collections.singletonList(
                    MerchantDestination.builder()
                        .amount(amount)
                        .merchantId(merchantId)
                        .build()
                ))
                .currencyCode(CurrencyCode.INR)
                .context(PeerToPeerMerchantTOAPaymentContext.builder()
                    .merchantOrderId(merchantOrderId)
                    .merchantTransactionId(merchantOrderId)
                    .originalTransaction(PhonePeOriginalTransaction.builder()
                        .transactionId(originalTxnId)
                        .build())
                    .message(
                        "Payment for P2P Merchant deemed to failed transaction, processed by stratos.")
                    .build())
                .build(),
            PP_MERCHANT_ID, PP_MERCHANT_ID, PAY_V1);
    }

    public GenericResponse<PaymentProcessorResult> initToaTransaction(PaymentRequest paymentRequest, String authId, String authString) {
        return initiatePay(paymentRequest, authId, authString, PAY_V2);
    }
}
