package com.phonepe.merchant.platform.stratos.server.v2.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class ResetAction extends InitiateMerchantCommunicationAction {


    @Inject
    @SuppressWarnings("java:S107")
    public ResetAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final EventIngester eventIngester,
            final CallbackActor callbackActor,
            final Provider<StateChangeHandlerActor> stateChangeHandlerActorProvider) {
        super(disputeService, disputeWorkflowRepository,
            eventIngester, callbackActor, stateChangeHandlerActorProvider);
    }

    @Override
    protected void preTransition(final DisputeWorkflow disputeWorkflow,
                                 final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {
        final var financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
                disputeWorkflow);

        if (financialDisputeWorkflow.getCurrentState().equals(DisputeWorkflowState.HOLD) &&
            financialDisputeWorkflow.getDisputedAmount() > 0) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.RESET_NOT_ALLOWED, Map.of(
                    Constants.MESSAGE, "Recovery is already done from merchant"));
        }
    }
}