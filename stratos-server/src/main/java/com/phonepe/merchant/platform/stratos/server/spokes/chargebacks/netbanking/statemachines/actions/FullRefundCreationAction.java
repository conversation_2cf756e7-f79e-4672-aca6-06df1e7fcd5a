package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import org.springframework.statemachine.StateContext;

public class FullRefundCreationAction extends RefundCreationAction {

    @Inject
    public FullRefundCreationAction(
        DisputeService disputeService,
        DisputeWorkflowRepository disputeWorkflowRepository,
        EventIngester eventIngester,
        PaymentsService paymentsService,
        Provider<StateChangeHandlerActor> stateChangeHandlerProvider,
        CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, paymentsService,
            stateChangeHandlerProvider, callbackActor);
    }

    @Override
    protected void transition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.
            getFinancialDisputeWorkflow(disputeWorkflow);

        financialDisputeWorkflow.setAcceptedAmount(disputeWorkflow.getDisputedAmount());

        super.transition(disputeWorkflow, stateContext);
    }

}
