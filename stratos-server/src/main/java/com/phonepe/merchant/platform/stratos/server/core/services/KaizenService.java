package com.phonepe.merchant.platform.stratos.server.core.services;

import com.phonepe.stratos.kaizen.models.data.ActionStatus;
import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface KaizenService {
    Optional<StoredActionMetadata> uploadEvidence(String disputeWorkflowId, String documentId, Map<String, Object> metadata);
    List<StoredDocumentUploadWithMetaDataActionMetadata> getAllEvidences(String kaizenWorkflowId);
    List<StoredDocumentUploadWithMetaDataActionMetadata> getAllEvidences(String kaizenWorkflowId, ActionStatus actionStatus);
    Optional<StoredActionMetadata> deleteEvidence(String actionId);
}