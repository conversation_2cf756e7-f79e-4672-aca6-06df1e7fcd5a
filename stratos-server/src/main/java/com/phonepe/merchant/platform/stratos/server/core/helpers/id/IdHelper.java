package com.phonepe.merchant.platform.stratos.server.core.helpers.id;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.zeus.models.Farm;
import com.phonepe.zeus.models.utils.FarmEnvUtils;
import io.appform.dropwizard.sharding.utils.ShardCalculator;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class IdHelper {
    private static final int ZONE_ID = Farm.getFarm(FarmEnvUtils.envFarm()).intZoneId();
    private static final String DISPUTE_METADATA_PREFIX = "DM";
    private static final String DISPUTE_WORKFLOW_PREFIX = "DW";
    private static final String DISPUTE_PREFIX = "D";

    private final ShardCalculator<String> shardCalculator;

    public String disputeWorkflowId(final String transactionReferenceId) {
        return sameShardId(transactionReferenceId, DISPUTE_WORKFLOW_PREFIX);
    }

    public String disputeId(final String transactionReferenceId) {
        return sameShardId(transactionReferenceId, DISPUTE_PREFIX);
    }

    public String disputeMetaDataId(final String transactionReferenceId) {
        return sameShardId(transactionReferenceId, DISPUTE_METADATA_PREFIX);
    }

    public String merchantOrderId(long retryCount, String transactionId){
        return String.format("DEEM%d-%s", retryCount, transactionId);
    }

    public String merchantTransactionId(final String transactionReferenceId) {
        return sameShardId(transactionReferenceId, "M");
    }

    public String createMerchantRefundId(long retryCount, String transactionId) {
        return String.format("CB%s-%d",transactionId, retryCount);
    }

    public String createMerchantRefundIdForExternalMerchant(String transactionId, String disputeStageId) {
        return String.format("CB%s-%s",transactionId, disputeStageId);
    }

    private String sameShardId(final String transactionReferenceId, final String prefix) {
        return IdGenerator.generateWithConstraints(prefix, List.of(
                //validation to have generated farm based id and transRefId in same shard
                id -> shardCalculator.shardId(transactionReferenceId) == shardCalculator.shardId(
            farmBasedId(id.getId())),
                //validation to ensure final id does not exceed 32 characters
                id -> id.getId().length()<=28))
            .map(id ->farmBasedId(id.getId()))
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.ID_GENERATION_FAILED, Map.of(
                Constants.TRANSACTION_ID, transactionReferenceId)));
    }
    private String farmBasedId(final String id) {
        return id + "_"+ ZONE_ID;
    }

    public String toaOrderId(long retryCount, String transactionId, String prefix) {
        return String.format(String.format("%s-%d-%s", prefix, retryCount, transactionId));
    }
}
