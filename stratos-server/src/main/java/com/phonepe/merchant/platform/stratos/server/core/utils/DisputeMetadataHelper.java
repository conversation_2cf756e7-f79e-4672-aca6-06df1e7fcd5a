package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalCreditTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalDebitTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.TOAContext;
import com.phonepe.merchant.platform.stratos.models.disputes.toa.ToaTransactionMetadata;
import com.phonepe.merchant.platform.stratos.models.udir.UdirNpciResponseModel;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.PrimaryKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.AccountingEventDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.CommentDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.FraudActionDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.InstitutionalCreditDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.InstitutionalDebitDisputeMetaData;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.NetBankingDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.UdirDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.ToaDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.WalletDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.CommentType;
import com.phonepe.merchant.platform.stratos.server.core.models.SourceType;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import com.phonepe.ruleengine.model.integration.FraudAction;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class DisputeMetadataHelper {

    private final IdHelper idHelper;

    public CommentDisputeMetadata toCommentDisputeMetadata(
        final DisputeWorkflow disputeWorkflow,
        final String userEmailId,
        final String comment,
        final CommentType commentType) {
        return CommentDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(
                idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .commenterEmailId(userEmailId)
            .commentType(commentType)
            .comment(comment)
            .build();
    }

    public InstitutionalCreditDisputeMetadata toCreditDisputeMetadata(
        final DisputeWorkflow disputeWorkflow,
        final InstitutionalCreditTransitionContext institutionalCreditTransitionContext) {
        return InstitutionalCreditDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(
                idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .creditSourceType(
                SourceType.valueOf(
                    institutionalCreditTransitionContext.getCreditSourceType().name()))
            .creditSourceId(institutionalCreditTransitionContext.getCreditSourceId())
            .creditAmount(institutionalCreditTransitionContext.getCreditAmount())
            .build();
    }

    public InstitutionalDebitDisputeMetaData toDebitDisputeMetadata(
        final DisputeWorkflow disputeWorkflow,
        final InstitutionalDebitTransitionContext institutionalDebitTransitionContext) {
        return InstitutionalDebitDisputeMetaData.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(
                idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .debitSourceType(
                SourceType.valueOf(institutionalDebitTransitionContext.getDebitSourceType().name()))
            .debitSourceId(institutionalDebitTransitionContext.getDebitSourceId())
            .debitAmount(institutionalDebitTransitionContext.getDebitAmount())
            .build();
    }

    public AccountingEventDisputeMetadata toAccountingEventDisputeMetadata(
        final DisputeWorkflow disputeWorkflow,
        final AccountingEvent accountingEvent) {
        return AccountingEventDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(
                idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .accountingEventId(accountingEvent.getHeader().getTransactionId())
            .accountingEvent(accountingEvent)
            .build();
    }

    public UdirDisputeMetadata toUdirDisputeMetadata(final DisputeWorkflow disputeWorkflow, final
    UPIClientOutgoingComplaintResponse upiClientOutgoingComplaintResponse) {
        return UdirDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(
                idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .udirResponse(toUdirNpciResponseModel(upiClientOutgoingComplaintResponse))
            .build();
    }

    public UdirNpciResponseModel toUdirNpciResponseModel(
        UPIClientOutgoingComplaintResponse upiClientOutgoingComplaintResponse) {
        return UdirNpciResponseModel.builder()
            .errorCode(upiClientOutgoingComplaintResponse.getErrorCode())
            .adjCode(upiClientOutgoingComplaintResponse.getAdjCode())
            .adjFlag(upiClientOutgoingComplaintResponse.getAdjFlag())
            .adjReason(upiClientOutgoingComplaintResponse.getAdjReason())
            .adjRemark(upiClientOutgoingComplaintResponse.getAdjRemark())
            .complaintId(upiClientOutgoingComplaintResponse.getComplaintId())
            .crn(upiClientOutgoingComplaintResponse.getCrn())
            .state(upiClientOutgoingComplaintResponse.getState())
            .build();
    }

    public FraudActionDisputeMetadata toFraActionDisputeMetadata(final FraudAction fraudAction, final DisputeWorkflow disputeWorkflow){

        return FraudActionDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(
                idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .reasonCode(TransformationUtils.getSafeEnumName(fraudAction.getResponseCode()))
            .actionType(fraudAction.getType())
            .build();
    }

    public NetBankingDisputeMetadata toNBDisputeMetadata(
        final DisputeWorkflow disputeWorkflow,
        final String merchantTxnId) {

        return NetBankingDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .disbursementTransactionId(merchantTxnId)
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .build();
    }

    public WalletDisputeMetadata toWalletDisputeMetadata(
        final DisputeWorkflow disputeWorkflow,
        final String reasonCode) {

        return WalletDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .reasonCode(reasonCode)
            .build();
    }

    public ToaDisputeMetadata toToaDisputeMetadata(final DisputeWorkflow disputeWorkflow,
                                                   final TOAContext toaContext) {
        return ToaDisputeMetadata.builder()
                .key(PrimaryKey.builder()
                        .partitionId(disputeWorkflow.getKey().getPartitionId())
                        .build())
                .disputeMetadataId(
                        idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
                .disbursementTransactionId(toaContext.getToaTransactionId())
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .merchantOrderId(toaContext.getMerchantOrderId())
                .toaTransactionMetadata(ToaTransactionMetadata.builder()
                        .toaReceiverInstrument(toaContext.getToaReceiverInstrument())
                        .build())
                .build();
    }
}
