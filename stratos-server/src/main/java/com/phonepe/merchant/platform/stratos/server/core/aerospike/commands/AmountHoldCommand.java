package com.phonepe.merchant.platform.stratos.server.core.aerospike.commands;

import com.aerospike.client.AerospikeClient;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.AerospikeCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.AerospikeSet;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.AmountHoldKey;
import com.phonepe.verified.kaizen.configs.AerospikeConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@Singleton
public class AmountHoldCommand extends AerospikeCommand<AmountHoldKey, Long> {

    @Inject
    public AmountHoldCommand(
            final AerospikeClient aerospikeClient,
            final AerospikeConfig stratosAerospikeConfig,Map<AerospikeSet, Integer> stratosAerospikeTTLSeconds) {
        super(aerospikeClient, stratosAerospikeConfig,stratosAerospikeTTLSeconds, AerospikeSet.AMOUNT_HOLD, Long.class);
    }
}
