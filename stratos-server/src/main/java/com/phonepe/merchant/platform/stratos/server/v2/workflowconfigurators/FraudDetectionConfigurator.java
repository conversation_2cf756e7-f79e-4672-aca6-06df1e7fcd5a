package com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class FraudDetectionConfigurator implements WorkflowConfigurator<DisputeWorkflowState, DisputeWorkflowEvent> {

    private final UpdateDisputeStateAction updateDisputeStateActionForFraudCheck;

    @Override
    @SneakyThrows
    public void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .target(DisputeWorkflowState.FRAUD_REJECTED)
                .event(DisputeWorkflowEvent.FRAUD_REJECT)
                .action(updateDisputeStateActionForFraudCheck)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .target(DisputeWorkflowState.SUSPECTED_FRAUD)
                .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
                .action(updateDisputeStateActionForFraudCheck)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.SUSPECTED_FRAUD)
                .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .event(DisputeWorkflowEvent.SUSPECTED_FRAUD_TO_ACCEPTANCE)
                .action(updateDisputeStateActionForFraudCheck)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.SUSPECTED_FRAUD)
                .target(DisputeWorkflowState.FRAUD_REJECTED)
                .event(DisputeWorkflowEvent.FRAUD_REJECT)
                .action(updateDisputeStateActionForFraudCheck)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.FRAUD_REJECTED)
                .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.FRAUD_REJECTED_TO_REPRESENTMENT)
                .action(updateDisputeStateActionForFraudCheck)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .target(DisputeWorkflowState.FRAUD_REJECTED)
                .event(DisputeWorkflowEvent.FRAUD_REJECT)
                .action(updateDisputeStateActionForFraudCheck)
                .and();
    }
}
