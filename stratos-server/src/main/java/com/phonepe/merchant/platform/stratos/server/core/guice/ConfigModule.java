package com.phonepe.merchant.platform.stratos.server.core.guice;

import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.phonepe.central.stratos.penalty.server.config.PenaltyTenantVersionConfig;
import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.error.configurator.model.ErrorConfiguratorConfig;
import com.phonepe.error.configurator.model.ErrorConfiguratorModule;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.configs.DisputeCategoryTtlConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.DisputeWorkflowStateConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.KillSwitchConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.KratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.configs.NpciDisputeFlag;
import com.phonepe.merchant.platform.stratos.server.core.configs.P2pmToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.TtlConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.toa.ToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.DocstoreBaseUrl;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.DocstoreClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.EdcClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.KillSwitchClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.KillSwitchConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.MerchantMandateClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.MerchantServiceConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.NexusClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.P2pmToaConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.PaymentsTxnlClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.PgTransportServiceConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.PlutusEventIngestionConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.PlutusTransactionStatusConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.ZencastConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.WalletTxnlClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.WardenClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.neuron.PulseType;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchants.platform.notificationbundle.config.NotificationClientConfig;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.verified.kaizen.configs.CaffeineCacheConfig;
import java.io.File;
import java.net.MalformedURLException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class ConfigModule extends ErrorConfiguratorModule<StratosErrorCodeKey> {


    @Provides
    @Singleton
    public Map<String, FileConfig> getFileConfigs(
        final StratosConfiguration config) {
        return config.getFileConfigs();
    }

    @Provides
    @Singleton
    public EventIngestorClientConfig getEventIngestorClientConfig(
        final StratosConfiguration config) {
        return config.getEventIngestor();
    }

    @Provides
    @Singleton
    @DocstoreClientConfig
    public HttpConfiguration getDocstoreClientConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.DOCSTORE_CLIENT_ID);
    }

    @Provides
    @Singleton
    @PaymentsTxnlClientConfig
    public HttpConfiguration getPaymentsTxnlClientConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.PAYMENTS_TXNL_CLIENT_ID);
    }

    @Provides
    @Singleton
    @NexusClientConfig
    public HttpConfiguration getNexusClientConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.NEXUS_CLIENT_ID);
    }

    @Provides
    @Singleton
    @WalletTxnlClientConfig
    public HttpConfiguration getWalletTxnlClientConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.WALLET_CHARGEBACK);
    }

    @Provides
    @Singleton
    @KillSwitchClientConfig
    public KillSwitchConfig getKillSwitchClientConfig(final StratosConfiguration config) {
        return config.getKillswitchConfig();
    }

    @Provides
    @Singleton
    @P2pmToaConfiguration
    public P2pmToaConfig getP2pmToaConfig(final StratosConfiguration config) {
        return config.getP2pmToaConfig();
    }

    @Provides
    @Singleton
    @MerchantMandateClientConfig
    public HttpConfiguration getMerchantMandateClientConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.MERCHANT_MANDATE_CLIENT_ID);
    }

    @Provides
    @Singleton
    @EdcClientConfig
    public HttpConfiguration getEdcClientConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.EDC_CLIENT_ID);
    }


    @Provides
    @Singleton
    @MerchantServiceConfiguration
    public HttpConfiguration getMerchantServiceConfiguration(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.MERCHANT_SERVICE_CLIENT_ID);
    }

    @Provides
    @Singleton
    @PlutusEventIngestionConfiguration
    public HttpConfiguration getPlutusEventIngestionConfiguration(
        final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.PLUTUS_EVENTS_INGESTION_CLIENT_ID);
    }

    @Provides
    @Singleton
    @PlutusTransactionStatusConfiguration
    public HttpConfiguration getPlutusTransactionStatusConfiguration(
        final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.PLUTUS_TRANSACTION_STATUS_CLIENT_ID);
    }

    @Provides
    @Singleton
    @PgTransportServiceConfig
    public HttpConfiguration getPgTransportConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.PG_TRANSPORT_CLIENT_ID);
    }

    @Provides
    @Singleton
    @KillSwitchConfiguration
    public HttpConfiguration getKillSwitchConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.KILLSWITCH_CLIENT_ID);
    }
    @Provides
    @Singleton
    @WardenClientConfig
    public HttpConfiguration getWardenClientConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.WARDEN_CLIENT_ID);
    }

    @Provides
    @Singleton
    @ZencastConfiguration
    public HttpConfiguration getZencastConfig(final RangerHubConfiguration config) {
        return HttpClientUtils.getConfig(config, Constants.ZENCAST_CLIENT_ID);
    }


    @Provides
    @Singleton
    public Map<String, DisputeCategory> getDisputeCategoryConfig(
        final StratosConfiguration config) {
        return config.getDisputeCategoryMapping().entrySet()
            .stream()
            .flatMap(e -> e.getValue().stream().map(
                v -> Map.entry(v, e.getKey())))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Provides
    @Singleton
    public DisputeWorkflowStateConfig getDisputeWorkflowStateConfig(
        final StratosConfiguration config) {
        return config.getDisputeWorkflowStateConfig();
    }

    @Provides
    @Singleton
    public Set<String> getchargebackAnomalyMerchants(
        final StratosConfiguration config) {
        return config.getChargebackAnomalyMerchants();
    }

    @Provides
    @Singleton
    public Map<PulseType, String> getPulseTypeCellMapping(
        final StratosConfiguration config) {
        return config.getPulseTypeCellMapping();
    }

    @Provides
    @Singleton
    public NotificationClientConfig getNotificationClientConfig(final StratosConfiguration config) {
        return config.getNotificationClientConfig();
    }

    @Provides
    @Singleton
    public Map<ActionType, List<StratosErrorCodeKey>>  getActorIgnoreErrorCode(final StratosConfiguration config) {
        return config.getActorIgnoreErrorCodes();
    }

    @Provides
    @Singleton
    public KratosConfiguration getKratosConfiguration(final StratosConfiguration config) {
        return config.getKratosConfiguration();
    }

    @Provides
    @Singleton
    public RangerHubConfiguration getRangerHubConfiguration(final StratosConfiguration config) {
        return config.getRangerHubConfiguration();
    }

    @Provides
    @Singleton
    public Map<DisputeType, ToaConfig> getToaConfigMap(final StratosConfiguration config) {
        return config.getToaConfig();
    }

    @Singleton
    @Override
    public ResourceErrorService<StratosErrorCodeKey> providesErrorConfiguratorService(ErrorConfiguratorConfig errorConfiguratorConfig) throws MalformedURLException {
        File resourceBundlePath = new File(errorConfiguratorConfig.getErrorPropertiesPath());
        return new ResourceErrorService<>(errorConfiguratorConfig, resourceBundlePath, StratosErrorCodeKey.class);
    }



    @Provides
    @Singleton
    public Map<DisputeWorkflowState, Long> provideDisputeWorkflowStateTTLDaysMap(StratosConfiguration config){
        return config.getDisputeWorkflowStateTTLDaysMap();
    }
    @Provides
    @Singleton
    public CaffeineCacheConfig caffeineCacheConfig(StratosConfiguration kaizenConfig) {
        return kaizenConfig.getCaffeineCacheConfig();
    }

    @Provides
    @Singleton
    @Named("aesCipherKeyConfig")
    public Map<Integer, byte[]> provideAesCipherKeyConfig(StratosConfiguration kaizenConfig) {
        return kaizenConfig.getAesCipherKeyConfig().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getBytes(StandardCharsets.UTF_8)));
    }

    @Provides
    @Singleton
    @Named("latestAesCipherKey")
    public Map.Entry<Integer, byte[]> provideLatestAesCipherKey(@Named("aesCipherKeyConfig") Map<Integer, byte[]> aesCipherKeyConfig) {
        return Collections.max(aesCipherKeyConfig.entrySet(), Map.Entry.comparingByKey());
    }
    @Provides
    @Singleton
    public Map<String, HttpConfiguration> provideHttpCallbackClientConfigMap(StratosConfiguration config) {
        return config.getHttpCallbackClientConfigMap();
    }
    @Provides
    @Singleton
    @DocstoreBaseUrl
    public String getDocstoreBaseUrl(final StratosConfiguration config) {
        return config.getDocstoreBaseUrl();
    }



    @Provides
    @Singleton
    public NpciDisputeFlag provideNpciDisputeFlag(StratosConfiguration config){
        return config.getNpciDisputeFlag();
    }

    @Provides
    @Singleton
    public Map<DisputeType, DisputeCategoryTtlConfig> provideFraudChargebackTtl(StratosConfiguration configuration){
        return configuration.getFraudChargebackTtl();
    }

    @Provides
    @Singleton
    public Map<DisputeType, TtlConfig> provideTtlConfigMap(final StratosConfiguration config) {
        return config.getTtlConfig();
    }

    @Provides
    @Singleton
    public Map<DisputeStage, String> provideDisputeStage(final StratosConfiguration configuration){
        return configuration.getWalletDisputeStageMap();
    }

    @Provides
    @Singleton
    public Map<DisputeType, String> provideOlympusTenantIdMap(StratosConfiguration config){
        return config.getOlympusTenantIdMap();
    }

    @Provides
    @Singleton
    public Map<String, PenaltyTenantVersionConfig> penaltyValidationConfigs(final StratosConfiguration config) {
        Map<String, PenaltyTenantVersionConfig> tenantSubcategoryMap = new HashMap<>();
        config.getPenaltyTenantConfigs()
                .forEach(penaltyValidationConfig -> {
                    String key = penaltyValidationConfig.getTenantName() + ":"
                            + penaltyValidationConfig.getTenantSubCategoryName();
                    penaltyValidationConfig.getTenantVersionConfigs()
                            .forEach(versionConfig -> tenantSubcategoryMap.put(key + ":" + versionConfig.getVersion(), versionConfig));
                });
        return tenantSubcategoryMap;
    }

    @Provides
    @Singleton
    public Map<DisputeType, DisputeWorkflowVersion> getDisputeWorkflowVersionMap(final StratosConfiguration config) {
        return config.getDisputeWorkflowVersionMap();
    }

    @Provides
    @Singleton
    public Map<DisputeType, Long> getHoldTtlConfig(final StratosConfiguration config) {
        return config.getHoldTtlConfig();
    }
    @Provides
    @Singleton
    public List<DisputeType> provideFileUploadChecker(StratosConfiguration config){
        return config.getFileUploadChecker();
    }
    @Provides
    @Singleton
    public Map<DisputeType, Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> provideIgnoreSignalStates(
            StratosConfiguration config){
        return config.getIgnoreSignalStateConfig();
    }

}
