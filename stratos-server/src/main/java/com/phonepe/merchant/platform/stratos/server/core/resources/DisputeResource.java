package com.phonepe.merchant.platform.stratos.server.core.resources;


import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeMetadataDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CheckStatusRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DownloadReportRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.UpdateCommunicationIdRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CheckStatusResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CreateDisputeResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.RefundEligibilityResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata.DisputeMetadataResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.EnumClass;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.resolver.OlympusTenantIdResolver;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.OlympusPermissionName;
import com.phonepe.merchant.platform.stratos.server.core.utils.StringUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.resolver.ConstantTenantTypeResolver;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.authz.enums.TenantType;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.http.HttpStatus;

@Slf4j
@Path("/v1/dispute")
@Tag(name = "Dispute Related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class DisputeResource {

    private final DisputeService disputeService;
    private final OlympusIMClient olympusIMClient;

    @GET
    @ExceptionMetered
    @AccessAllowed
    @Path("/events/{disputeType}/{disputeStage}/{disputeWorkflowVersion}/{currentState}")
    @Operation(summary = "Fetch upcoming allowed events for a given dispute type, stage, version and current state")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Set<DisputeWorkflowEvent> getUpcomingEvents(
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
        @PathParam("disputeType") final DisputeType disputeType,
        @PathParam("disputeStage") final DisputeStage disputeStage,
        @PathParam("disputeWorkflowVersion") final DisputeWorkflowVersion disputeWorkflowVersion,
        @PathParam("currentState") final DisputeWorkflowState currentState) {

        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        return disputeService
            .getAuthorizedUpcomingEvents(userAuthDetails, disputeType, disputeStage,
                disputeWorkflowVersion, currentState);
    }

    @POST
    @ExceptionMetered
    @AccessAllowed
    @Path("/{transactionReferenceId}/{disputeWorkflowId}/trigger/{disputeWorkflowEvent}")
    @Operation(summary = "Trigger a event on given payment reference id and dispute workflow id")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void triggerEvent(
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
        @PathParam("transactionReferenceId") final String transactionReferenceId,
        @PathParam("disputeWorkflowId") final String disputeWorkflowId,
        @PathParam("disputeWorkflowEvent") final DisputeWorkflowEvent disputeWorkflowEvent,
        @Valid @NotNull final TransitionContext transitionContext) {

        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        AuthorizationUtils.authorizeTriggerEvent(olympusIMClient, userAuthDetails, disputeWorkflowEvent);

        disputeService
            .triggerEvent(userAuthDetails, transactionReferenceId, disputeWorkflowId,
                disputeWorkflowEvent, transitionContext);
    }

    @PUT
    @ExceptionMetered
    @Path("/communication")
    @RolesAllowed("chargeback/update-communication-id")
    @Operation(summary = "Update communication id for a list of dispute workflow ids")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void updateCommunicationIdInDisputeWorkflow(
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
        @Valid @NotNull final UpdateCommunicationIdRequest updateCommunicationIdRequest) {

        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();

        UserType userType = AuthorizationUtils.getUserType(userAuthDetails.getUserDetails());

        disputeService.updateCommunicationIdInDisputeWorkflow(updateCommunicationIdRequest,
            userAuthDetails.getUserDetails().getUserId(),
            userType);
    }

    @GET
    @ExceptionMetered
    @Produces("image/svg+xml")
    @Path("/graph/{disputeType}/{disputeStage}/{disputeWorkflowVersion}")
    @AccessAllowed
    @Operation(summary = "Generate SVG Graph of State Machine given Dispute Type, Stage and Workflow Version")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public String getStateMachineSvgGraph(
        @PathParam("disputeType") final DisputeType disputeType,
        @PathParam("disputeStage") final DisputeStage disputeStage,
        @PathParam("disputeWorkflowVersion") final DisputeWorkflowVersion disputeWorkflowVersion) {

        return disputeService
            .getStateMachineSvgGraph(disputeType, disputeStage, disputeWorkflowVersion);
    }

    @POST
    @ExceptionMetered
    @Path("/status")
    @AccessAllowed
    @Operation(summary = "Check Status of Dispute")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public CheckStatusResponse status(
        @Valid final CheckStatusRequest checkStatusRequest,
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {

        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        AuthorizationUtils.authorizeDisputeStatusCheck(olympusIMClient, userAuthDetails,
            DtoUtils.disputeDtoToType(checkStatusRequest.getDisputeType()));

        return disputeService.checkStatus(checkStatusRequest);
    }

    @POST
    @ExceptionMetered
    @Path("/download")
    @AccessAllowed
    @Produces({Constants.MEDIA_TYPE_CSV, MediaType.APPLICATION_JSON})
    @Operation(summary = "Download Summary based on Filter Parameters")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response downloadSummary(
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
        @Valid final DownloadReportRequest downloadReportRequest) {

        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        AuthorizationUtils.authorizedDownloadReportApi(olympusIMClient, userAuthDetails, downloadReportRequest.getReportType());

        final String contentDisposition = String.format("attachment;filename=\"%s_summary.csv\"",
            StringUtils.normalize(downloadReportRequest.getReportType().name()));

        return Response.ok(disputeService.download(downloadReportRequest))
            .header("Content-Disposition", contentDisposition)
            .build();
    }

    @GET
    @ExceptionMetered
    @AccessAllowed
    @Path("/enums")
    @Operation(summary = "Fetch all Enums of classes" )
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Map<String, Map<Integer, String>> getAllEnumsByEnumClassName( @Valid @QueryParam("enumClasses") final List<EnumClass> enumClassList){
        ValidationUtils.validateEnumList(enumClassList);
        return disputeService.getAllEnumsByEnumClassName(enumClassList);
    }

    @GET
    @ExceptionMetered
    @RolesAllowed("dispute/refund-eligibility")
    @Path("/refund-eligibility/{transactionReferenceId}")
    @Operation(summary = "Get refund eligibility for a transaction")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public RefundEligibilityResponse refundEligibility(
        @PathParam("transactionReferenceId") final String transactionReferenceId){
        return disputeService.getRefundEligibility(transactionReferenceId);
    }

    @POST
    @ExceptionMetered
    @Path("/reconcile")
    @RolesAllowed("dispute/reconcile")
    @Operation(summary = "Reconcile a dispute")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response reconcile(@Valid DisputeReconcileRequest reconcileRequest) {
        disputeService.reconcile(reconcileRequest);
        return Response.ok().build();
    }

    @GET
    @ExceptionMetered
    @RolesAllowed("dispute/metadata")
    @Path("/{disputeWorkflowId}/metadata")
    @Operation(summary = "Get dispute metadata")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public DisputeMetadataResponse getDisputeMetadata(
        @PathParam("disputeWorkflowId") final String disputeWorkflowId,
        @Valid @QueryParam("type") final List<DisputeMetadataDto> type) {
        return TransformationUtils.toDisputeMetadataResponse(
            disputeService.getDisputeMetadata(disputeWorkflowId,
                type.stream().map(e -> DisputeMetadataType.valueOf(e.name())).collect(
                    Collectors.toSet())));
    }

    @POST
    @ExceptionMetered
    @Path("/{transactionType}/{disputeType}/create")
    @AccessAllowed(permissions =
        {OlympusPermissionName.DISPUTE_CREATION}, tenantTypeResolverParam = TenantType.Constants.CUSTOM,
        tenantTypeResolver = ConstantTenantTypeResolver.class,
        tenantIdResolver = OlympusTenantIdResolver.class)
    @Operation(summary = "Create a dispute")
    @ApiKillerMeta(tags = {
        ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public GenericResponse<CreateDisputeResponse> createDispute(
        @PathParam("transactionType") final TransactionType transactionType,
        @PathParam("disputeType") final com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType disputeType,
        @NotNull @Valid final CreateDisputeRequest createDisputeRequest,
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {

        String userId = serviceUserPrincipal.getUserAuthDetails().getUserDetails().getUserId();

        try {
            log.info("Input request for dispute creation is {}", createDisputeRequest);
            CreateDisputeResponse createDisputeResponse = disputeService.createDispute(
                createDisputeRequest,
                userId);
            return new GenericResponse<>(true, String.valueOf(HttpStatus.OK_200),
                createDisputeResponse);
        } catch (DisputeException ex) {
            log.error("Dispute exception in creating dispute {}", createDisputeRequest, ex);
            return new GenericResponse<>(false, String.valueOf(ex.getErrorCode().getKey()),
                ex.getMessage(), null);
        } catch (Exception ex) {
            log.error("Exception in creating dispute {}", createDisputeRequest, ex);
            return new GenericResponse<>(false, String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR_500),
                "error in dispute creation ", null);
        }
    }
}
