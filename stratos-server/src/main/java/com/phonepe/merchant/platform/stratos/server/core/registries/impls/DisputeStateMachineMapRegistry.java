package com.phonepe.merchant.platform.stratos.server.core.registries.impls;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeStateMachineRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.statemachines.EdcChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.NetBankingChargebackFirstLevelStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.PgFirstLevelChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.PgPreArbChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.statemachines.UdirComplaintStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.UpiFirstLevelChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.UpiPreArbChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.statemachines.WalletFirstLevelChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.statemachines.WalletPreArbChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.bbps.statemachine.BBPSToaStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.notionalcredit.statemachine.NotionalCreditStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines.P2pmToaStateMachine;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.edc.EDCFirstLevelStateMachineV2;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.pg.PGFirstLevelStateMachineV2;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.pg.PGPreArbStateMachineV2;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.upi.UPIFirstLevelChargebackV2;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.upi.UPIPreArbChargebackV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachine;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Singleton
public class DisputeStateMachineMapRegistry implements DisputeStateMachineRegistry {

    private final Map<DisputeStateMachineRegistryKey, DisputeStateMachine> disputeStateMachineRegister;

    private final LoadingCache<DisputeStateMachineRegistryKey, StateMachine<DisputeWorkflowState, DisputeWorkflowEvent>> disputeInternalStateMachineCache;

    @Inject
    @SuppressWarnings("java:S107")
    public DisputeStateMachineMapRegistry(
            final UpiFirstLevelChargebackStateMachine upiFirstLevelChargebackStateMachine,
            final UpiPreArbChargebackStateMachine upiPreArbChargebackStateMachine,
            final PgFirstLevelChargebackStateMachine pgFirstLevelChargebackStateMachine,
            final PgPreArbChargebackStateMachine pgPreArbChargebackStateMachine,
            final UdirComplaintStateMachine udirComplaintStateMachine,
            final P2pmToaStateMachine p2pmToaStateMachine,
            final EdcChargebackStateMachine edcChargebackStateMachine,
            final NetBankingChargebackFirstLevelStateMachine netbankingChargebackFirstLevelStateMachine,
            final NotionalCreditStateMachine notionalCreditStateMachine,
            final BBPSToaStateMachine bbpsToaStateMachine,
            final UPIFirstLevelChargebackV2 upiFirstLevelChargebackV2,
            final UPIPreArbChargebackV2 upiPreArbChargebackV2,
            final PGFirstLevelStateMachineV2 pgFirstLevelStateMachineV2,
            final PGPreArbStateMachineV2 pgPreArbStateMachineV2,
            final EDCFirstLevelStateMachineV2 edcFirstLevelStateMachineV2,
            final WalletFirstLevelChargebackStateMachine walletFirstLevelChargebackStateMachine,
            final WalletPreArbChargebackStateMachine walletPreArbChargebackStateMachine) {
        disputeStateMachineRegister = new HashMap<>();
        register(upiFirstLevelChargebackStateMachine);
        register(upiPreArbChargebackStateMachine);
        register(pgFirstLevelChargebackStateMachine);
        register(pgPreArbChargebackStateMachine);
        register(udirComplaintStateMachine);
        register(p2pmToaStateMachine);
        register(edcChargebackStateMachine);
        register(netbankingChargebackFirstLevelStateMachine);
        register(notionalCreditStateMachine);
        register(bbpsToaStateMachine);
        register(upiFirstLevelChargebackV2);
        register(upiPreArbChargebackV2);
        register(pgFirstLevelStateMachineV2);
        register(pgPreArbStateMachineV2);
        register(edcFirstLevelStateMachineV2);
        register(walletFirstLevelChargebackStateMachine);
        register(walletPreArbChargebackStateMachine);
        logRegisteredDisputeStateMachines();
        disputeInternalStateMachineCache = Caffeine.newBuilder()
            .maximumSize(1_000)
            .build(this::loadInternalStateMachine);
    }

    @Override
    public DisputeStateMachine get(final DisputeStateMachineRegistryKey key) {
        final var disputeStateMachine = disputeStateMachineRegister.get(key);
        if (Objects.isNull(disputeStateMachine)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_STATE_MACHINE_NOT_FOUND,
                Map.of(Constants.MESSAGE, "No dispute state machine found with provided key",
                    Constants.KEY, key));
        }
        return disputeStateMachine;
    }

    @Override
    public StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> getInternalStateMachine(
        final DisputeStateMachineRegistryKey key) {
        return disputeInternalStateMachineCache.get(key);
    }

    @Override
    public Set<DisputeStateMachineRegistryKey> getAllRegisteredKeys() {
        return disputeStateMachineRegister.keySet();
    }

    private void register(final DisputeStateMachine disputeStateMachine) {
        disputeStateMachineRegister.put(disputeStateMachine.getRegistryKey(), disputeStateMachine);
    }

    private StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> loadInternalStateMachine(
        final DisputeStateMachineRegistryKey key) {
        return get(key).internalApiGetStateMachine();
    }

    private void logRegisteredDisputeStateMachines() {

        final var builder = new StringBuilder("Registered dispute state machines:")
            .append(System.lineSeparator())
            .append(System.lineSeparator());

        disputeStateMachineRegister.forEach((k, v) -> builder
            .append("\t")
            .append(k.getDisputeType()).append(",")
            .append(k.getDisputeStage()).append(",")
            .append(k.getDisputeWorkflowVersion())
            .append("\t => ")
            .append("(").append(v.getClass().getName()).append(")")
            .append(System.lineSeparator()));

        log.info(builder.toString());
    }
}
