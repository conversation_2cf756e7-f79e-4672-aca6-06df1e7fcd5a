package com.phonepe.merchant.platform.stratos.server.core.models;

public enum RowType {
    UPI_FILE_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitUpiFileRow();
        }
    },
    PG_FILE_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitPgFileRow();
        }
    },
    PG_MIS_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitPgMisRow();
        }
    },
    TOA_NEURON_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitToaNeuronRow();
        }
    },
    EDC_FILE_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitEdcFileRow();
        }
    },
    EDC_MIS_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitEdcMisRow();
        }
    },
    NB_FILE_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitNBFileRow();
        }
    },
    TOA_API_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitToaApiRow();
        }
    },
    WALLET_FILE_ROW {
        @Override
        public <T> T accept(final RowTypeVisitor<T> visitor) {
            return visitor.visitNBFileRow();
        }
    };


    public abstract <T> T accept(RowTypeVisitor<T> visitor);
}
