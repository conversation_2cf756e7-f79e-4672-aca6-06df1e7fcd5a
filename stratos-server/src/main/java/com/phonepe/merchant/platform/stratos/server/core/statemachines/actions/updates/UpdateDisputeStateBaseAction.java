package com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates;

import com.phonepe.merchant.platform.stratos.models.callbacks.CallbackType;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStateMapper;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.CallbackMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.BaseTransitionAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import com.phonepe.olympus.im.models.user.UserAuthDetails;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@RequiredArgsConstructor
public abstract class UpdateDisputeStateBaseAction extends
    BaseTransitionAction<DisputeWorkflowState, DisputeWorkflowEvent> {

    protected final DisputeService disputeService;

    protected final DisputeWorkflowRepository disputeWorkflowRepository;

    protected final EventIngester eventIngester;
    protected final CallbackActor callbackActor;

    @Override
    protected void performTransition(
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var transactionReferenceId = stateContext.getExtendedState()
            .get(Fields.transactionReferenceId, String.class);
        Objects.requireNonNull(transactionReferenceId);

        final var disputeWorkflowId = stateContext.getExtendedState()
            .get(Fields.disputeWorkflowId, String.class);
        Objects.requireNonNull(disputeWorkflowId);

        final var userAuthDetails = stateContext.getExtendedState()
                        .get(UserAuthDetails.class, UserAuthDetails.class);
        final var userDetails = userAuthDetails.getUserDetails();
        Objects.requireNonNull(userDetails);

        final var userId = userDetails.getUserId();
        Objects.requireNonNull(userId);

        final var userType = AuthorizationUtils.getUserType(userDetails);
        Objects.requireNonNull(userType);

        final var disputeWorkflow = disputeService
            .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);

        final var previousState = disputeWorkflow.getCurrentState();

        preTransition(disputeWorkflow, stateContext);

        disputeWorkflow.setCurrentEvent(stateContext.getEvent());
        disputeWorkflow.setCurrentState(stateContext.getTarget().getId());
        disputeWorkflow.setGandalfUserId(userId);
        disputeWorkflow.setUserType(userType);

        Optional<DisputeWorkflow> dw = disputeWorkflowRepository.save(disputeWorkflow, savedDisputeWorkflow -> {
            transition(disputeWorkflow, stateContext);
            return savedDisputeWorkflow;
        });
        if(dw.isPresent()){
            final var currentState = disputeWorkflow.getCurrentState();
            DisputeStateMapper mapper = MapperUtils.getStateMapper(
                    disputeWorkflow.getDisputeWorkflowVersion());
            var prevStatus = previousState.accept(mapper, dw.get());
            var currStatus = currentState.accept(mapper, dw.get());
            if (prevStatus != null && currStatus != null &&
                    !prevStatus.equals(currStatus)) {
                try {
                    callbackActor.publish(CallbackMessage.builder()
                        .dispute(disputeWorkflow.getDispute())
                        .callbackType(CallbackType.STATE_CHANGE)
                        .previousState(prevStatus)
                        .currentStatus(currStatus)
                        .eventTime(LocalDateTime.now())
                        .build());
                } catch (Exception e) {
                    log.error("Exception while pushing callbacks event for disputeWorkflowId : {}",
                        disputeWorkflowId,
                        e);
                }
            }
        }

        postTransition(disputeWorkflow);

        eventIngester.generateEvent(
            FoxtrotEventUtils.toDisputeActionSuccessEvent(disputeWorkflow.getDispute(),
                disputeWorkflow, stateContext.getSource().getId()));
    }

    protected void preTransition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {
        // NOOP
    }

    protected void transition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {
        // NOOP
    }

    protected void postTransition(final DisputeWorkflow disputeWorkflow) {
        // NOOP
    }
}
