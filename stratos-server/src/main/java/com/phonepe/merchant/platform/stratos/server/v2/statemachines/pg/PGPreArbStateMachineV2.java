package com.phonepe.merchant.platform.stratos.server.v2.statemachines.pg;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.*;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.TerminalStateListener;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseHoldRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.actions.PgChargebackCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.ChargebackRecoveryEligibilityAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.HoldReversalAction;
import com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators.*;

@Singleton
public class PGPreArbStateMachineV2 extends PGFirstLevelStateMachineV2{

    @Inject
    @SuppressWarnings("java:S107")
    protected PGPreArbStateMachineV2(
            final TransitionLockCommand transitionLockCommand,
            final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
            final ProactiveHoldWorkflowConfigurator proactiveHoldWorkflowConfigurator,
            final MerchantCommunicationConfigurator merchantCommunicationConfigurator,
            final FraudDetectionConfigurator fraudDetectionConfigurator,
            final PgChargebackCreateEntryAction createEntryAction,
            final DebitDisputeAction debitDisputeAction,
            final AcceptDisputeAction acceptDisputeAction,
            final AcceptDebitAndDebitDisputeAction acceptDebitAndDebitDisputeAction,
            final InternalMIDWorkflowConfigurator internalMIDWorkflowConfigurator,
            final PartialFulfilmentWorkflowConfigurator partialFulfilmentWorkflowConfigurator,
            final UpdateDisputeStateAction updateDisputeStateAction,
            final FraudCheckDisputeAction fraudCheckDisputeAction,
            final CancelWorkflowConfigurator cancelWorkflowConfigurator,
            final RaiseHoldRecoveryReversalAccountingEventAction raiseHoldRecoveryReversalAccountingEventAction,
            final ReverseHoldRecoverCBConfigurator reverseHoldRecoverCBConfigurator,
            final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction,
            final ChargebackRecoveryEligibilityAction recoverChargebackAction,
            final HoldReversalAction holdReversalAction,
            final TerminalStateListener<DisputeWorkflowState, DisputeWorkflowEvent> terminalStateListener) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, proactiveHoldWorkflowConfigurator, merchantCommunicationConfigurator, fraudDetectionConfigurator, createEntryAction, debitDisputeAction,
                acceptDisputeAction, acceptDebitAndDebitDisputeAction, internalMIDWorkflowConfigurator,
                partialFulfilmentWorkflowConfigurator, updateDisputeStateAction, fraudCheckDisputeAction, cancelWorkflowConfigurator,
                raiseHoldRecoveryReversalAccountingEventAction, reverseHoldRecoverCBConfigurator, raiseAccountingEventAction, recoverChargebackAction, holdReversalAction, terminalStateListener);
    }
    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
                .disputeType(DisputeType.PG_CHARGEBACK)
                .disputeStage(DisputeStage.PRE_ARBITRATION)
                .disputeWorkflowVersion(DisputeWorkflowVersion.V2)
                .build();
    }
}
