package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.row;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.commons.SourceType;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalDebitTransitionContext;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeSignalEvent;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.PgMisRowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PgTransportService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.row.RowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;


@Slf4j
public class PgMisRowProcessor extends RowProcessor {

    private final PgTransportService pgTransportService;
    private final DisputeService disputeService;

    private final EventIngester eventIngester;
    private final Map<DisputeType,Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> ignoreSignalStateConfig;

    @Inject
    public PgMisRowProcessor(PgTransportService pgTransportService, DisputeService disputeService,
             RowRepository rowRepository, final EventIngester eventIngester,
             Map<DisputeType, Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> ignoreSignalStateConfig) {
        super(rowRepository);
        this.pgTransportService = pgTransportService;
        this.disputeService = disputeService;
        this.eventIngester=eventIngester;
        this.ignoreSignalStateConfig = ignoreSignalStateConfig;
    }

    public boolean process(final PgMisRowMessage pgMisRowMessage) {
        log.info("pgMisRowMessage: {}", pgMisRowMessage);

        var rowId = pgMisRowMessage.getRowId();
        var transactionId = pgMisRowMessage.getTransactionId();

        final BigDecimal netAmount = pgMisRowMessage.getNetAmount();

        final var sourceId = pgMisRowMessage.getSourceId();

        var isProcessed = saveOrUpdateRow(sourceId, rowId, RowState.UNDER_PROCESS,
            RowContext.builder()
                .content(MapperUtils.serializeToString(pgMisRowMessage)).build(),
            RowType.PG_MIS_ROW);
        if (isProcessed) {
            return true;
        }
        try {
            var paymentsTransactionId = getPaymentTransactionId(transactionId);

            var dispute = disputeService.getDispute(paymentsTransactionId,
                DisputeType.PG_CHARGEBACK);
            var disputeWorkflow = disputeService.getDisputeWorkflow(
                paymentsTransactionId,
                DisputeType.PG_CHARGEBACK, dispute.getCurrentDisputeStage());
            eventIngester.generateEvent(FoxtrotEventUtils.getSignalEvent(disputeWorkflow,DisputeWorkflowEvent.RECEIVE_DEBIT, DisputeSignalEvent.SignalType.DEBIT));

            var transactionType = pgMisRowMessage.getTransactionType();
            log.info(
                "Processing Signal for paymentsTransactionId:{}, dispute: {}, disputeWorkflow: {}",
                paymentsTransactionId, dispute, disputeWorkflow);
            transactionType.accept(new RowTransactionTypeVisitor<Void>() {
                @Override
                public Void visitChargeback() {
                    if(DisputeWorkflowUtils.ignoreSignal(ignoreSignalStateConfig, disputeWorkflow)) {
                        log.info("Ignored debit Signal for disputeWorkflowId : {}, current state : {}",
                            disputeWorkflow.getDisputeWorkflowId(), disputeWorkflow.getCurrentState());
                        return null;
                    }
                    DisputeWorkflowEvent disputeWorkflowEvent = Optional.ofNullable(
                        disputeWorkflow.getCurrentState().accept(
                            new DisputeWorkflowStateNonMandatoryVisitor<DisputeWorkflowEvent>() {
                                @Override
                                public DisputeWorkflowEvent visitPgAcceptanceCompleted() {
                                    return DisputeWorkflowEvent.RECEIVE_DEBIT;
                                }
                                @Override
                                public DisputeWorkflowEvent visitAcceptanceCompleted() {
                                    return DisputeWorkflowEvent.RECEIVE_DEBIT;
                                }

                                @Override
                                public DisputeWorkflowEvent visitPgPartialRepresentmentCompleted() {
                                    return DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT;
                                }

                                @Override
                                public DisputeWorkflowEvent visitPartialRepresentmentCompleted() {
                                    return DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT;
                                }

                                @Override
                                public DisputeWorkflowEvent visitMerchantAcceptedChargeback() {
                                    return DisputeWorkflowEvent.MERCHANT_ACCEPTED_CHARGEBACK_TO_DEBIT_RECEIVED;
                                }
                            })).orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSITION,
                        Map.of(Constants.MESSAGE,
                            String.format("Invalid Debit transformation for %s from %s",
                                disputeWorkflow.getDisputeWorkflowId(),
                                disputeWorkflow.getCurrentState()))));

                    disputeService.triggerEvent(
                        Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                        paymentsTransactionId,
                        disputeWorkflow.getDisputeWorkflowId(),
                        disputeWorkflowEvent,
                        InstitutionalDebitTransitionContext.builder()
                            .debitAmount(netAmount.multiply(new BigDecimal(100)).longValue())
                            .debitSourceType(SourceType.FILE)
                            .debitSourceId(sourceId)
                            .build()
                    );
                    saveOrUpdateRow(sourceId, rowId, RowState.PROCESSED, RowContext.builder()
                            .content(MapperUtils.serializeToString(pgMisRowMessage)).build(),
                        RowType.PG_MIS_ROW);
                    return null;
                }

                // For Transaction Type Chargeback_Reversed we are marking it as Failed Directly because credit row is not expected
                @Override
                public Void visitChargebackReversed() {
                    saveOrUpdateRow(sourceId, pgMisRowMessage.getRowId(), RowState.FAILED,
                        RowContext.builder()
                            .content(MapperUtils.serializeToString(pgMisRowMessage))
                            .build(), RowType.PG_MIS_ROW);
                    return null;
                }
            });
            return true;
        } catch (Exception e) {
            log.error("Error in processing Signal", e);
            var error = StratosErrorCodeKey.INTERNAL_SERVER_ERROR;
            if (e instanceof DisputeException) { //NOSONAR
                log.error("Pg Mis Row Error Context {}",
                    MapperUtils.serializeToString(((DisputeException) e).getContext()));
                error = ((DisputeException) e).getErrorCode();
            }

            saveOrUpdateRow(sourceId, pgMisRowMessage.getRowId(), RowState.FAILED,
                RowContext.builder()
                    .content(MapperUtils.serializeToString(pgMisRowMessage))
                    .code(error.name())
                    .build(),
                RowType.PG_MIS_ROW);
            throw e;
        }
    }

    private String getPaymentTransactionId(String transactionId){
        if (transactionId.startsWith("T"))
            return transactionId;
        return pgTransportService.getPaymentsIdFromPgTxnId(transactionId);
    }

}
