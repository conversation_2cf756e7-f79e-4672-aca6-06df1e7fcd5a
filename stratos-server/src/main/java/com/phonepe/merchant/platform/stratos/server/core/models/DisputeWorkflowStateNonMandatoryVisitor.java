package com.phonepe.merchant.platform.stratos.server.core.models;

public interface DisputeWorkflowStateNonMandatoryVisitor<T> extends DisputeWorkflowStateVisitor<T> {

    @Override
    default T visitReceived() {
        return null;
    }

    @Override
    default T visitRgcsAcceptanceRequired() {
        return null;
    }

    @Override
    default T visitRgcsAcceptanceCompleted() {
        return null;
    }

    @Override
    default T visitRepresentmentRequired() {
        return null;
    }

    @Override
    default T visitNPCIRepresentmentCompleted() {
        return null;
    }

    @Override
    default T visitCreditReceived() {
        return null;
    }

    @Override
    default T visitRefundBlocked() {
        return null;
    }

    @Override
    default T visitFulfilmentDocumentsReceived() {
        return null;
    }

    @Override
    default T visitPartialFulfilmentDocumentsReceived() {
        return null;
    }

    @Override
    default T visitNPCIPartialRepresentmentCompleted() {
        return null;
    }

    @Override
    default T visitPartialCreditReceived() {
        return null;
    }

    @Override
    default T visitMerchantNotRespondedWithinTTL() {
        return null;
    }

    @Override
    default T visitMerchantAcceptedChargeback() {
        return null;
    }

    @Override
    default T visitNPCIAcceptanceCompleted() {
        return null;
    }

    @Override
    default T visitAbsorbChargebackRequested() {
        return null;
    }

    @Override
    default T visitAbsorbChargebackRejected() {
        return null;
    }

    @Override
    default T visitAbsorbChargebackApproved() {
        return null;
    }

    @Override
    default T visitChargebackAbsorbed() {
        return null;
    }

    @Override
    default T visitChargebackAbsorbedReversed() {
        return null;
    }

    @Override
    default T visitRecoverChargebackRequested() {
        return null;
    }

    @Override
    default T visitRecoverChargebackRejected() {
        return null;
    }

    @Override
    default T visitRecoverChargebackApproved() {
        return null;
    }

    @Override
    default T visitRecoverChargebackEventRaised() {
        return null;
    }

    @Override
    default T visitRecoverChargebackEventAccepted() {
        return null;
    }

    @Override
    default T visitReversalOfRecoveredChargebackRequested() {
        return null;
    }

    @Override
    default T visitReversalOfRecoveredChargebackApproved() {
        return null;
    }

    @Override
    default T visitReversalOfRecoveredChargebackEventRaised() {
        return null;
    }

    @Override
    default T visitReversalOfRecoveredChargebackEventAccepted() {
        return null;
    }

    @Override
    default T visitDebitReceived() {
        return null;
    }

    @Override
    default T visitPartialDebitReceived() {
        return null;
    }

    @Override
    default T visitPgRepresentmentCompleted() {
        return null;
    }

    @Override
    default T visitPgAcceptanceCompleted() {
        return null;
    }

    @Override
    default T visitPgPartialRepresentmentCompleted() {
        return null;
    }

    @Override
    default T visitUdirComplaintRejected() {
        return null;
    }

    @Override
    default T visitFailure() {
        return null;
    }

    @Override
    default T visitUdirComplaintAccepted() {
        return null;
    }

    @Override
    default T visitUdirResponseReceived() {
        return null;
    }

    @Override
    default T visitUdirResponseNotReceived() {
        return null;
    }

    @Override
    default T visitInternalMerchantRepresentmentRequired() {
        return null;
    }

    @Override
    default T visitChargebackCancelled() {
        return null;
    }

    @Override
    default T visitToaBlockedDueToKs() {
        return null;
    }

    @Override
    default T visitP2pmToaInitiated() {
        return null;
    }

    @Override
    default T visitP2pmToaInitiationFailed() {
        return null;
    }

    @Override
    default T visitP2pmToaCompleted() {
        return null;
    }

    @Override
    default T visitP2pmToaFailed() {
        return null;
    }

    @Override
    default T visitP2pmToaPending() {
        return null;
    }

    @Override
    default T visitP2pmToaCompletedExternally() {
        return null;
    }

    @Override
    default T visitP2pmToaFailedAfterMaxAutoRetry() {
        return null;
    }

    @Override
    default T visitRepresentedCompleted() {
        return null;
    }

    @Override
    default T visitAcceptanceCompleted() {
        return null;
    }

    @Override
    default T visitPartialRepresentmentCompleted() {
        return null;
    }

    @Override
    default T visitFraudRejected() {
        return null;
    }

    @Override
    default T visitSuspectedFraud() {
        return null;
    }

    @Override
    default T visitCBRefundCreated() {
        return null;
    }

    @Override
    default T visitCBRefundInitated() {
        return null;
    }

    @Override
    default T visitCBRefundAccepted() {
        return null;
    }

    @Override
    default T visitCBRefundFailed() {
        return null;
    }

    @Override
    default T visitToaInitiated() {
        return null;
    }

    @Override
    default T visitToaInitiationFailed() {
        return null;
    }

    @Override
    default T visitToaCompleted() {
        return null;
    }

    @Override
    default T visitToaFailed() {
        return null;
    }

    @Override
    default T visitToaPending() {
        return null;
    }

    @Override
    default T visitToaFailedAfterMaxAutoRetry() {
        return null;
    }

    @Override
    default T visitToaOpened(){ return null;}

    @Override
    default T visitToaClosed() { return null;}

    @Override
    default T visitFraudRepresentmentCompleted(){
        return null;
    }

    @Override
    default T visitNpciAckChargeback(){
        return null;
    }

    @Override
    default T visitNpciRejectedChargeback(){
        return null;
    }

    @Override
    default T visitPartialAcceptedChargeback(){
        return null;
    }

    @Override
    default T visitFullyAcceptedChargeback(){
        return null;
    }

    @Override
    default T visitPartialRejectedChargeback(){
        return null;
    }

    @Override
    default T visitAcceptedChargeback(){
        return null;
    }

    @Override
    default T visitRejectedChargeback(){
        return null;
    }

    @Override
    default T visitCbRefundInitiationCompleted(){
        return null;
    }

    @Override
    default T visitToaUnableToProcess() {
        return null;
    }

    @Override
    default T visitCbRefundProcessedExternally(){
        return null;
    }
}
