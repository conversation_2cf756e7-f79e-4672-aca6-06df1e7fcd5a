package com.phonepe.merchant.platform.stratos.server.core.models;

/**
 * This entity is persisted in DB by it's Ordinal Value Hence only append at the end and do not
 * change Order of existing values while adding new values
 */
public enum DisputeWorkflowState {

    RECEIVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitReceived(context);
        }
    },

    RGCS_ACCEPTANCE_REQUIRED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRgcsAcceptanceRequired();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRgcsAcceptanceRequired(context);
        }
    },
    RGCS_ACCEPTANCE_COMPLETED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRgcsAcceptanceCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRgcsAcceptanceCompleted(context);
        }
    },

    REPRESENTMENT_REQUIRED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRepresentmentRequired();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRepresentmentRequired(context);
        }
    },
    NPCI_REPRESENTMENT_COMPLETED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitNPCIRepresentmentCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitNPCIRepresentmentCompleted(context);
        }
    },
    CREDIT_RECEIVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitCreditReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitCreditReceived(context);
        }
    },

    REFUND_BLOCKED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRefundBlocked();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRefundBlocked(context);
        }
    },

    FULFILMENT_DOCUMENTS_RECEIVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitFulfilmentDocumentsReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitFulfilmentDocumentsReceived(context);
        }
    },

    PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPartialFulfilmentDocumentsReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPartialFulfilmentDocumentsReceived(context);
        }
    },
    NPCI_PARTIAL_REPRESENTMENT_COMPLETED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitNPCIPartialRepresentmentCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitNPCIPartialRepresentmentCompleted(context);
        }
    },
    PARTIAL_CREDIT_RECEIVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPartialCreditReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPartialCreditReceived(context);
        }
    },

    MERCHANT_NOT_RESPONDED_WITHIN_TTL {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitMerchantNotRespondedWithinTTL();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitMerchantNotRespondedWithinTTL(context);
        }
    },

    MERCHANT_ACCEPTED_CHARGEBACK {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitMerchantAcceptedChargeback();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitMerchantAcceptedChargeback(context);
        }
    },
    NPCI_ACCEPTANCE_COMPLETED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitNPCIAcceptanceCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitNPCIAcceptanceCompleted(context);
        }
    },

    ABSORB_CHARGEBACK_REQUESTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitAbsorbChargebackRequested();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitAbsorbChargebackRequested(context);
        }
    },
    ABSORB_CHARGEBACK_REJECTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitAbsorbChargebackRejected();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitAbsorbChargebackRejected(context);
        }
    },
    ABSORB_CHARGEBACK_APPROVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitAbsorbChargebackApproved();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitAbsorbChargebackApproved(context);
        }
    },
    CHARGEBACK_ABSORBED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitChargebackAbsorbed();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitChargebackAbsorbed(context);
        }
    },
    CHARGEBACK_ABSORB_REVERSED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitChargebackAbsorbedReversed();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitChargebackAbsorbedReversed(context);
        }
    },

    RECOVER_CHARGEBACK_REQUESTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverChargebackRequested();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverChargebackRequested(context);
        }
    },
    RECOVER_CHARGEBACK_REJECTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverChargebackRejected();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverChargebackRejected(context);
        }
    },
    RECOVER_CHARGEBACK_APPROVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverChargebackApproved();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverChargebackApproved(context);
        }
    },
    RECOVER_CHARGEBACK_EVENT_RAISED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverChargebackEventRaised();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverChargebackEventRaised(context);
        }
    },
    RECOVER_CHARGEBACK_EVENT_ACCEPTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverChargebackEventAccepted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverChargebackEventAccepted(context);
        }
    },

    REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitReversalOfRecoveredChargebackRequested();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitReversalOfRecoveredChargebackRequested(context);
        }
    },
    REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitReversalOfRecoveredChargebackApproved();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitReversalOfRecoveredChargebackApproved(context);
        }
    },
    REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitReversalOfRecoveredChargebackEventRaised();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitReversalOfRecoveredChargebackEventRaised(context);
        }
    },
    REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitReversalOfRecoveredChargebackEventAccepted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitReversalOfRecoveredChargebackEventAccepted(context);
        }
    },
    DEBIT_RECEIVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitDebitReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitDebitReceived(context);
        }
    },
    PARTIAL_DEBIT_RECEIVED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPartialDebitReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPartialDebitReceived(context);
        }
    },
    PG_REPRESENTMENT_COMPLETED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPgRepresentmentCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPgRepresentmentCompleted(context);
        }
    },
    PG_ACCEPTANCE_COMPLETED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPgAcceptanceCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPgAcceptanceCompleted(context);
        }
    },
    PG_PARTIAL_REPRESENTMENT_COMPLETED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPgPartialRepresentmentCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPgPartialRepresentmentCompleted(context);
        }
    }, UDIR_COMPLAINT_REJECTED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitUdirComplaintRejected();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitUdirComplaintRejected(context);
        }
    },
    FAILURE {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitFailure();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitFailure(context);
        }
    },
    UDIR_COMPLAINT_ACCEPTED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitUdirComplaintAccepted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitUdirComplaintAccepted(context);
        }
    },
    UDIR_RESPONSE_RECEIVED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitUdirResponseReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitUdirResponseReceived(context);
        }
    },
    UDIR_RESPONSE_NOT_RECEIVED_WITHIN_TTL {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitUdirResponseNotReceived();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitUdirResponseNotReceived(context);
        }
    },
    INTERNAL_MID_REPRESENTMENT_REQUIRED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitInternalMerchantRepresentmentRequired();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitInternalMerchantRepresentmentRequired(context);
        }
    },
    CHARGEBACK_CANCELLED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitChargebackCancelled();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitChargebackCancelled(context);
        }
    },

    // General TOA specific states
    // RECEIVED (states common from chargebacks)
    TOA_BLOCKED_DUE_TO_KS {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaBlockedDueToKs();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaBlockedDueToKs(context);
        }
    },

    // P2PM TOA states
    P2PM_TOA_INITIATED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitP2pmToaInitiated();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitP2pmToaInitiated(context);
        }
    },
    P2PM_TOA_INITIATION_FAILED{
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitP2pmToaInitiationFailed();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitP2pmToaInitiationFailed(context);
        }
    },
    P2PM_TOA_COMPLETED{
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitP2pmToaCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitP2pmToaCompleted(context);
        }
    },
    P2PM_TOA_FAILED{
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitP2pmToaFailed();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitP2pmToaFailed(context);
        }
    },
    P2PM_TOA_PENDING{
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitP2pmToaPending();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitP2pmToaPending(context);
        }
    },
    TOA_COMPLETED_EXTERNALLY{
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitP2pmToaCompletedExternally();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitP2pmToaCompletedExternally(context);
        }
    },
    P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitP2pmToaFailedAfterMaxAutoRetry();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitP2pmToaFailedAfterMaxAutoRetry(context);
        }
    },
    REPRESENTMENT_COMPLETED{
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRepresentedCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRepresentedCompleted(context);
        }
    },
    ACCEPTANCE_COMPLETED{
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitAcceptanceCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitAcceptanceCompleted(context);
        }
    },
    PARTIAL_REPRESENTMENT_COMPLETED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPartialRepresentmentCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPartialRepresentmentCompleted(context);
        }
    },
    FRAUD_REJECTED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitFraudRejected();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitFraudRejected(context);
        }
    },

    SUSPECTED_FRAUD {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitSuspectedFraud();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitSuspectedFraud(context);
        }
    },

    CB_REFUND_CREATED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitCBRefundCreated();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitCBRefundCreated(context);
        }
    },
    CB_REFUND_INITIATED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitCBRefundInitated();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitCBRefundInitated(context);
        }
    },
    CB_REFUND_ACCEPTED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitCBRefundAccepted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitCBRefundAccepted(context);
        }
    },
    CB_REFUND_FAILED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitCBRefundFailed();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitCBRefundFailed(context);
        }
    },
    TOA_OPENED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaOpened();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaOpened(context);
        }
    },
    TOA_INITIATED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaInitiated();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaInitiated(context);
        }
    },
    TOA_INITIATION_FAILED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaInitiationFailed();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaInitiationFailed(context);
        }
    },
    TOA_COMPLETED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaCompleted(context);
        }
    },
    TOA_FAILED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaFailed();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaFailed(context);
        }
    },
    TOA_PENDING {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaPending();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaPending(context);
        }
    },
    TOA_FAILED_AFTER_MAX_AUTO_RETRY {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaFailedAfterMaxAutoRetry();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaFailedAfterMaxAutoRetry(context);
        }
    },
    TOA_CLOSED {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaClosed();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaClosed(context);
        }
    },
    FRAUD_REPRESENTMENT_COMPLETED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitFraudRepresentmentCompleted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitFraudRepresentmentCompleted(context);
        }
    },
    NPCI_ACK_CHARGEBACK {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitNpciAckChargeback();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitNpciAckChargeback(context);
        }
    },
    NPCI_REJECTED_CHARGEBACK {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitNpciRejectedChargeback();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitNpciRejectedChargeback(context);
        }
    },
    PARTIAL_REJECTED_CHARGEBACK{
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPartialRejectedChargeback();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPartialRejectedChargeback(context);
        }
    },
    PARTIAL_ACCEPTED_CHARGEBACK{
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitPartialAcceptedChargeback();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitPartialAcceptedChargeback(context);
        }
    },
    FULLY_ACCEPTED_CHARGEBACK{
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitFullyAcceptedChargeback();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitFullyAcceptedChargeback(context);
        }
    },
    ACCEPTED_CHARGEBACK{
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor){
            return visitor.visitAcceptedChargeback();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitAcceptedChargeback(context);
        }
    },
    REJECTED_CHARGEBACK{
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor){
            return visitor.visitRejectedChargeback();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRejectedChargeback(context);
        }
    },
    CB_REFUND_INITIATED_COMPLETED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitCbRefundInitiationCompleted();
        }

        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor,
                               R context) {
            return visitor.visitCbRefundInitiationCompleted(context);
        }
    },
    TOA_UNABLE_TO_PROCESS {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitToaUnableToProcess();
        }

        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitToaUnableToProcess(context);
        }
    },
    CB_REFUND_PROCESSED_EXTERNALLY {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitCbRefundProcessedExternally();
        }

        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitCbRefundProcessedExternally(context);
        }
    },
    HOLD {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitHold();
        }

        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitHold(context);
        }
    },
    END {
        @Override
        public <T> T accept(DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitEnd();
        }

        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitEnd(context);
        }
    },
    RECOVER_HOLD_EVENT_RAISED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverHoldEventRaised();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverHoldEventRaised(context);
        }
    },
    RECOVER_HOLD_EVENT_ACCEPTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverHoldEventAccepted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverHoldEventAccepted(context);
        }
    },
    REVERSAL_OF_RECOVERED_HOLD_EVENT_RAISED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitReversalOfRecoveredHoldEventRaised();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitReversalOfRecoveredHoldEventRaised(context);
        }
    },
    REVERSAL_OF_RECOVERED_HOLD_EVENT_ACCEPTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitReversalOfRecoveredHoldEventAccepted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitReversalOfRecoveredHoldEventAccepted(context);
        }
    },
    RECOVER_PARTIAL_HOLD_EVENT_RAISED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverPartialHoldEventRaised();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverPartialHoldEventRaised(context);
        }
    },
    RECOVER_PARTIAL_HOLD_EVENT_ACCEPTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitRecoverPartialHoldEventAccepted();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitRecoverPartialHoldEventAccepted(context);
        }
    },
    MERCHANT_ACTION_REQUESTED {
        @Override
        public <T> T accept(final DisputeWorkflowStateVisitor<T> visitor) {
            return visitor.visitMerchantActionRequested();
        }
        @Override
        public <T, R> T accept(DisputeWorkflowStateVisitorWithConxtext<T, R> visitor, R context) {
            return visitor.visitMerchantActionRequested(context);
        }
    },

    ;
    public abstract <T> T accept(DisputeWorkflowStateVisitor<T> visitor);

    public abstract <T,R> T accept(DisputeWorkflowStateVisitorWithConxtext<T,R> visitor, R context);
}
