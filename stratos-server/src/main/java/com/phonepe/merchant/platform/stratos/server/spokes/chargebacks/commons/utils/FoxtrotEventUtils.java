package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils;

import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursement;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.PenaltyProbableRequest;
import com.phonepe.central.stratos.penalty.server.queue.messages.EscalatedEntityNotificationQueueMessage;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEventType;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.responses.FileUploadResponse;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.events.BaseEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.PulseStatus;
import com.phonepe.merchant.platform.stratos.server.core.events.type.AccountingEventStatus;
import com.phonepe.merchant.platform.stratos.server.core.events.type.CallbackEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeActionFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeActionSuccessEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeSignalEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.FoxtrotChargebackAccountingEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.KratosRecommendedActionEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.P2pmToaPulseEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.RefundActionEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.SidelineSkippedEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.ToaRetryEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.ToaStatusEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.escalation.EscalationFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.notification.NotificationFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyClassEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyClassFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyClassSuccessEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyDisbursementEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyDisbursementFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyDisbursementSuccessEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyInstanceEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyInstanceGrowthEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyProbableEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyProbableReconcileFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyProbableRegisterFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyRecoveryEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyRecoveryFailureEvent;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.FileState;
import com.phonepe.merchant.platform.stratos.server.core.models.FileType;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.PrimusFileState;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.CallbackMessage;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.ToaProcessorMessage;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.events.EOFEvent;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.events.FileProcessStateEvent;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.events.FileUploadEvent;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.events.UdirOutgoingCallBackEvent;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.events.UdirOutgoingComplaintRequestEvent;
import com.phonepe.models.payments.upi.udircomplaint.UdirOutgoingComplaintRequest;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import com.phonepe.ruleengine.model.integration.FraudAction;
import java.util.Date;
import java.util.Optional;
import lombok.experimental.UtilityClass;
import lombok.val;

@UtilityClass
public class FoxtrotEventUtils {

    public FoxtrotChargebackAccountingEvent toChargebackAccountingEvent(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow,
        final AccountingEvent accountingEvent,
        final AccountingEventStatus accountingEventStatus) {

        return toChargebackAccountingEvent(
            dispute, disputeWorkflow,
            accountingEvent.getHeader().getEventType(),
            accountingEvent.getHeader().getTransactionId(),
            accountingEvent.getTransaction().getAmount(),
            accountingEventStatus);
    }

    public FoxtrotChargebackAccountingEvent toChargebackAccountingEvent(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow,
        final AccountingEventType eventType,
        final String eventId,
        final long eventAmount,
        final AccountingEventStatus accountingEventStatus) {

        return FoxtrotChargebackAccountingEvent.builder()
            .disputeId(dispute.getDisputeId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion())
            .disputeType(dispute.getDisputeType())
            .disputeStage(disputeWorkflow.getDisputeStage())
            .transactionReferenceId(dispute.getTransactionReferenceId())
            .merchantId(dispute.getMerchantId())
            .merchantTransactionId(dispute.getMerchantTransactionId())
            .instrumentTransactionId(dispute.getInstrumentTransactionId())
            .disputeReferenceId(dispute.getDisputeReferenceId())
            .rrn(dispute.getRrn())
            .transactionAmount(dispute.getTransactionAmount())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .chargebackAccountingEventType(eventType)
            .chargebackAccountingEventId(eventId)
            .chargebackAccountingEventAmount(eventAmount)
            .accountingEventStatus(accountingEventStatus)
            .build();
    }

    public DisputeActionSuccessEvent toDisputeActionSuccessEvent(
        final Dispute updatedDispute,
        final DisputeWorkflow updatedDisputeWorkflow, final DisputeWorkflowState fromState) {

        return DisputeActionSuccessEvent.builder()
            .disputeAmount(updatedDisputeWorkflow.getDisputedAmount())
            .disputeCategory(updatedDispute.getDisputeCategory())
            .acceptedAmount(Optional.of(updatedDisputeWorkflow)
                .filter(FinancialDisputeWorkflow.class::isInstance)
                .map(
                    disputeWorkflow -> ((FinancialDisputeWorkflow) disputeWorkflow).getAcceptedAmount())
                .orElse(0L)
            )
            .disputeIssuer(updatedDispute.getDisputeIssuer())
            .disputeSourceId(updatedDisputeWorkflow.getDisputeSourceId())
            .disputeWorkflowEvent(updatedDisputeWorkflow.getCurrentEvent())
            .fromState(fromState)
            .toState(updatedDisputeWorkflow.getCurrentState())
            .disputeId(updatedDispute.getDisputeId())
            .disputeStage(updatedDisputeWorkflow.getDisputeStage())
            .disputeType(updatedDisputeWorkflow.getDisputeType())
            .merchantId(updatedDispute.getMerchantId())
            .disputeWorkflowId(updatedDisputeWorkflow.getDisputeWorkflowId())
            .gandalfUserId(updatedDisputeWorkflow.getGandalfUserId())
            .disputeReferenceId(updatedDispute.getDisputeReferenceId())
            .disputeWorkflowVersion(updatedDisputeWorkflow.getDisputeWorkflowVersion())
            .transactionAmount(updatedDispute.getTransactionAmount())
            .instrumentTransactionId(updatedDispute.getInstrumentTransactionId())
            .transactionReferenceId(updatedDisputeWorkflow.getTransactionReferenceId())
            .build();
    }

    public DisputeActionFailureEvent toDisputeActionFailedEvent(
            final DisputeWorkflow updatedDisputeWorkflow, final DisputeWorkflowState toState,String errorContext,String errorCode) {

        return DisputeActionFailureEvent.builder()
                .transactionReferenceId(updatedDisputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(updatedDisputeWorkflow.getDisputeWorkflowId())
                .disputeWorkflowEvent(updatedDisputeWorkflow.getCurrentEvent())
                .fromState(updatedDisputeWorkflow.getCurrentState())
                .toState(toState)
                .merchantId(updatedDisputeWorkflow.getDispute().getMerchantId())
                .errorContext(errorContext)
                .errorCode(errorCode)
                .build();
    }

    public UdirOutgoingComplaintRequestEvent toUdirOutgoingComplaintRequestEvent(
        final UdirOutgoingComplaintRequest complaintRequest) {
        return UdirOutgoingComplaintRequestEvent.builder()
            .complaintId(complaintRequest.getComplaintId())
            .requestType(complaintRequest.getRequestType())
            .adjAmount(complaintRequest.getAdjAmount())
            .transactionReferenceId(complaintRequest.getTransactionId())
            .requestDate(complaintRequest.getRequestDate())
            .build();
    }

    public UdirOutgoingCallBackEvent toUdirOutgoingCallBackEvent(
        final UPIClientOutgoingComplaintResponse upiClientOutgoingComplaintResponse,
        final String transactionReferenceId
    ) {

        return UdirOutgoingCallBackEvent.builder()
            .complaintId(upiClientOutgoingComplaintResponse.getComplaintId())
            .state(upiClientOutgoingComplaintResponse.getState())
            .adjFlag(upiClientOutgoingComplaintResponse.getAdjFlag())
            .adjCode(upiClientOutgoingComplaintResponse.getAdjCode())
            .transactionReferenceId(transactionReferenceId)
            .type(upiClientOutgoingComplaintResponse.getType())
            .success(upiClientOutgoingComplaintResponse.isSuccess())
            .resultType(upiClientOutgoingComplaintResponse.getResultType())
            .errorCode(upiClientOutgoingComplaintResponse.getErrorCode())
            .requestDate(upiClientOutgoingComplaintResponse.getRequestDate())
            .crn(upiClientOutgoingComplaintResponse.getCrn())
            .adjRemark(upiClientOutgoingComplaintResponse.getAdjRemark())
            .adjReason(upiClientOutgoingComplaintResponse.getAdjReason())
            .respAdjAmount(upiClientOutgoingComplaintResponse.getRespAdjAmount())
            .build();
    }

    public static BaseEvent toToaPulseEvent(ToaProcessorMessage toaProcessorMessage, PulseStatus pulseStatus, StratosErrorCodeKey stratosErrorCode) {
        return P2pmToaPulseEvent.builder()
            .pulseEventId(toaProcessorMessage.getEventId())
            .errorCode(stratosErrorCode)
            .transactionReferenceId(toaProcessorMessage.getTransactionId())
            .pulseStatus(pulseStatus)
            .build();
    }

    public static BaseEvent toToaRetryEvent(DisputeWorkflow disputeWorkflow, UserAuthDetails userAuthDetails, long retryCount) {
        return ToaRetryEvent.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeId(disputeWorkflow.getDisputeId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .currentDisputeEvent(disputeWorkflow.getCurrentEvent())
            .disputeType(disputeWorkflow.getDisputeType())
            .disputeAmount(disputeWorkflow.getDisputedAmount())
            .gandalfUserId(userAuthDetails.getUserDetails().getUserId())
            .retryCount(retryCount)
            .build();
    }
    public FileUploadEvent toFileUploadEvent(
        final FileUploadResponse fileUploadResponse,
        final FileType fileType,
        final int fileRows,
        final FileState fileState,
        final DisputeType disputeType,
        final UserDetails userDetails,String message){

        return FileUploadEvent.builder()
            .fileId(Optional.ofNullable(fileUploadResponse.getId()).orElse("Null"))
            .fileName(fileUploadResponse.getFileName())
            .fileType(fileType)
            .fileRows(fileRows)
            .fileState(fileState)
            .disputeType(disputeType)
            .userId(userDetails.getUserId())
            .userType(AuthorizationUtils.getUserType(userDetails))
            .message(message)
            .build();

    }

    public FileProcessStateEvent toFileProcessStateEvent(
        File file
    ){
        return FileProcessStateEvent.builder()
            .fileId(file.getFileId())
            .fileName(file.getFileName())
            .fileState(file.getFileState())
            .fileType(file.getFileType())
            .disputeType(file.getDisputeType())
            .createdAt(file.getCreatedAt())
            .updatedAt(file.getUpdatedAt())
            .build();
    }

    public static BaseEvent toSidelineSkippedEvent(StratosErrorCodeKey errorCode, String message, ActionType actionType) {
        return SidelineSkippedEvent.builder()
            .errorCode(errorCode)
            .message(message)
            .actionType(actionType)
            .build();
    }

    public EOFEvent toEOFEvent(
        File file, String reason, PrimusFileState primusFileState
    ){
        return EOFEvent.builder()
            .fileId(file.getFileId())
            .fileName(file.getFileName())
            .fileType(file.getFileType())
            .disputeType(file.getDisputeType())
            .createdAt(file.getCreatedAt())
            .updatedAt(file.getUpdatedAt())
            .reason(reason)
            .primusFileState(primusFileState)
            .build();
    }

    public KratosRecommendedActionEvent toKratosRecommendedActionEvent(DisputeWorkflow disputeWorkflow,
        KratosRecommendedAction recommendedAction, FraudAction fraudAction, String requestId){
        return KratosRecommendedActionEvent.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .requestId(requestId)
            .responseCode(TransformationUtils.getSafeEnumName(fraudAction.getResponseCode()))
            .recommendedAction(recommendedAction)
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .transactionAmount(disputeWorkflow.getDisputedAmount())
            .currentEvent(disputeWorkflow.getCurrentEvent())
            .currentState(disputeWorkflow.getCurrentState())
            .disputeType(disputeWorkflow.getDisputeType())
            .disputeStage(disputeWorkflow.getDisputeStage())
            .build();
    }



    public RefundActionEvent toRefundActionEvent(
        String disputeWorkflowId, String transactionReferenceId,String refundId,
        String refundAction,String errorCode, String errorContext
    ){
        return RefundActionEvent.builder()
            .refundId(refundId)
            .refundAction(refundAction)
            .errorContext(errorContext)
            .errorCode(errorCode)
            .disputeWorkflowId(disputeWorkflowId)
            .transactionReferenceId(transactionReferenceId)
            .build();
    }

    public ToaStatusEvent toToaStatusEvent(final DisputeWorkflow disputeWorkflow, final String toaState) {
        val dispute = disputeWorkflow.getDispute();
        return ToaStatusEvent.builder()
                .communicationId(dispute.getDisputeReferenceId())
                .toaState(toaState)
                .amount(disputeWorkflow.getDisputedAmount())
                .creationDate(dispute.getCreatedAt().toString())
                .updationDate(dispute.getUpdatedAt().toString())
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .build();
    }

    public BaseEvent toCallbackEvent(
            CallbackMessage message, String identifier, String serviceId, boolean success) {
        Dispute dispute = message.getDispute();
        return CallbackEvent.builder()
            .callbackType(message.getCallbackType())
            .success(success)
            .previousState(message.getPreviousState())
            .currentStatus(message.getCurrentStatus())
            .disputeId(dispute.getDisputeId())
            .transactionReferenceId(dispute.getTransactionReferenceId())
            .disputeType(dispute.getDisputeType())
            .currentDisputeStage(dispute.getCurrentDisputeStage())
            .merchantId(dispute.getMerchantId())
            .entityIdentifier(identifier)
            .serviceId(serviceId)
            .eventTime(message.getEventTime())
            .build();
    }

    public BaseEvent toSignalEvent(
            long signalAmount, DisputeSignalEvent.SignalType signalType,String disputeId, DisputeStage disputeStage,
            DisputeType disputeType, String disputeWorkflowId, String transactionReferenceId,
            DisputeWorkflowState currentState,DisputeWorkflowEvent workflowEvent

    ) {
        return DisputeSignalEvent.builder()
                .signalAmount(signalAmount)
                .signalType(signalType)
                .disputeId(disputeId)
                .disputeStage(disputeStage)
                .disputeType(disputeType)
                .disputeWorkflowId(disputeWorkflowId)
                .transactionReferenceId(transactionReferenceId)
                .workflowEvent(workflowEvent)
                .fromState(currentState)
                .build();
    }

    public BaseEvent getSignalEvent(DisputeWorkflow storedDisputeWorkflow,DisputeWorkflowEvent workflowEvent,DisputeSignalEvent.SignalType signalType) {
        return FoxtrotEventUtils.toSignalEvent(storedDisputeWorkflow.getDisputedAmount(), signalType,
                storedDisputeWorkflow.getDisputeId(), storedDisputeWorkflow.getDisputeStage(),
                storedDisputeWorkflow.getDisputeType(), storedDisputeWorkflow.getDisputeWorkflowId(),
                storedDisputeWorkflow.getTransactionReferenceId(), storedDisputeWorkflow.getCurrentState(), workflowEvent);
    }

    public BaseEvent getNotificationFailureEvent(final TenantInfo tenantInfo,
                                                 final String penaltyClassId,
                                                 final DisputeException exception) {
        return NotificationFailureEvent.builder()
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .penaltyClassId(penaltyClassId)
                .errorCode(exception.getErrorCode()
                        .name())
                .message(exception.getMessage())
                .build();
    }

    public PenaltyClassEvent getPenaltyClassSuccessEvent(final PenaltyClass penaltyClass) {
        return PenaltyClassSuccessEvent.builder()
                .name(penaltyClass.getName())
                .penaltyClassId(penaltyClass.getId())
                .penaltyClassState(penaltyClass.getState())
                .tenantName(penaltyClass.getTenant()
                        .getName())
                .tenantSubCategory(penaltyClass.getTenant()
                        .getSubCategory())
                .build();
    }


    public PenaltyClassEvent getPenaltyClassFailureEvent(final PenaltyClass penaltyClass,
                                                         final DisputeException disputeException) {
        return PenaltyClassFailureEvent.builder()
                .name(penaltyClass.getName())
                .tenantName(penaltyClass.getTenant()
                        .getName())
                .tenantSubCategory(penaltyClass.getTenant()
                        .getSubCategory())
                .errorCode(disputeException.getErrorCode()
                        .name())
                .message(disputeException.getMessage())
                .build();
    }

    public PenaltyProbableEvent getProbableRegisterFailureEvent(final TenantInfo tenantInfo,
                                                                final PenaltyProbableRequest penaltyProbableRequest,
                                                                final DisputeException exception) {
        return PenaltyProbableRegisterFailureEvent.builder()
                .penaltyClassId(penaltyProbableRequest.getPenaltyClassId())
                .transactionId(penaltyProbableRequest.getTransactionContext()
                        .getTransactionId())
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .beneficiaryId(penaltyProbableRequest.getBeneficiary()
                        .getId())
                .beneficiaryType(penaltyProbableRequest.getBeneficiary()
                        .getType())
                .transactionId(penaltyProbableRequest.getTransactionContext()
                        .getTransactionId())
                .transactionAmount(penaltyProbableRequest.getTransactionContext()
                        .getTransactionAmount())
                .dueDate(penaltyProbableRequest.getDueDate())
                .errorCode(exception.getErrorCode()
                        .name())
                .message(exception.getMessage())
                .build();
    }

    public PenaltyProbableEvent getPenaltyProbableReconcileFailureEvent(final TenantInfo tenantInfo,
                                                                        final PenaltyProbable probableEntity,
                                                                        final DisputeException disputeException) {
        return PenaltyProbableReconcileFailureEvent.builder()
                .penaltyClassId(probableEntity.getPenaltyClassId())
                .penaltyProbableId(probableEntity.getProbableId())
                .transactionId(probableEntity.getTransactionId())
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .beneficiaryId(probableEntity.getBeneficiary()
                        .getId())
                .beneficiaryType(probableEntity.getBeneficiary()
                        .getType())
                .transactionAmount(probableEntity.getTransactionAmount())
                .dueDate(probableEntity.getDueDate())
                .errorCode(disputeException.getErrorCode()
                        .name())
                .message(disputeException.getMessage())
                .build();
    }

    public BaseEvent getPenaltyCreationEvent(final TenantInfo tenantInfo,
                                             final Penalty penalty) {
        return PenaltyInstanceEvent.builder()
                .penaltyClassId(penalty.getPenaltyClassId())
                .penaltyId(penalty.getPenaltyId())
                .penaltyStatus(penalty.getStatus())
                .transactionId(penalty.getTransactionId())
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .beneficiaryId(penalty.getBeneficiary()
                        .getId())
                .beneficiaryType(penalty.getBeneficiary()
                        .getType())
                .transactionAmount(penalty.getTransactionAmount())
                .initialPenaltyAmount(penalty.getInitialPenaltyAmount())
                .build();

    }

    public BaseEvent getPenaltyGrowthEvent(final TenantInfo tenantInfo,
                                             final Penalty penalty,final Date nextGrowthDate) {
        return PenaltyInstanceGrowthEvent.builder()
                .penaltyClassId(penalty.getPenaltyClassId())
                .penaltyId(penalty.getPenaltyId())
                .penaltyStatus(penalty.getStatus())
                .transactionId(penalty.getTransactionId())
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .beneficiaryId(penalty.getBeneficiary()
                        .getId())
                .beneficiaryType(penalty.getBeneficiary()
                        .getType())
                .transactionAmount(penalty.getTransactionAmount())
                .initialPenaltyAmount(penalty.getInitialPenaltyAmount())
                .nextGrowthDate(nextGrowthDate)
                .finalPenaltyAmount(penalty.getFinalPenaltyAmount())
                .build();

    }

    public BaseEvent getEscalationFailureEvent(final TenantInfo tenantInfo,
                                               final EscalatedEntityNotificationQueueMessage escalatedEntityNotificationQueueMessage,
                                               final DisputeException disputeException) {
        return EscalationFailureEvent.builder()
                .mappingId(escalatedEntityNotificationQueueMessage.getMappingId())
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .escalationType(escalatedEntityNotificationQueueMessage.getEscalationType())
                .escalationLevel(escalatedEntityNotificationQueueMessage.getEscalationLevel())
                .errorCode(disputeException.getErrorCode()
                        .name())
                .message(disputeException.getMessage())
                .build();
    }

    public PenaltyDisbursementEvent getPenaltyDisbursementSuccessEvent(final TenantInfo tenantInfo,
                                                                       final PenaltyDisbursement penaltyDisbursement) {
        return PenaltyDisbursementSuccessEvent.builder()
                .penaltyClassId(penaltyDisbursement.getPenaltyClassId())
                .penaltyId(penaltyDisbursement.getPenaltyId())
                .transactionId(penaltyDisbursement.getTransactionId())
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .disbursementId(penaltyDisbursement.getDisbursementId())
                .beneficiaryId(penaltyDisbursement.getBeneficiaryId())
                .disbursementTransactionId(penaltyDisbursement.getDisbursementTransactionId())
                .disbursedAmount(penaltyDisbursement.getDisbursedAmount())
                .penaltyDisbursementMode(penaltyDisbursement.getDisbursementMode())
                .penaltyDisbursementState(penaltyDisbursement.getStatus())
                .build();
    }

    public PenaltyDisbursementEvent getPenaltyDisbursementFailureEvent(final TenantInfo tenantInfo,
                                                                       final PenaltyDisbursement penaltyDisbursement,
                                                                       final DisputeException exception) {
        return PenaltyDisbursementFailureEvent.builder()
                .penaltyClassId(penaltyDisbursement.getPenaltyClassId())
                .penaltyId(penaltyDisbursement.getPenaltyId())
                .transactionId(penaltyDisbursement.getTransactionId())
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .disbursementId(penaltyDisbursement.getDisbursementId())
                .beneficiaryId(penaltyDisbursement.getBeneficiaryId())
                .disbursedAmount(penaltyDisbursement.getDisbursedAmount())
                .penaltyDisbursementMode(penaltyDisbursement.getDisbursementMode())
                .penaltyDisbursementState(penaltyDisbursement.getStatus())
                .errorCode(exception.getErrorCode()
                        .name())
                .message(exception.getMessage())
                .build();
    }


    public PenaltyRecoveryEvent getPenaltyRecoveryFailureEvent(final Penalty penalty,
                                                               final String penaltyDisbursementId,
                                                               final Long amountToBeRecovered,
                                                               final TenantInfo tenantInfo,
                                                               final DisputeException exception) {
        return PenaltyRecoveryFailureEvent.builder()
                .penaltyClassId(penalty.getPenaltyClassId())
                .penaltyId(penalty.getPenaltyId())
                .transactionId(penalty.getTransactionId())
                .disbursementId(penaltyDisbursementId)
                .amountToBeRecovered(amountToBeRecovered)
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .errorCode(exception.getErrorCode()
                        .name())
                .message(exception.getMessage())
                .build();
    }
}
