package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class RefundCreationAction extends UpdateDisputeStateBaseAction {

    private final PaymentsService paymentsService;
    private final Provider<StateChangeHandlerActor> stateChangeHandlerProvider;

    @Inject
    public RefundCreationAction(
        DisputeService disputeService,
        DisputeWorkflowRepository disputeWorkflowRepository,
        EventIngester eventIngester, PaymentsService paymentsService,
        Provider<StateChangeHandlerActor> stateChangeHandlerProvider,
        CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);

        this.paymentsService = paymentsService;
        this.stateChangeHandlerProvider = stateChangeHandlerProvider;
    }

    @Override
    @SneakyThrows
    protected void preTransition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var transactionId = stateContext.getExtendedState()
            .get(Fields.transactionReferenceId, String.class);

         paymentsService.unblockReversals(transactionId);

    }

    @Override
    @SneakyThrows
    protected void transition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var disputeWorkflowMessage = DisputeWorkflowMessage.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowEvent(DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND)
            .build();

        stateChangeHandlerProvider.get().publish(disputeWorkflowMessage);
    }

}
