package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.NonFinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVisitor;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds.ChargebackRecoveryFeedPublishActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import io.appform.dropwizard.actors.actor.Actor;
import lombok.SneakyThrows;
import java.util.List;
import lombok.experimental.UtilityClass;

import java.util.Map;

@UtilityClass
public class DisputeWorkflowUtils {

    public FinancialDisputeWorkflow getFinancialDisputeWorkflow(DisputeWorkflow disputeWorkflow) {
        return disputeWorkflow.accept(
            new DisputeWorkflowVisitor<>() {

                @Override
                public FinancialDisputeWorkflow visit(
                    FinancialDisputeWorkflow financialDisputeWorkflow1) {
                    return financialDisputeWorkflow1;
                }

                @Override
                public FinancialDisputeWorkflow visit(
                    NonFinancialDisputeWorkflow nonFinancialDisputeWorkflow) {
                    throw DisputeExceptionUtil.error(StratosErrorCodeKey.OPERATION_NOT_ALLOWED, Map.of(
                        Constants.MESSAGE, "Financial DisputeWorkflow Required"));
                }
            });
    }
    @SneakyThrows
    public void closeWorkflow(
            final DisputeWorkflow workflow,
            final StateChangeHandlerActor stateChangeHandlerActor) {
        final DisputeWorkflowMessage msg = DisputeWorkflowMessage.builder()
                .disputeWorkflowId(workflow.getDisputeWorkflowId())
                .disputeWorkflowEvent(DisputeWorkflowEvent.END_WORKFLOW)
                .transactionReferenceId(workflow.getTransactionReferenceId())
                .build();
        stateChangeHandlerActor.publish(msg);
    }

    @SneakyThrows
    public void merchantRecovery(
            final DisputeWorkflow currentStagedisputeWorkflow,
            final Actor<ActionType, DisputeWorkflowMessage> raiseAccountingEventActor,
            final ChargebackRecoveryFeedPublishActor chargebackRecoveryFeedPublishActor) {
        final DisputeWorkflowMessage disputeWorkflowMessage = DisputeWorkflowMessage.builder()
                .transactionReferenceId(currentStagedisputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(currentStagedisputeWorkflow.getDisputeWorkflowId())
                .build();
        chargebackRecoveryFeedPublishActor.publish(disputeWorkflowMessage);
        raiseAccountingEventActor.publish(disputeWorkflowMessage);
    }

    public boolean ignoreSignal(
            Map<DisputeType,Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> ignoreSignalStateConfig,
            DisputeWorkflow disputeWorkflow){
        if( ignoreSignalStateConfig == null ||
            !ignoreSignalStateConfig.containsKey(disputeWorkflow.getDisputeType()) ||
            !ignoreSignalStateConfig.get(disputeWorkflow.getDisputeType()).containsKey(disputeWorkflow.getDisputeWorkflowVersion()))
            return false;
        return ignoreSignalStateConfig.get(disputeWorkflow.getDisputeType())
                .get(disputeWorkflow.getDisputeWorkflowVersion())
                .contains(disputeWorkflow.getCurrentState());

    }
}
