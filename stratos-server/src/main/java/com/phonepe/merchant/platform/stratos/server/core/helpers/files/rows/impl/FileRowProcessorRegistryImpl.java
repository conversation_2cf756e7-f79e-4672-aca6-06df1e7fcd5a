package com.phonepe.merchant.platform.stratos.server.core.helpers.files.rows.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.helpers.files.rows.FileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.core.helpers.files.rows.FileRowProcessorRegistry;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.FileType;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.FileUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.files.EdcFileRowProcessorImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.files.NetBankingFileRowProcessorImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.files.PgFileRowProcessorImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.files.UpiFileRowProcessorImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.files.WalletFileRowProcessorImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.bbps.files.BBPSToaFileRowProcessorImpl;

import java.util.Map;
import java.util.Optional;

@Singleton
public class FileRowProcessorRegistryImpl implements FileRowProcessorRegistry {

    private final Map<String, FileRowProcessor> handlers;

    @Inject
    public FileRowProcessorRegistryImpl(final UpiFileRowProcessorImpl upiFileRowProcessor,
        final PgFileRowProcessorImpl pgFileRowProcessor,
        final EdcFileRowProcessorImpl edcFileRowProcessor,
        final NetBankingFileRowProcessorImpl netbankingFileRowProcessor,
        final BBPSToaFileRowProcessorImpl bbpsToaFileRowProcessor,
        final WalletFileRowProcessorImpl walletFileRowProcessor) {
        handlers = Map.ofEntries(
            Map.entry(FileUtils.fileIdentifier(DisputeType.UPI_CHARGEBACK, FileType.YES),
                upiFileRowProcessor),
            Map.entry(FileUtils.fileIdentifier(DisputeType.UPI_CHARGEBACK, FileType.APP),
                upiFileRowProcessor),
            Map.entry(FileUtils.fileIdentifier(DisputeType.UPI_CHARGEBACK, FileType.ICP),
                upiFileRowProcessor),
            Map.entry(
                FileUtils.fileIdentifier(DisputeType.PG_CHARGEBACK, FileType.PG_FIRST_LEVEL),
                pgFileRowProcessor),
            Map.entry(
                FileUtils.fileIdentifier(DisputeType.PG_CHARGEBACK, FileType.PG_PRE_ARBITRATION),
                pgFileRowProcessor),
            Map.entry(
                FileUtils.fileIdentifier(DisputeType.EDC_CHARGEBACK, FileType.EDC_FIRST_LEVEL),
                edcFileRowProcessor),
            Map.entry(
                FileUtils.fileIdentifier(DisputeType.NB_CHARGEBACK, FileType.NB_FIRST_LEVEL),
                netbankingFileRowProcessor),
            Map.entry(FileUtils.fileIdentifier(DisputeType.BBPS_TAT_BREACH_TOA, FileType.BBPS_TOA),
                bbpsToaFileRowProcessor),
            Map.entry(FileUtils.fileIdentifier(DisputeType.WALLET_CHARGEBACK, FileType.PHP),
                walletFileRowProcessor)
        );
    }

    @Override
    public FileRowProcessor getProcessor(final DisputeType disputeType, final FileType fileType) {
        return Optional.ofNullable(handlers.get(FileUtils.fileIdentifier(disputeType, fileType)))
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_FILE,
                Map.of(Constants.MESSAGE, "No Row Processor for " + disputeType + " " + fileType)
            ));
    }
}
