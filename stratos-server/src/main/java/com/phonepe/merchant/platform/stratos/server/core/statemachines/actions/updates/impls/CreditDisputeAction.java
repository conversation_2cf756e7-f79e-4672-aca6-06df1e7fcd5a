package com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;


@Slf4j
@Singleton
public class CreditDisputeAction extends UpdateDisputeStateBaseAction {

    @Inject
    public CreditDisputeAction(
        final DisputeService disputeService,
        final DisputeWorkflowRepository disputeWorkflowRepository,
        final EventIngester eventIngester,
        final CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
    }

    @Override
    protected void transition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        disputeService.creditDisputeAction(disputeWorkflow, stateContext);
    }
}
