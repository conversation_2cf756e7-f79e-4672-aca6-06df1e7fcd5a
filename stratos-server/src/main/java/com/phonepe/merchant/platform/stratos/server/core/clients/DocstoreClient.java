package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.DocstoreClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.docstore.model.request.documents.publicfile.PublicDefaultUploadMetaData;
import com.phonepe.platform.docstore.model.request.documents.upload.FileUploadRequest;
import com.phonepe.platform.docstore.model.request.documents.upload.context.Models.TTLConfig;
import com.phonepe.platform.docstore.model.request.documents.upload.context.Models.TTLPriority;
import com.phonepe.platform.docstore.model.request.documents.upload.context.fileupload.InternalFileUploadContext;
import com.phonepe.platform.docstore.model.request.documents.upload.context.fileupload.PublicFileUploadContext;
import com.phonepe.platform.docstore.model.response.DocStoreUploadResponse;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import com.phonepe.platform.http.v2.executor.httpdata.MultiPartHttpData;
import com.phonepe.platform.http.v2.executor.httpdata.MultiPartHttpData.FilePart;
import com.phonepe.platform.http.v2.executor.httpdata.MultiPartHttpData.FormPart;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;

@Slf4j
@Singleton
public class DocstoreClient {

    private static final String NAMESPACE = "stratos";
    private static final long FILE_TTL_TWO_YEARS = 2 * 365 * 24 * 60 * 60L;
    private final OlympusIMClient olympusIMClient;

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;

    @Inject
    public DocstoreClient(@DocstoreClientConfig final HttpConfiguration httpConfiguration,
        final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
        final ObjectMapper mapper,
        final MetricRegistry metricRegistry,
        final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(
            DocstoreClient.class, httpConfiguration,
            serviceEndpointProviderFactory, mapper, metricRegistry);
        this.olympusIMClient = olympusIMClient;
    }


    public byte[] getFile(final String fileId) {

        final var url = String.format("/v1/documents/%s?download=true", fileId);

        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "getFile", url, byte[].class, olympusIMClient);
    }

    public DocStoreUploadResponse pushFile(final byte[] file, final String fileName) {

        final var url = String.format("/v1/documents/%s", NAMESPACE);

        final var fileUploadRequest = MultiPartHttpData.builder()
            .mediaType(MultipartBody.FORM.toString())
            .filePart(new FilePart("file", fileName,
                MediaType.parse("application/octet-stream"),
                file
            ))
            .formPart(new FormPart("fileUploadRequest",
                MapperUtils.serializeToString(FileUploadRequest
                    .builder()
                    .namespace(NAMESPACE)
                    .fileUploadContext(InternalFileUploadContext.builder()
                        .build())
                    .ttlConfig(TTLConfig.builder()
                        .timeToLiveInSec(FILE_TTL_TWO_YEARS)
                        .priority(TTLPriority.LOW)
                        .build())
                    .build())))
            .multipartType("form-data")
            .build();

        return HttpClientUtils.executePost(
            httpExecutorBuilderFactory, "fileUpload", url, fileUploadRequest,
            DocStoreUploadResponse.class, olympusIMClient);
    }

    //this is supported on v2 endpoint of docstore
    public DocStoreUploadResponse pushFilePublic(byte[] file, String fileName) {

        final var url = String.format("/v2/documents/%s", NAMESPACE);

        final var fileUploadRequest = MultiPartHttpData.builder()
                .mediaType(MultipartBody.FORM.toString())
                .filePart(new FilePart("file", fileName,
                        MediaType.parse("application/octet-stream"),
                        file
                ))
                .formPart(new FormPart("fileUploadRequest",
                        MapperUtils.serializeToString(FileUploadRequest
                                .builder()
                                .namespace(NAMESPACE)
                                .fileUploadContext(PublicFileUploadContext.builder()
                                        .metaData(PublicDefaultUploadMetaData.builder().build())
                                        .build())
                                .ttlConfig(TTLConfig.builder()
                                        .timeToLiveInSec(FILE_TTL_TWO_YEARS)
                                        .priority(TTLPriority.LOW)
                                        .build())
                                .build())))
                .multipartType("form-data")
                .build();

        return HttpClientUtils.executePost(
                httpExecutorBuilderFactory, "publicFileUpload", url, fileUploadRequest,
                DocStoreUploadResponse.class, olympusIMClient);
    }
}
