package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.payments.common.party.PartyVisitor;
import com.phonepe.models.payments.common.party.impl.CardNetworkAcquirerParty;
import com.phonepe.models.payments.common.party.impl.ExternalUserParty;
import com.phonepe.models.payments.common.party.impl.InternalUserParty;
import com.phonepe.models.payments.common.party.impl.MerchantParty;
import com.phonepe.models.payments.common.party.impl.MerchantUserParty;
import com.phonepe.models.payments.common.party.impl.UnknownParty;
import com.phonepe.models.payments.pay.ReceivedPayment;
import com.phonepe.models.payments.pay.context.TransferMode;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Builder
@AllArgsConstructor
public class PartyMerchantIdVisitor implements PartyVisitor<String> {

    private static final Set<TransferMode> WALLET_TOPUP_MODES = Set.of(TransferMode.WALLET_TOPUP,
        TransferMode.WALLET_APP_TOPUP);
    private ReceivedPayment receivedPayment;
    private static final String UNSUPPORTED_PAYEE_MESSAGE = "Unsupported payee ";

    @Override
    public String visit(final InternalUserParty internalUserParty) {
        // Handle old wallet-topup flow (that was being handled at payments hence doesnt have a merchant payer)
        // Sample Txn Id P2108280712137600027935
        return Optional.ofNullable(receivedPayment)
            .map(ReceivedPayment::getContext)
            .filter(context -> WALLET_TOPUP_MODES.contains(context.getTransferMode()))
            .map(context -> "PHONEPEWALLETTOPUP")
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
                Map.of(Constants.MESSAGE, UNSUPPORTED_PAYEE_MESSAGE + internalUserParty.getType(),
                    Constants.TRANSACTION_ID, receivedPayment.getTransactionId())
            ));
    }

    @Override
    public String visit(final ExternalUserParty externalUserParty) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, UNSUPPORTED_PAYEE_MESSAGE + externalUserParty.getType(),
                Constants.TRANSACTION_ID, receivedPayment.getTransactionId())
        );
    }

    @Override
    public String visit(final MerchantParty merchantParty) {
        return merchantParty.getMerchantId();
    }

    @Override
    public String visit(final MerchantUserParty merchantUserParty){
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, UNSUPPORTED_PAYEE_MESSAGE + merchantUserParty.getType(),
                Constants.TRANSACTION_ID, receivedPayment.getTransactionId())
        );
    }

    @Override
    public String visit(UnknownParty merchantUserParty) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, UNSUPPORTED_PAYEE_MESSAGE + merchantUserParty.getType(),
                Constants.TRANSACTION_ID, receivedPayment.getTransactionId())
        );
    }

    @Override
    public String visit(CardNetworkAcquirerParty merchantUserParty) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, UNSUPPORTED_PAYEE_MESSAGE + merchantUserParty.getType(),
                Constants.TRANSACTION_ID, receivedPayment.getTransactionId())
        );
    }
}
