package com.phonepe.merchant.platform.stratos.server.core.models;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventGenerationType {
    CHARGEBACK_RECOVERY("_CBK_REC"),
    CHARGEBACK_PENALTY_RECOVERY("_CBK_PEN_REC"),
    CHARGEBACK_RECOVERY_REVERSAL("_CBK_REC_REV"),
    PENALTY_RECOVERY("_PEN_REC"),
    HOLD_RECOVERY("_HLD_REC"),
    HOLD_RECOVERY_REVERSAL("_HLD_REC_REV");

    private final String eventIdSuffix;
}
