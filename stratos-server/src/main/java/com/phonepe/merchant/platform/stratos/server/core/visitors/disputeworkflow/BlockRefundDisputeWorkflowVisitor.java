package com.phonepe.merchant.platform.stratos.server.core.visitors.disputeworkflow;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.Builder;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Optional;

@Data
@Builder
public class BlockRefundDisputeWorkflowVisitor implements DisputeTypeVisitor<Response> {

    private final PaymentsService paymentsService;
    private final DisputeWorkflowRepository disputeWorkflowRepository;
    private final EdcService edcService;
    private final @Valid DisputeType disputeType;
    private final @Valid DisputeStage disputeStage;
    private final @Valid @NotEmpty List<String> transactionIds;

    @Override
    @SuppressWarnings("java:S4144")
    public Response visitUpiChargeback() {
        transactionIds.forEach(paymentsService::blockReversals);
        return Response.ok().build();
    }

    @Override
    @SuppressWarnings("java:S4144")
    public Response visitPgChargeback() {
        transactionIds.forEach(paymentsService::blockReversals);
        return Response.ok().build();
    }

    @Override
    @SuppressWarnings("java:S4144")
    public Response visitUdirOutgoingComplaint() {
        DisputeException stratosError = DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_TYPE);
        return Response.status(Integer.parseInt(stratosError.getErrorCode().getKey()),
                stratosError.getMessage()).build();
    }

    @Override
    @SuppressWarnings("java:S4144")
    public Response visitUdirIncomingComplaint() {
        DisputeException stratosError = DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_TYPE);
        return Response.status(Integer.parseInt(stratosError.getErrorCode().getKey()),
                stratosError.getMessage()).build();
    }

    @Override
    @SuppressWarnings("java:S4144")
    public Response visitP2PMToa() {
        DisputeException stratosError = DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_TYPE);
        return Response.status(Integer.parseInt(stratosError.getErrorCode().getKey()),
                stratosError.getMessage()).build();
    }

    @Override
    public Response visitEdcChargeback() {
        List<String> merchantTxnIds = transactionIds.stream()
                .map(transactionId -> disputeWorkflowRepository.select(transactionId, disputeType, disputeStage))
                .filter(Optional::isPresent)
                .map(disputeWorkflow -> disputeWorkflow.get().getDispute().getMerchantTransactionId())
                .toList();
        merchantTxnIds.forEach(edcService::blockReversals);
        return Response.ok().build();
    }

    @Override
    @SuppressWarnings("java:S4144")
    public Response visitNetBankingChargeback() {
        transactionIds.forEach(paymentsService::blockReversals);
        return Response.ok().build();
    }

    @Override
    public Response visitNotionalCreditToa() {
        DisputeException stratosError = DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_TYPE);
        return Response.status(Integer.parseInt(stratosError.getErrorCode().getKey()),
                stratosError.getMessage()).build();
        }

    @Override
    public Response visitWalletChargeback(){
        return visitNotionalCreditToa();
    }

    @Override
    public Response visitBbpsTatBreachToa() {
        return visitNotionalCreditToa();
    }
}
