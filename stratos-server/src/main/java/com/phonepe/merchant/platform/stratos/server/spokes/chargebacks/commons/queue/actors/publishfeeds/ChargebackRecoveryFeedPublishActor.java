package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.TstoreService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AccountingEventUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.FeedUtils;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import java.util.Set;
import javax.inject.Singleton;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ChargebackRecoveryFeedPublishActor extends Actor<ActionType, DisputeWorkflowMessage> {

    private final PaymentsService paymentsService;

    private final DisputeService disputeService;

    private final TstoreService tstoreService;

    @Inject
    @SuppressWarnings("java:S107")
    public ChargebackRecoveryFeedPublishActor(
        final Map<ActionType, ActorConfig> actorConfigMap,
        final ConnectionRegistry connectionRegistry,
        final ObjectMapper mapper,
        final RetryStrategyFactory retryStrategyFactory,
        final ExceptionHandlingFactory exceptionHandlingFactory,
        final PaymentsService paymentsService,
        final DisputeService disputeService,
        final TstoreService tstoreService) {

        super(ActionType.CHARGEBACK_RECOVERY_FEED_PUBLISHER,
            actorConfigMap.get(ActionType.CHARGEBACK_RECOVERY_FEED_PUBLISHER),
            connectionRegistry, mapper, retryStrategyFactory,
            exceptionHandlingFactory, DisputeWorkflowMessage.class,
            Set.of(JsonProcessingException.class));
        this.paymentsService = paymentsService;
        this.disputeService = disputeService;
        this.tstoreService = tstoreService;
    }

    @Override
    protected boolean handle(
        final DisputeWorkflowMessage disputeWorkflowMessage,
        final MessageMetadata messageMetadata) {

        final var transactionReferenceId = disputeWorkflowMessage.getTransactionReferenceId();
        final var disputeWorkflowId = disputeWorkflowMessage.getDisputeWorkflowId();

        final var disputeWorkflow = disputeService
            .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);
        final var dispute = disputeWorkflow.getDispute();

        final var globalPaymentId = paymentsService.getGlobalPaymentId(transactionReferenceId);

        final var financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            disputeWorkflow);

        final var recoveryFeed = FeedUtils.toDisputeFeed(dispute, disputeWorkflow,
            FeedUtils.toChargebackDisputeContext(dispute, financialDisputeWorkflow),
            globalPaymentId);

        final var recoveryEntityId = AccountingEventUtils
            .toAccountingEventId(disputeWorkflowId, EventGenerationType.CHARGEBACK_RECOVERY);

        tstoreService.createFeed(recoveryEntityId, recoveryFeed);

        if (financialDisputeWorkflow.getPenaltyAmount() > 0L) {

            final var penaltyRecoveryFeed = FeedUtils.toDisputeFeed(dispute, disputeWorkflow,
                FeedUtils.toChargebackPenaltyDisputeContext(dispute, financialDisputeWorkflow),
                globalPaymentId);

            final var penaltyRecoveryEntityId = AccountingEventUtils
                .toAccountingEventId(
                    disputeWorkflowId,
                    EventGenerationType.CHARGEBACK_PENALTY_RECOVERY);

            tstoreService
                .createFeed(penaltyRecoveryEntityId, penaltyRecoveryFeed);
        }

        return true;
    }
}
