package com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.creations;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.callbacks.CallbackType;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.CallbackMessage;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.BaseTransitionAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class CreateDisputeEntryBaseAction extends
    BaseTransitionAction<DisputeWorkflowState, DisputeWorkflowEvent> {

    private final DisputeRepository disputeRepository;

    private final DisputeWorkflowRepository disputeWorkflowRepository;

    protected final EventIngester eventIngester;
    protected final CallbackActor callbackActor;

    @Override
    protected void performTransition(
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var dispute = stateContext.getExtendedState().get(Dispute.class, Dispute.class);
        Objects.requireNonNull(dispute);

        final var disputeWorkflow = stateContext.getExtendedState()
            .get(DisputeWorkflow.class, DisputeWorkflow.class);
        Objects.requireNonNull(disputeWorkflow);

        ValidationUtils.validateDisputeId(disputeWorkflow, dispute);

        preTransition(dispute, disputeWorkflow);

        AtomicReference<DisputeWorkflow> wfCreated = new AtomicReference<>();

        Optional<Dispute> disputeCreated = disputeRepository.save(dispute, storedDispute -> {
            Optional<DisputeWorkflow> dw = disputeWorkflowRepository.save(disputeWorkflow);
            dw.ifPresent(wfCreated::set);
            transition(dispute, disputeWorkflow);
            return storedDispute;
        });
        if(disputeCreated.isPresent()){
            try {
                callbackActor.publish(CallbackMessage.builder()
                        .dispute(disputeCreated.get())
                        .callbackType(CallbackType.DISPUTE_CREATION)
                        .previousState(null)
                        .currentStatus(disputeWorkflow.getCurrentState().accept(
                                MapperUtils.getStateMapper(wfCreated.get().getDisputeWorkflowVersion()),wfCreated.get()))
                        .eventTime(LocalDateTime.now())
                        .build());
            } catch (Exception e) {
                log.error("Exception while pushing callbacks event for dispute creation : {}",
                        disputeCreated.get().getDisputeId(),
                        e);
            }
        }

        postTransition(dispute, disputeWorkflow);
        eventIngester.generateEvent(
            FoxtrotEventUtils.toDisputeActionSuccessEvent(disputeCreated.orElse(dispute),
                disputeWorkflow, null));
    }

    protected void preTransition(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow) {
        // NOOP
    }

    protected void transition(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow) {
        // NOOP
    }

    protected void postTransition(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow) {
        // NOOP
    }
}
