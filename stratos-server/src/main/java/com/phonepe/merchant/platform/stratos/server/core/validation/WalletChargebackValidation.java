package com.phonepe.merchant.platform.stratos.server.core.validation;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.pipeline.exceptions.PipelineException;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.DisputeCategoryTtlConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.TtlConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WalletTransactionService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.core.validator.DisputeValidator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

@Singleton
@Slf4j
public class WalletChargebackValidation implements DisputeValidator {

    private final WalletTransactionService walletTransactionService;
    private final DisputeService disputeService;
    private final Map<DisputeType, TtlConfig> ttlConfigMap;
    private final Map<DisputeType, DisputeCategoryTtlConfig> fraudChargebackTtl;
    private final EventIngester eventIngester;

    @Inject
    public WalletChargebackValidation(
        WalletTransactionService walletTransactionService, DisputeService disputeService,
        Map<DisputeType, TtlConfig> ttlConfigMap, Map<DisputeType, DisputeCategoryTtlConfig> fraudChargebackTtl,
        EventIngester eventIngester
    ) {
        this.walletTransactionService = walletTransactionService;
        this.disputeService = disputeService;
        this.ttlConfigMap = ttlConfigMap;
        this.fraudChargebackTtl = fraudChargebackTtl;
        this.eventIngester = eventIngester;
    }

    private static @NotNull List<DisputeStateMachineRegistryKey> getKey() {
        return List.of(DisputeStateMachineRegistryKey.builder()
                .disputeType(DisputeType.WALLET_CHARGEBACK)
                .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .build(),
            DisputeStateMachineRegistryKey.builder()
                .disputeType(DisputeType.WALLET_CHARGEBACK)
                .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
                .disputeStage(DisputeStage.PRE_ARBITRATION)
                .build());
    }

    @Override
    @SneakyThrows
    public boolean validate(CreateDisputeRequest createDisputeRequest) {
        try {
            var validation = new CreateDisputeRequestValidationPipeline(
                "createDisputeRequestValidation").append(
                new DuplicateTransactionValidation("DuplicateValidation", disputeService))
                .append(new WalletTTLValidation("ttlValidation", ttlConfigMap, fraudChargebackTtl,
                    walletTransactionService)).append(
                new WalletTransactionValidation("walletTransactionValidation", disputeService,
                    walletTransactionService)
            );
            validation.processPipeline(createDisputeRequest);
            return true;
        }
        catch (PipelineException ex) {
            log.error("Error in validation for createDisputeRequest {} with exception cause is",
                createDisputeRequest, ex.getCause());
            if (Objects.nonNull(ex.getCause()) && Objects.nonNull(ex.getCause().getCause()) &&
                ((ex.getCause().getCause())instanceof DisputeException)) {
                    throw ((DisputeException) ex.getCause().getCause());
            }
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.VALIDATION_FAILURE,
                ex,
                Map.of(Constants.MESSAGE,
                    "validation error for create dispute request : " + createDisputeRequest));
        }
    }
}
