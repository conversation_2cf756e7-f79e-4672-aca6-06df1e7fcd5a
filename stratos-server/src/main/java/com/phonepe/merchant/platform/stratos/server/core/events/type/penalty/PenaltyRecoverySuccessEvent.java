package com.phonepe.merchant.platform.stratos.server.core.events.type.penalty;

import com.phonepe.merchant.platform.stratos.server.core.events.EventType;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PenaltyRecoverySuccessEvent extends PenaltyRecoveryEvent {

    @NotNull
    private final Long amountRecovered;

    @NotBlank
    private final String mcc;

    @NotBlank
    private final String merchantId;


    @Builder
    public PenaltyRecoverySuccessEvent(final String penaltyClassId,
                                       final String tenantName,
                                       final String tenantSubCategory,
                                       final String penaltyId,
                                       final String transactionId,
                                       final String disbursementId,
                                       final Long amountRecovered,
                                       final String mcc,
                                       final String merchantId) {
        super(EventType.PENALTY_RECOVERY_SUCCESS_EVENT, penaltyClassId, tenantName, tenantSubCategory, penaltyId,
                transactionId, disbursementId);
        this.amountRecovered = amountRecovered;
        this.mcc = mcc;
        this.merchantId = merchantId;
    }

    @Override
    protected String getGroupingKey() {
        return getPenaltyClassId();
    }
}
