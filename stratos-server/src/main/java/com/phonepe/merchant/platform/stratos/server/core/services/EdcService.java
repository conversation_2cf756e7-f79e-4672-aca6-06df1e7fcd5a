package com.phonepe.merchant.platform.stratos.server.core.services;

import com.phonepe.edc.response.EdcTransactionDetailsResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummaryRow;
import com.phonepe.merchant.platform.stratos.models.row.requests.EdcRowSignalContext;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;

public interface EdcService {

    boolean isFullyReversed(String merchantTransactionId);

    void blockReversals(String merchantTransactionId);

    Void unblockReversals(String merchantTransactionId);

    EdcTransactionDetailsResponse getEdcTransactionDetails(final String tenant, final String merchantId, final String terminalId, final String rrn);

    EdcTransactionDetailsResponse getEdcTransactionDetailsFromTransactionId(final String merchantTransactionId);

    void enrichChargebackSummaryFromEdc(ChargebackSummaryRow chargebackSummaryRow);

    void validateDebitAmount(EdcRowSignalContext edcRowSignalContext, DisputeWorkflow disputeWorkflow);
}
