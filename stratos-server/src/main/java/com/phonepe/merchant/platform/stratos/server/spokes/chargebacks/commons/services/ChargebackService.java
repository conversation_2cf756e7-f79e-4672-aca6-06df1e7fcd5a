package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.NpciChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.NpciChargebackSummaryRow;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import java.util.List;
import java.util.Set;

public interface ChargebackService {

    Set<DisputeWorkflowState> getAllStates();

    List<ChargebackSummary> filter(final DisputeFilter filter);

    List<NpciChargebackSummary> npciChargebackFilter(final DisputeFilter filter);

    byte[] download(final FileFormat fileFormat, final DisputeFilter filter);

    byte[] npciDownload(final FileFormat fileFormat, final DisputeFilter filter);

    void updateChargebackStateBreachingTtl(final DateRange dateRange);

    NpciChargebackSummaryRow enrichNpciChargebackSummaryRow(final DisputeWorkflow disputeWorkflow);
}
