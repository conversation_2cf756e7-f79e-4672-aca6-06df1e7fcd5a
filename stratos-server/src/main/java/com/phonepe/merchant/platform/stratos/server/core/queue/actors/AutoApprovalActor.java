package com.phonepe.merchant.platform.stratos.server.core.queue.actors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.CommentContext;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.configs.AutoApprovalConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class AutoApprovalActor extends Actor<ActionType, DisputeWorkflowMessage> {

    private final DisputeService disputeService;
    private final StratosConfiguration stratosConfiguration;

    private final EventIngester eventIngester;
    @Inject
    protected AutoApprovalActor(
            final Map<ActionType, ActorConfig> actorConfigMap,
            final ConnectionRegistry connectionRegistry,
            final ObjectMapper mapper,
            final RetryStrategyFactory retryStrategyFactory,
            final ExceptionHandlingFactory exceptionHandlingFactory, DisputeService disputeService, StratosConfiguration stratosConfiguration,final EventIngester eventIngester) {
        super(ActionType.AUTO_APPROVAL_HANDLER, actorConfigMap.get(ActionType.AUTO_APPROVAL_HANDLER), connectionRegistry, mapper, retryStrategyFactory, exceptionHandlingFactory,
                DisputeWorkflowMessage.class, Set.of(JsonProcessingException.class));
        this.disputeService = disputeService;
        this.stratosConfiguration = stratosConfiguration;
        this.eventIngester =eventIngester;
    }

    @Override
    protected boolean handle(final DisputeWorkflowMessage disputeWorkflowMessage,
                             final MessageMetadata messageMetadata) {
        final var disputeWorkflow = disputeService
                .validateAndGetDisputeWorkflow(disputeWorkflowMessage.getTransactionReferenceId(),
                        disputeWorkflowMessage.getDisputeWorkflowId());
        String autoApprovalKey = disputeWorkflow.getDisputeType() + "_" + disputeWorkflow.getDisputeStage();
        Optional<AutoApprovalConfig> autoApprovalConfig = getIsAutoApproved(autoApprovalKey, disputeWorkflow.getCurrentState());

        if (autoApprovalConfig.isPresent()) {
            log.info("Auto approving DisputeWorkflow {}, TransactionWokflow {}",
                    disputeWorkflowMessage.getDisputeWorkflowId(), disputeWorkflowMessage.getTransactionReferenceId());

            disputeService.triggerEvent(
                    Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                    disputeWorkflowMessage.getTransactionReferenceId(),
                    disputeWorkflowMessage.getDisputeWorkflowId(),
                    autoApprovalConfig.get().getToEvent(),
                    CommentContext.builder().comment("Auto approved")
                            .build()
            );
        } else {
            log.error("AutoApproval is not enabled for disputeWorkflowId {} with state {} and message {}",disputeWorkflow.getDisputeWorkflowId(),disputeWorkflow.getCurrentState(),disputeWorkflowMessage);
            eventIngester.generateEvent(FoxtrotEventUtils.
                    toDisputeActionFailedEvent(disputeWorkflow, disputeWorkflow.getCurrentState(),
                            "AutoApproval is not enabled", ""));
        }
        return true;
    }

    private Optional<AutoApprovalConfig> getIsAutoApproved(final String autoApprovalKey, final DisputeWorkflowState disputeWorkflowState) {
        val autoApprovalConfigList = stratosConfiguration.getAutoApprovalConfig().get(autoApprovalKey);
        if (autoApprovalConfigList == null)
            return Optional.empty();
        else
            return autoApprovalConfigList.stream().filter(autoApprovalConfig -> autoApprovalConfig.getCurrentState().equals(disputeWorkflowState))
                    .findFirst();
    }
}
