package com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.HoldReversalAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseHoldRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.ChargebackRecoveryEligibilityAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.PartialHoldEligibilityAction;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ReverseHoldRecoverCBConfigurator implements WorkflowConfigurator<DisputeWorkflowState, DisputeWorkflowEvent>{
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final ChargebackRecoveryEligibilityAction chargebackRecoveryEligibilityAction;
    private final PartialHoldEligibilityAction partialHoldEligibilityAction;
    private final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction;
    private final RaiseHoldRecoveryReversalAccountingEventAction raiseHoldRecoveryReversalAccountingEventAction;
    private final HoldReversalAction holdReversalAction;

    @Override
    @SneakyThrows
    public void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
            .withInternal()
            .source(DisputeWorkflowState.HOLD)
            .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_HOLD)
            .action(holdReversalAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.HOLD)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_HOLD_EVENT)
            .action(raiseHoldRecoveryReversalAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.HOLD)
            .target(DisputeWorkflowState.END)
            .event(DisputeWorkflowEvent.END_WORKFLOW)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_RAISED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_REVERSAL_OF_RECOVERED_HOLD_EVENT)
            .action(chargebackRecoveryEligibilityAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_ACCEPTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT)
            .action(raiseAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .target(DisputeWorkflowState.END)
            .event(DisputeWorkflowEvent.END_WORKFLOW)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_RECOVER_CHARGEBACK_EVENT)
            .action(partialHoldEligibilityAction)


            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_ACCEPTED)
            .target(DisputeWorkflowState.END)
            .event(DisputeWorkflowEvent.END_WORKFLOW)
            .action(updateDisputeStateAction)

        ;

    }
}
