package com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators;

import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseHoldRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.InitiateMerchantCommunicationAction;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.EqualsAndHashCode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.ProactiveHoldAction;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ProactiveHoldWorkflowConfigurator implements WorkflowConfigurator<DisputeWorkflowState, DisputeWorkflowEvent> {

    private final ProactiveHoldAction proactiveHoldAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final RaiseHoldRecoveryAccountingEventAction raiseAccountingEventAction;
    private final UnblockRefundAction unblockRefundAction;
    private final FraudCheckDisputeAction fraudCheckUpdateDisputeAction;
    private final InitiateMerchantCommunicationAction initiateMerchantCommunicationAction;

    @Override
    @SneakyThrows
    public void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {

        transitions
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.REFUND_BLOCKED)
                .event(DisputeWorkflowEvent.BLOCK_REFUND)
                .action(proactiveHoldAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
                .event(DisputeWorkflowEvent.REQUEST_REPRESENTMENT)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REFUND_BLOCKED)
                .target(DisputeWorkflowState.RECOVER_HOLD_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_RECOVER_HOLD_EVENT)
                .action(raiseAccountingEventAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RECOVER_HOLD_EVENT_RAISED)
                .target(DisputeWorkflowState.RECOVER_HOLD_EVENT_ACCEPTED)
                .event(DisputeWorkflowEvent.ACCEPT_RECOVER_HOLD_EVENT)
                .action(initiateMerchantCommunicationAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REFUND_BLOCKED)
                .target(DisputeWorkflowState.RECOVER_HOLD_EVENT_ACCEPTED)
                .event(DisputeWorkflowEvent.ACCEPT_RECOVER_HOLD_EVENT)
                .action(initiateMerchantCommunicationAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RECOVER_HOLD_EVENT_ACCEPTED)
                .target(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
                .event(DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
                .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
                .action(unblockRefundAction) //need to evaluate
                .and();

    }
}
