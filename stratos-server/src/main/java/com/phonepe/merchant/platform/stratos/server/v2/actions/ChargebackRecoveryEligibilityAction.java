package com.phonepe.merchant.platform.stratos.server.v2.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds.ChargebackRecoveryFeedPublishActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.raiseevents.RaiseChargebackRecoveryAccountingEventActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Singleton
public class ChargebackRecoveryEligibilityAction extends UpdateDisputeStateBaseAction {
    private final Provider<RaiseChargebackRecoveryAccountingEventActor> raiseAccountingEventActor;
    private final Provider<ChargebackRecoveryFeedPublishActor> chargebackRecoveryFeedPublishActor;
    private final StateChangeHandlerActor stateChangeHandlerActor;

    @Inject
    @SuppressWarnings("java:S107")
    public ChargebackRecoveryEligibilityAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final EventIngester eventIngester,
            final Provider<RaiseChargebackRecoveryAccountingEventActor> raiseAccountingEventActor,
            final Provider<ChargebackRecoveryFeedPublishActor> chargebackRecoveryFeedPublishActor,
            final CallbackActor callbackActor, StateChangeHandlerActor stateChangeHandlerActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.raiseAccountingEventActor = raiseAccountingEventActor;
        this.chargebackRecoveryFeedPublishActor = chargebackRecoveryFeedPublishActor;
        this.stateChangeHandlerActor = stateChangeHandlerActor;
    }

    @Override
    @SneakyThrows
    protected void postTransition(DisputeWorkflow dw) {
        FinancialDisputeWorkflow disputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
                dw);
        if (disputeWorkflow.getAcceptedAmount() == 0 || disputeService.isChargebackRecoveryDone(
                disputeWorkflow.getDisputeWorkflowId())) {
            DisputeWorkflowUtils.closeWorkflow(disputeWorkflow, stateChangeHandlerActor);

        }
        else {
            DisputeWorkflowUtils.merchantRecovery(
                disputeWorkflow,
                raiseAccountingEventActor.get(),
                chargebackRecoveryFeedPublishActor.get());
        }
    }
}
