package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.payments.pay.context.ContextVisitor;
import com.phonepe.models.payments.pay.context.impl.*;

import com.phonepe.models.payments.pay.context.impl.AccountWithdrawalClosureContext;
import com.phonepe.models.payments.pay.context.impl.AccountWithdrawlContext;
import com.phonepe.models.payments.pay.context.impl.AccountWithdrawlReversalContext;
import com.phonepe.models.payments.pay.context.impl.CancelGiftCardContext;
import com.phonepe.models.payments.pay.context.impl.ClosureAccountWithdrawalReversalContext;
import com.phonepe.models.payments.pay.context.impl.CreateGiftCardContext;
import com.phonepe.models.payments.pay.context.impl.DelegatePaymentResponseContext;
import com.phonepe.models.payments.pay.context.impl.ExternalInternationalIntentPaymentContext;
import com.phonepe.models.payments.pay.context.impl.IncomingPaymentContext;
import com.phonepe.models.payments.pay.context.impl.InstrumentAuthContext;
import com.phonepe.models.payments.pay.context.impl.IntentPaymentContext;
import com.phonepe.models.payments.pay.context.impl.MerchantAccountVerificationPaymentContext;
import com.phonepe.models.payments.pay.context.impl.MerchantBuybackContext;
import com.phonepe.models.payments.pay.context.impl.MerchantCashbackPaymentContext;
import com.phonepe.models.payments.pay.context.impl.MerchantCreditPaymentContext;
import com.phonepe.models.payments.pay.context.impl.MerchantGiftCardPrepayContext;
import com.phonepe.models.payments.pay.context.impl.MerchantIncentivePaymentContext;
import com.phonepe.models.payments.pay.context.impl.MerchantRefundPaymentContext;
import com.phonepe.models.payments.pay.context.impl.MerchantReversalPaymentContext;
import com.phonepe.models.payments.pay.context.impl.MerchantToMerchantPaymentContext;
import com.phonepe.models.payments.pay.context.impl.MerchantToMerchantReversalContext;
import com.phonepe.models.payments.pay.context.impl.PeerToMerchantPaymentContext;
import com.phonepe.models.payments.pay.context.impl.PeerToPeerMerchantTOAPaymentContext;
import com.phonepe.models.payments.pay.context.impl.PeerToPeerPaymentContext;
import com.phonepe.models.payments.pay.context.impl.PhonePePayoutContext;
import com.phonepe.models.payments.pay.context.impl.PosPaymentContext;
import com.phonepe.models.payments.pay.context.impl.QuickCheckOutEnrollmentContext;
import com.phonepe.models.payments.pay.context.impl.RedeemGiftCardContext;
import com.phonepe.models.payments.pay.context.impl.ResponsePaymentContext;
import com.phonepe.models.payments.pay.context.impl.ScannedPaymentContext;
import com.phonepe.models.payments.pay.context.impl.ToaPaymentContext;
import com.phonepe.models.payments.pay.context.impl.UPICreditCardBillPaymentContext;
import com.phonepe.models.payments.pay.context.impl.UPILiteClosureContext;
import com.phonepe.models.payments.pay.context.impl.UPILiteTopUpContext;
import com.phonepe.models.payments.pay.context.impl.UpiWalletCreditCBSPaymentContext;
import com.phonepe.models.payments.pay.context.impl.UpiWalletDebitCBSPaymentContext;
import com.phonepe.models.payments.pay.context.impl.UserMigrationContext;
import com.phonepe.models.payments.pay.context.impl.UserSelfPaymentContext;
import com.phonepe.models.payments.pay.context.impl.WalletAppTopupContext;
import com.phonepe.models.payments.pay.context.impl.WalletClosureRedemptionContext;
import com.phonepe.models.payments.pay.context.impl.WalletClosureTopUpReversalContext;
import com.phonepe.models.payments.pay.context.impl.WalletTopupContext;
import com.phonepe.models.payments.pay.context.impl.WalletTopupReversalContext;
import java.util.Map;

public class PaymentContextMerchantTransactionIdVisitor implements ContextVisitor<String> {


    public static final String NOT_SUPPORTED_TEXT = "not supported";

    @Override
    public String visit(UpiWalletDebitCBSPaymentContext context) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(UpiWalletCreditCBSPaymentContext context) {
       throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(final AccountWithdrawalClosureContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final AccountWithdrawlContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final AccountWithdrawlReversalContext context) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(final IncomingPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final IntentPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(
        ExternalInternationalIntentPaymentContext context)
        throws Exception {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantCashbackPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantCreditPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantRefundPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantReversalPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantToMerchantPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final PeerToMerchantPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final PeerToPeerPaymentContext context) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(final UserMigrationContext context) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(final UserSelfPaymentContext context) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(final ResponsePaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final ScannedPaymentContext context) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(final ToaPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantBuybackContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final WalletAppTopupContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final WalletTopupContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final RedeemGiftCardContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final CreateGiftCardContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final CancelGiftCardContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantGiftCardPrepayContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final PosPaymentContext context) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(final InstrumentAuthContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final WalletClosureRedemptionContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final WalletTopupReversalContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantToMerchantReversalContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final PeerToPeerMerchantTOAPaymentContext context)
        {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final ClosureAccountWithdrawalReversalContext context) {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(final MerchantIncentivePaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final QuickCheckOutEnrollmentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final MerchantAccountVerificationPaymentContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final WalletClosureTopUpReversalContext context)
        {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(final PhonePePayoutContext context) {
        return context.getMerchantTransactionId();
    }

    @Override
    public String visit(UPILiteTopUpContext context) throws Exception {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(UPILiteClosureContext context) throws Exception {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(UPILiteWithdrawalContext context) throws Exception {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(UPICreditCardBillPaymentContext context)
        throws Exception {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(DelegatePaymentResponseContext context){
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(PeerToPeerV2PaymentContext context) throws Exception {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(NcmcAcquiringTopupPaymentContext context) throws Exception {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(NcmcDebitContext context) throws Exception {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }

    @Override
    public String visit(NcmcRupayCreditContext context) throws Exception {
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
            Map.of(Constants.MESSAGE, context.getTransferMode() + NOT_SUPPORTED_TEXT));
    }
}
