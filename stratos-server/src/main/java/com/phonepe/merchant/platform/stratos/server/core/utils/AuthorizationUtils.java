package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.phonepe.merchant.platform.stratos.models.commons.DownloadReportType;
import com.phonepe.merchant.platform.stratos.models.commons.visitors.DownloadReportVisitor;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.olympus.im.models.user.UserDetails;
import io.dropwizard.primer.exception.PrimerException;
import java.util.Set;
import java.util.function.BooleanSupplier;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

@UtilityClass
public class AuthorizationUtils {

    public boolean isAuthorized(
        OlympusIMClient olympusIMClient,
        final UserAuthDetails userAuthDetails,
        final String requiredGroupLevelPermission) {

        return olympusIMClient.verifyPermission(userAuthDetails, requiredGroupLevelPermission);
    }
    public boolean isAuthorizedToCallback(
        OlympusIMClient olympusIMClient,
        final UserAuthDetails userAuthDetails,
        final String callbackType) {

        return isAuthorized(olympusIMClient, userAuthDetails, "dispute/" + callbackType);
    }

    public boolean isAuthorizedToTriggerEvent(
        OlympusIMClient olympusIMClient,
        final UserAuthDetails userAuthDetails,
        final DisputeWorkflowEvent disputeWorkflowEvent) {

        return isAuthorized(olympusIMClient, userAuthDetails, "chargeback/transition-" + disputeWorkflowEvent.name().toLowerCase());
    }
    @SneakyThrows
    public void authorizeCallback(
            OlympusIMClient olympusIMClient,
            final UserAuthDetails userAuthDetails,
            final String callbackType) {
        authorize(() -> isAuthorizedToCallback(olympusIMClient, userAuthDetails, callbackType));
    }

    @SneakyThrows
    public void authorizeTriggerEvent(
        OlympusIMClient olympusIMClient,
        final UserAuthDetails userAuthDetails,
        final DisputeWorkflowEvent disputeWorkflowEvent) {

        authorize(() -> isAuthorizedToTriggerEvent(olympusIMClient, userAuthDetails, disputeWorkflowEvent));
    }

    public void authorize(
        OlympusIMClient olympusIMClient,
        final UserAuthDetails userAuthDetails,
        final String requiredGroupLevelPermission) {

        authorize(() -> isAuthorized(olympusIMClient, userAuthDetails, requiredGroupLevelPermission));
    }

    @SneakyThrows
    public void authorize(final BooleanSupplier isAuthorizedSupplier) {

        if (!isAuthorizedSupplier.getAsBoolean()) {
            throw PrimerException.builder()
                .errorCode("FORBIDDEN")
                .message("Forbidden Permission Error")
                .status(403)
                .recoverable(true)
                .build();
        }
    }

    public UserType getUserType (UserDetails userDetails) {
        return (userDetails.getUserType() == com.phonepe.olympus.im.models.authn.UserType.HUMAN) ?
            UserType.USER : UserType.SYSTEM;
    }

    public boolean isAuthorizedNonGroupLevelPermission(OlympusIMClient olympusIMClient, UserAuthDetails userAuthDetails,
        final String requiredPermission) {
        return olympusIMClient.verifyPermission(userAuthDetails, requiredPermission);
    }

    public void authorizeRowHistory(OlympusIMClient olympusIMClient, final UserAuthDetails userAuthDetails, Set<RowTypeDto> rowTypes) {
        for (RowTypeDto rowType : rowTypes) {
            authorize(() -> isAuthorizedNonGroupLevelPermission(olympusIMClient, userAuthDetails,
                "dispute/row-history-" + rowType.name().toLowerCase()));
        }
    }

    public void authorizeRowSignal(OlympusIMClient olympusIMClient, final UserAuthDetails userAuthDetails, RowTypeDto rowType) {
        authorize(() -> isAuthorizedNonGroupLevelPermission(olympusIMClient, userAuthDetails,
            "dispute/row-process-" + rowType.name().toLowerCase()));
    }

    public void authorizeUdirRaiseComplaint(OlympusIMClient olympusIMClient, final UserAuthDetails userAuthDetails,
        DisputeType disputeType) {
        authorize(() -> isAuthorizedNonGroupLevelPermission(olympusIMClient, userAuthDetails,
            "dispute/raise-" + disputeType.name().toLowerCase()));
    }

    public void authorizeDisputeStatusCheck(OlympusIMClient olympusIMClient, final UserAuthDetails userAuthDetails,
        DisputeType disputeType) {
        authorize(() -> isAuthorizedNonGroupLevelPermission(olympusIMClient, userAuthDetails,
            "dispute/status-" + disputeType.name().toLowerCase()));
    }

    public void authorizedDownloadReportApi(final OlympusIMClient olympusIMClient, final UserAuthDetails userAuthDetails, DownloadReportType reportType){
        final var permission = reportType.accept(new DownloadReportVisitor<String, Void>() {
            @Override
            public String visitChargeback(Void request) {
                return "chargeback/summary";
            }
            @Override
            public String visitToa(Void request) {
                return "p2pmtoa/summary";
            }
            @Override
            public String visitNpicChargeback(Void request) {
                return "npci-chargeback/summary";
            }
        }, null);

        authorize(() -> isAuthorizedNonGroupLevelPermission(olympusIMClient, userAuthDetails,
            permission));
    }
}
