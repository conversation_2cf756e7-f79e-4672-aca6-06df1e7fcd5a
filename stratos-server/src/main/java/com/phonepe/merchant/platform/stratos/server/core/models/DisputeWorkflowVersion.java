package com.phonepe.merchant.platform.stratos.server.core.models;

/**
 * This entity is persisted in DB by it's Ordinal Value Hence only append at the end and do not
 * change Order of existing values while adding new values
 */
public enum DisputeWorkflowVersion {
    V1 {
        @Override
        public <T> T accept(DisputeWorkflowVersionVisitor<T> visitor) {
            return visitor.visitV1();
        }
    },
    V2 {
        @Override
        public <T> T accept(DisputeWorkflowVersionVisitor<T> visitor) {
            return visitor.visitV2();
        }
    };

    public abstract <T> T accept(DisputeWorkflowVersionVisitor<T> visitor);
}
