package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.server.core.clients.PlutusEventIngestionClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.AccountingEventStatus;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.checkstatus.impls.CheckHoldRecoveryReversalAccountingEventStatusActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import lombok.SneakyThrows;
import org.springframework.statemachine.StateContext;

import java.util.Objects;

public class RaiseHoldRecoveryReversalAccountingEventAction extends UpdateDisputeStateBaseAction {
    private final DisputeMetadataRepository disputeMetadataRepository;

    private final PlutusEventIngestionClient plutusEventIngestionClient;

    private final Provider<CheckHoldRecoveryReversalAccountingEventStatusActor> checkAccountingEventStatusActor;

    private final DisputeMetadataHelper disputeMetadataHelper;

    @Inject
    @SuppressWarnings("java:S107")
    public RaiseHoldRecoveryReversalAccountingEventAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final EventIngester eventIngester,
            final DisputeMetadataRepository disputeMetadataRepository,
            final PlutusEventIngestionClient plutusEventIngestionClient,
            final Provider<CheckHoldRecoveryReversalAccountingEventStatusActor> checkAccountingEventStatusActor,
            final DisputeMetadataHelper disputeMetadataHelper,
            final CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.disputeMetadataRepository = disputeMetadataRepository;
        this.plutusEventIngestionClient = plutusEventIngestionClient;
        this.checkAccountingEventStatusActor = checkAccountingEventStatusActor;
        this.disputeMetadataHelper = disputeMetadataHelper;
    }

    @Override
    protected void transition(
            final DisputeWorkflow disputeWorkflow,
            final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var extendedState = stateContext.getExtendedState();

        final var recoveryReversalAccountingEvent = extendedState
                .get(Constants.HOLD_RECOVERY_REVERSAL_EVENT, AccountingEvent.class);
        Objects.requireNonNull(recoveryReversalAccountingEvent);

        plutusEventIngestionClient.sendAccountingEvent(recoveryReversalAccountingEvent);

        final var recoveryReversalAccountingEventDisputeMetadata = disputeMetadataHelper
                .toAccountingEventDisputeMetadata(disputeWorkflow, recoveryReversalAccountingEvent);

        disputeMetadataRepository.save(recoveryReversalAccountingEventDisputeMetadata);

        final var foxtrotRecoveryReversalEvent = FoxtrotEventUtils
                .toChargebackAccountingEvent(disputeWorkflow.getDispute(), disputeWorkflow,
                        recoveryReversalAccountingEvent, AccountingEventStatus.RAISED);
        eventIngester.generateEvent(foxtrotRecoveryReversalEvent);
    }

    @Override
    @SneakyThrows
    protected void postTransition(final DisputeWorkflow disputeWorkflow) {
        checkAccountingEventStatusActor.get().publishWithDelay(DisputeWorkflowMessage.builder()
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .build(), 5_000L);
    }
}
