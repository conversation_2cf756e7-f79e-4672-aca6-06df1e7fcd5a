package com.phonepe.merchant.platform.stratos.server.core.visitors;

import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.RefundEligibilityResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowStateVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@NoArgsConstructor
public class RefundEligibilityStateVisitor implements DisputeWorkflowStateVisitor<RefundEligibilityResponse> {

    @Override
    public RefundEligibilityResponse visitReceived() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRgcsAcceptanceRequired() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRgcsAcceptanceCompleted() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRepresentmentRequired() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitNPCIRepresentmentCompleted() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitCreditReceived() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRefundBlocked() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitFulfilmentDocumentsReceived() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPartialFulfilmentDocumentsReceived() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitNPCIPartialRepresentmentCompleted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPartialCreditReceived() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitMerchantNotRespondedWithinTTL() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitMerchantAcceptedChargeback() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitNPCIAcceptanceCompleted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitAbsorbChargebackRequested() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitAbsorbChargebackRejected() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitAbsorbChargebackApproved() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitChargebackAbsorbed() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitChargebackAbsorbedReversed() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverChargebackRequested() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverChargebackRejected() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverChargebackApproved() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverChargebackEventRaised() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverChargebackEventAccepted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredChargebackRequested() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredChargebackApproved() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredChargebackEventRaised() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredChargebackEventAccepted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitDebitReceived() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPartialDebitReceived() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPgRepresentmentCompleted() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPgAcceptanceCompleted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPgPartialRepresentmentCompleted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitUdirComplaintRejected() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitFailure() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitUdirComplaintAccepted() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitUdirResponseReceived() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitUdirResponseNotReceived() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitInternalMerchantRepresentmentRequired() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitChargebackCancelled() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaBlockedDueToKs() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitP2pmToaInitiated() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitP2pmToaInitiationFailed() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitP2pmToaCompleted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitP2pmToaFailed() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitP2pmToaPending() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitP2pmToaCompletedExternally() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitP2pmToaFailedAfterMaxAutoRetry() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRepresentedCompleted() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitAcceptanceCompleted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPartialRepresentmentCompleted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitFraudRejected() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitSuspectedFraud() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitCBRefundCreated() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitCBRefundInitated() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitCBRefundAccepted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitCBRefundFailed() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaOpened() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaClosed() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaInitiated() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaInitiationFailed() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaCompleted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaFailed() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaPending() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitToaFailedAfterMaxAutoRetry() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitFraudRepresentmentCompleted(){
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitHold() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }
    @Override
    public RefundEligibilityResponse visitNpciAckChargeback() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitNpciRejectedChargeback() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPartialRejectedChargeback() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitPartialAcceptedChargeback() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitFullyAcceptedChargeback() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitAcceptedChargeback() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRejectedChargeback() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitCbRefundInitiationCompleted() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitEnd() {
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverHoldEventRaised() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverHoldEventAccepted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredHoldEventRaised() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredHoldEventAccepted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }
    @Override
    public RefundEligibilityResponse visitToaUnableToProcess() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverPartialHoldEventRaised() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverPartialHoldEventAccepted() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitMerchantActionRequested() {
        return null;
    }
    @Override
    public RefundEligibilityResponse visitCbRefundProcessedExternally() {
        return TransformationUtils.getFailedResponseForRefundEligibility();
    }
}
