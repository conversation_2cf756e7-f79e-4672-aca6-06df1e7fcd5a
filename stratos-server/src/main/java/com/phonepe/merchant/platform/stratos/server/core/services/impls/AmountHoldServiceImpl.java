package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.AmountHoldCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.AmountHoldKey;
import com.phonepe.merchant.platform.stratos.server.core.services.AmountHoldService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class AmountHoldServiceImpl implements AmountHoldService {
    private final AmountHoldCommand amountHoldCommand;

    @Override
    public boolean holdExist(String transactionId) {
        return Objects.nonNull(amountHoldCommand.get(new AmountHoldKey(transactionId)));
    }

    @Override
    public long getHoldAmount(String transactionId) {
        if(!holdExist(transactionId))
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.HOLD_NOT_FOUND);
        return amountHoldCommand.get(new AmountHoldKey(transactionId));
    }

    @Override
    public void addHold(String transactionId, long amount) {
        amountHoldCommand.strictSave(new AmountHoldKey(transactionId), amount);
    }

    @Override
    public void removeHold(String transactionId) {
        amountHoldCommand.delete(new AmountHoldKey(transactionId));
    }
}
