package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.phonepe.merchant.platform.stratos.models.feeds.DisputeContext;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeFeed;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeStageFeed;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeTypeFeed;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeWorkflowVersionFeed;
import com.phonepe.merchant.platform.stratos.models.feeds.contexts.ChargebackDisputeContext;
import com.phonepe.merchant.platform.stratos.models.feeds.contexts.ChargebackPenaltyDisputeContext;
import com.phonepe.merchant.platform.stratos.models.feeds.contexts.ChargebackReversalDisputeContext;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import lombok.experimental.UtilityClass;

@UtilityClass
public class FeedUtils {

    public DisputeFeed toDisputeFeed(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow,
        final DisputeContext disputeContext,
        final String globalPaymentId) {
        return DisputeFeed.builder()
            .disputeId(dispute.getDisputeId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeWorkflowVersion(DisputeWorkflowVersionFeed
                .valueOf(disputeWorkflow.getDisputeWorkflowVersion().name()))
            .disputeType(DisputeTypeFeed.valueOf(disputeWorkflow.getDisputeType().name()))
            .disputeStage(DisputeStageFeed.valueOf(disputeWorkflow.getDisputeStage().name()))
            .globalPaymentId(globalPaymentId)
            .transactionReferenceId(dispute.getTransactionReferenceId())
            .merchantId(dispute.getMerchantId())
            .merchantTransactionId(dispute.getMerchantTransactionId())
            .transactionAmount(dispute.getTransactionAmount())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .disputeContext(disputeContext)
            .build();
    }

    public ChargebackDisputeContext toChargebackDisputeContext(
        final Dispute dispute,
        final FinancialDisputeWorkflow disputeWorkflow) {
        return ChargebackDisputeContext.builder()
            .instrumentTransactionId(dispute.getInstrumentTransactionId())
            .disputeReferenceId(dispute.getDisputeReferenceId())
            .rrn(dispute.getRrn())
            .chargebackSourceType(disputeWorkflow.getDisputeSourceType().name())
            .chargebackSourceId(disputeWorkflow.getDisputeSourceId())
            .acceptedAmount(disputeWorkflow.getAcceptedAmount())
            .build();
    }

    public ChargebackPenaltyDisputeContext toChargebackPenaltyDisputeContext(
        final Dispute dispute,
        final FinancialDisputeWorkflow disputeWorkflow) {
        return ChargebackPenaltyDisputeContext.builder()
            .instrumentTransactionId(dispute.getInstrumentTransactionId())
            .disputeReferenceId(dispute.getDisputeReferenceId())
            .rrn(dispute.getRrn())
            .chargebackPenaltySourceType(disputeWorkflow.getDisputeSourceType().name())
            .chargebackPenaltySourceId(disputeWorkflow.getDisputeSourceId())
            .penaltyAmount(disputeWorkflow.getPenaltyAmount())
            .build();
    }

    public ChargebackReversalDisputeContext toChargebackReversalDisputeContext(
        final Dispute dispute,
        final FinancialDisputeWorkflow disputeWorkflow) {
        return ChargebackReversalDisputeContext.builder()
            .instrumentTransactionId(dispute.getInstrumentTransactionId())
            .disputeReferenceId(dispute.getDisputeReferenceId())
            .rrn(dispute.getRrn())
            .chargebackReversalSourceType(disputeWorkflow.getDisputeSourceType().name())
            .chargebackReversalSourceId(disputeWorkflow.getDisputeSourceId())
            .reversedAmount(disputeWorkflow.getAcceptedAmount())
            .build();
    }
}
