package com.phonepe.merchant.platform.stratos.server.core.guice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.github.jknack.handlebars.Handlebars;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Scopes;
import com.google.inject.Singleton;
import com.phonepe.error.configurator.model.ErrorConfiguratorConfig;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.helpers.files.FileProcessorRegistry;
import com.phonepe.merchant.platform.stratos.server.core.helpers.files.impl.FileProcessorRegistryImpl;
import com.phonepe.merchant.platform.stratos.server.core.helpers.files.rows.FileRowProcessorRegistry;
import com.phonepe.merchant.platform.stratos.server.core.helpers.files.rows.impl.FileRowProcessorRegistryImpl;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeCreatorRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeStateMachineRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeValidationRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.impls.DisputeCreatorMapRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.impls.DisputeStateMachineMapRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.impls.DisputeValidationMapRegistry;
import com.phonepe.merchant.platform.stratos.server.core.services.*;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.*;
import com.phonepe.merchant.platform.stratos.server.core.services.AccountingEventService;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeStateMachineGraphService;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.EvidenceService;
import com.phonepe.merchant.platform.stratos.server.core.services.FileService;
import com.phonepe.merchant.platform.stratos.server.core.services.KaizenService;
import com.phonepe.merchant.platform.stratos.server.core.services.KratosService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantService;
import com.phonepe.merchant.platform.stratos.server.core.services.NetPeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.PgTransportService;
import com.phonepe.merchant.platform.stratos.server.core.services.RefundService;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.core.services.TstoreService;
import com.phonepe.merchant.platform.stratos.server.core.services.UdirService;
import com.phonepe.merchant.platform.stratos.server.core.services.WardenService;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.AccountingEventServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.DisputeServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.DisputeStateMachineGraphServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.EdcServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.EvidenceServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.FileServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.KaizenServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.KratosServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.MerchantMandateServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.MerchantServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.NetpeServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.PaymentsServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.PgTransportServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.RefundServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.RowServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.TstoreServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.UdirServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WardenServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.validator.ValidationService;
import com.phonepe.merchant.platform.stratos.server.core.validator.ValidationServiceImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services.ChargebackService;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services.impls.ChargebackServiceImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.services.NetBankingChargebackService;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.services.impl.NetBankingChargebackServiceImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.service.WalletChargebackService;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.service.impl.WalletChargebackServiceImpl;
import com.phonepe.merchants.platform.primus.core.convert.InternalJsonSchemaConverter;
import com.phonepe.merchants.platform.primus.core.convert.impl.ExternalJsonSchemaConverter;
import com.phonepe.merchants.platform.primus.core.convert.impl.FailedValidationMessageConverter;
import com.phonepe.merchants.platform.primus.core.filter.FilterCommand;
import com.phonepe.merchants.platform.primus.core.filter.impl.FilterCommandImpl;
import com.phonepe.merchants.platform.primus.core.read.impl.CsvReader;
import com.phonepe.merchants.platform.primus.core.transform.TransformCommands;
import com.phonepe.merchants.platform.primus.core.transform.impl.JsonNodeTransformer;
import com.phonepe.merchants.platform.primus.core.transform.impl.TransformCommandsImpl;
import com.phonepe.merchants.platform.primus.core.utils.SerDe;
import com.phonepe.merchants.platform.primus.core.validate.ValidationCommands;
import com.phonepe.merchants.platform.primus.core.validate.impl.JsonSchemaValidator;
import com.phonepe.merchants.platform.primus.core.validate.impl.ValidationCommandsImpl;
import io.appform.hope.core.exceptions.errorstrategy.InjectValueErrorHandlingStrategy;
import io.appform.hope.lang.HopeLangEngine;

import javax.validation.constraints.NotNull;

public class BindingModule extends AbstractModule {

    @Override
    protected void configure() {
        bind(ErrorConfiguratorConfig.class).to(StratosConfiguration.class);
        bind(CsvReader.class).in(Scopes.SINGLETON);
        bind(InternalJsonSchemaConverter.class).in(Scopes.SINGLETON);
        bind(ExternalJsonSchemaConverter.class).in(Scopes.SINGLETON);
        bind(FailedValidationMessageConverter.class).in(Scopes.SINGLETON);
        bind(FileService.class).to(FileServiceImpl.class).in(Scopes.SINGLETON);
        bind(KratosService.class).to(KratosServiceImpl.class).in(Scopes.SINGLETON);
        bind(NetPeService.class).to(NetpeServiceImpl.class).in(Scopes.SINGLETON);
        bind(KaizenService.class).to(KaizenServiceImpl.class).in(Scopes.SINGLETON);
        bind(WardenService.class).to(WardenServiceImpl.class).in(Scopes.SINGLETON);
    }

    @Singleton
    @Provides
    public @NotNull SerDe providesSerDe() {
        return new SerDe() {
            @Override
            public ObjectMapper mapper() {
                var mapper = MapperUtils.setProperties(new ObjectMapper());
                mapper.registerModule(new JavaTimeModule());
                mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
                return mapper;
            }
        };
    }

    @Provides
    @Singleton
    public @NotNull
    FilterCommand provideFilterCommand() {
        return new FilterCommandImpl(HopeLangEngine.builder()
            .errorHandlingStrategy(new InjectValueErrorHandlingStrategy())
            .build());
    }

    @Singleton
    @Provides
    public @NotNull
    FileProcessorRegistry providesFilePreProcessorRegistry(final FileProcessorRegistryImpl
        fileProcessorRegistry) {
        return fileProcessorRegistry;
    }

    @Singleton
    @Provides
    public @NotNull
    FileRowProcessorRegistry providesFileRowProcessorRegistry(final FileRowProcessorRegistryImpl
        fileRowProcessorRegistry) {
        return fileRowProcessorRegistry;
    }

    @Singleton
    @Provides
    public @NotNull
    TransformCommands providesTransformCommands(final SerDe serDe) {
        return new TransformCommandsImpl(new JsonNodeTransformer(serDe));
    }

    @Singleton
    @Provides
    public @NotNull
    ValidationCommands providesValidationCommands(final JsonSchemaValidator jsonSchemaValidator) {
        return new ValidationCommandsImpl(jsonSchemaValidator);
    }

    @Singleton
    @Provides
    public @NotNull
    JsonSchemaValidator providesSchemaValidator(
        final ExternalJsonSchemaConverter externalJsonSchemaConverter,
        final InternalJsonSchemaConverter internalJsonSchemaConverter,
        final FailedValidationMessageConverter failedValidationMessageConverter) {
        return new JsonSchemaValidator(externalJsonSchemaConverter, internalJsonSchemaConverter,
            failedValidationMessageConverter);
    }

    @Provides
    public DisputeStateMachineRegistry provideDisputeStateMachineRegistry(
        final DisputeStateMachineMapRegistry disputeStateMachineMapRegistry) {
        return disputeStateMachineMapRegistry;
    }

    @Provides
    public DisputeStateMachineGraphService provideDisputeStateMachineService(
        final DisputeStateMachineGraphServiceImpl disputeStateMachineService) {
        return disputeStateMachineService;
    }

    @Provides
    public DisputeService provideDisputeService(final DisputeServiceImpl disputeService) {
        return disputeService;
    }

    @Provides
    public EvidenceService provideEvidenceService(final EvidenceServiceImpl evidenceService) {
        return evidenceService;
    }

    @Provides
    public ChargebackService provideChargebackService(
        final ChargebackServiceImpl chargebackService) {
        return chargebackService;
    }

    @Provides
    public PaymentsService providePaymentsService(final PaymentsServiceImpl paymentsService) {
        return paymentsService;
    }

    @Provides
    public MerchantMandateService provideMerchantMandateService(final MerchantMandateServiceImpl merchantMandateService){
        return merchantMandateService;
    }

    @Provides
    public EdcService provideEdcService(final EdcServiceImpl edcService){
        return edcService;
    }

    @Provides
    public TstoreService provideTstoreService(final TstoreServiceImpl tstoreService) {
        return tstoreService;
    }

    @Provides
    public MerchantService provideMerchantService(final MerchantServiceImpl merchantService) {
        return merchantService;
    }

    @Provides
    public AccountingEventService provideAccountingEventService(
        final AccountingEventServiceImpl accountingEventService) {
        return accountingEventService;
    }

    @Provides
    @Singleton
    public Handlebars provideHandleBars() {
        return new Handlebars();
    }

    @Provides
    public PgTransportService providePgTransportService(
        final PgTransportServiceImpl pgTransportService) {
        return pgTransportService;
    }

    @Provides
    public RowService providePgMisReportService(
        final RowServiceImpl rowService) {
        return rowService;
    }

    @Provides
    public UdirService provideUdirService(
        final UdirServiceImpl udirService) {
        return udirService;
    }

    @Provides
    public DisputeCreatorRegistry provideDisputeStateMachineRegistry(
        final DisputeCreatorMapRegistry disputeCreatorMapRegistry) {
        return disputeCreatorMapRegistry;
    }

    @Provides
    @Singleton
    public DisputeValidationRegistry provideDisputeValidationRegistry(
        DisputeValidationMapRegistry disputeValidationMapRegistry) {
        return disputeValidationMapRegistry;
    }

    @Provides
    public ValidationService provideValidationService(ValidationServiceImpl validationService) {
        return validationService;
    }
    @Provides
    public AmountHoldService provideAmountHoldService(final AmountHoldServiceImpl amountHoldService) {
        return amountHoldService;
    }

    @Provides
    public NetBankingChargebackService provideNetBankingChargebackService(
        final NetBankingChargebackServiceImpl netBankingChargebackService) {
        return netBankingChargebackService;
    }

    @Provides
    public WalletChargebackService provideWalletChargebackService(
        final WalletChargebackServiceImpl walletChargebackService) {
        return walletChargebackService;
    }

    @Provides
    public RefundService provideRefundService(
        final RefundServiceImpl refundService
    ){
        return refundService;
    }
}
