package com.phonepe.merchant.platform.stratos.server.core.exception.mappers;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.StratosErrorResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;
import java.util.HashMap;

@Slf4j
@Provider
@NoArgsConstructor(onConstructor = @__({@Inject}))
public class GenericExceptionMapper implements ExceptionMapper<Exception> {

    @Override
    public Response toResponse(final Exception e) {
        log.error("Error handling request", e);
        return Response.serverError()
            .entity(StratosErrorResponse.builder()
                .errorCode(StratosErrorCodeKey.INTERNAL_SERVER_ERROR.name())
                .errorMessage(DisputeExceptionUtil.getMessage(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,new HashMap<>()))
                .build())
            .build();
    }
}
