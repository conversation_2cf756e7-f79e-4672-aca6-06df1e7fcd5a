package com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.actors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.configs.P2pmToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.P2pmToaConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowRetryMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.services.P2pmToaService;
import com.phonepe.models.payments.merchant.MerchantTransactionState.MerchantTransactionStateVisitor;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import java.util.Set;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class P2pmToaPayStatusCheckActor extends Actor<ActionType, DisputeWorkflowRetryMessage> {

    private final DisputeService disputeService;
    private final P2pmToaService p2pmToaService;
    private final P2pmToaConfig p2PMToaConfig;
    private final EventIngester eventIngester;

    @Inject
    @SuppressWarnings("java:S107")
    public P2pmToaPayStatusCheckActor(
        @P2pmToaConfiguration final P2pmToaConfig p2PMToaConfig,
        final Map<ActionType, ActorConfig> actorConfigMap,
        final ConnectionRegistry connectionRegistry,
        final ObjectMapper mapper,
        final RetryStrategyFactory retryStrategyFactory,
        final ExceptionHandlingFactory exceptionHandlingFactory,
        final DisputeService disputeService,
        final P2pmToaService p2pmToaService,
        final EventIngester eventIngester) {
        super(
            ActionType.P2PM_TOA_PAY_STATUS_CHECK_HANDLER,
            actorConfigMap.get(ActionType.P2PM_TOA_PAY_STATUS_CHECK_HANDLER),
            connectionRegistry, mapper, retryStrategyFactory,
            exceptionHandlingFactory, DisputeWorkflowRetryMessage.class,
            Set.of(JsonProcessingException.class));

        this.disputeService = disputeService;
        this.p2pmToaService = p2pmToaService;
        this.p2PMToaConfig = p2PMToaConfig;
        this.eventIngester = eventIngester;
    }

    @Override
    protected boolean handle(final DisputeWorkflowRetryMessage disputeWorkflowRetryMessage,
        final MessageMetadata messageMetadata) throws Exception {

        final var disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
            disputeWorkflowRetryMessage.getTransactionReferenceId(),
            disputeWorkflowRetryMessage.getDisputeWorkflowId());

        var txnStatus = p2pmToaService
            .getP2pmToaPayStatus(disputeWorkflow);

        return txnStatus.visit(new MerchantTransactionStateVisitor<>() {
            @Override
            public Boolean visitCreated() {
                return moveToaToPending(disputeWorkflowRetryMessage, disputeWorkflow);
            }

            @Override
            public Boolean visitCompleted() {
                return moveToaToCompleted(disputeWorkflowRetryMessage);
            }

            @Override
            public Boolean visitFailed() {
                return moveToaToFailed(disputeWorkflowRetryMessage);
            }

            @Override
            public Boolean visitAccepted() {
                return visitCreated();
            }

            @Override
            public Boolean visitCancelled() {
                return visitFailed();
            }
        });
    }

    @SneakyThrows
    private boolean moveToaToPending(DisputeWorkflowRetryMessage disputeWorkflowRetryMessage,
        DisputeWorkflow disputeWorkflow) {

        long retryCount = disputeWorkflowRetryMessage.getRetryCount();
        retryCount++;
        if (ValidationUtils.isPayStatusCheckMaxRetryReached(retryCount, p2PMToaConfig)) {
            disputeService.triggerEvent(
                Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                disputeWorkflowRetryMessage.getTransactionReferenceId(),
                disputeWorkflowRetryMessage.getDisputeWorkflowId(),
                DisputeWorkflowEvent.INITIATED_TO_PENDING_AFTER_MAX_AUTO_RETRY,
                Constants.EMPTY_TRANSITION_CONTEXT);

        } else {
            // Retrying
            this.publishWithDelay(DisputeWorkflowRetryMessage.builder()
                .transactionReferenceId(disputeWorkflowRetryMessage.getTransactionReferenceId())
                .retryCount(retryCount)
                .disputeWorkflowId(disputeWorkflowRetryMessage.getDisputeWorkflowId())
                .build(), getDelayInMillisecond(retryCount));

            // publish retry event
            eventIngester.generateEvent(
                FoxtrotEventUtils.toToaRetryEvent(disputeWorkflow, Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                    retryCount));
        }

        return true;
    }

    private boolean moveToaToCompleted(DisputeWorkflowRetryMessage disputeWorkflowRetryMessage) {
        disputeService.triggerEvent(
            Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            disputeWorkflowRetryMessage.getTransactionReferenceId(),
            disputeWorkflowRetryMessage.getDisputeWorkflowId(),
            DisputeWorkflowEvent.INITIATED_TO_COMPLETED,
            Constants.EMPTY_TRANSITION_CONTEXT);
        return true;
    }

    private boolean moveToaToFailed(DisputeWorkflowRetryMessage disputeWorkflowRetryMessage) {
        disputeService.triggerEvent(
            Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            disputeWorkflowRetryMessage.getTransactionReferenceId(),
            disputeWorkflowRetryMessage.getDisputeWorkflowId(),
            DisputeWorkflowEvent.INITIATED_TO_FAILED,
            Constants.EMPTY_TRANSITION_CONTEXT);
        return true;
    }

    private long getDelayInMillisecond(long retryCount) {
        return (retryCount + 1) * p2PMToaConfig.getPayStatusCheckIncrementalDelayInSec() * 1000;
    }
}
