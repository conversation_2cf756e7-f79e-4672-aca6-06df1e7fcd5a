package com.phonepe.merchant.platform.stratos.server.core.utils;


import com.phonepe.merchant.platform.stratos.models.disputes.commons.TransactionDetails;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.model.DisputeRefundV2;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.models.DisputedRefund;
import com.phonepe.models.payments.pay.TransactionDetail;
import lombok.experimental.UtilityClass;

@UtilityClass
public class RefundUtils {
    public DisputedRefund toDisputedRefund(
        Dispute dispute, DisputeWorkflow disputeWorkflow,
        TransactionDetail transactionDetail, String transactionId, long retryCount, long amount) {

        return DisputedRefund.builder()
            .dispute(dispute)
            .disputeWorkflow(disputeWorkflow)
            .transactionDetail(transactionDetail)
            .transactionId(transactionId)
            .amount(amount)
            .retryCount(retryCount)
            .build();

    }

    public DisputeRefundV2 toDisputedRefundV2(
        FinancialDisputeWorkflow disputeWorkflow,
        TransactionDetails transactionDetail) {

        return DisputeRefundV2.builder()
            .dispute(disputeWorkflow.getDispute())
            .transactionDetail(transactionDetail)
            .disputeWorkflow(disputeWorkflow)
            .amount(disputeWorkflow.getAcceptedAmount())
            .build();

    }
}
