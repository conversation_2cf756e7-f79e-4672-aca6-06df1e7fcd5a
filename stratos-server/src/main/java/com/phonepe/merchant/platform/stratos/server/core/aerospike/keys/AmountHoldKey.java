package com.phonepe.merchant.platform.stratos.server.core.aerospike.keys;

import com.phonepe.merchant.platform.stratos.server.core.aerospike.AerospikeKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AmountHold<PERSON>ey implements AerospikeKey {

    private String transactionId;

    @Override
    public String getKey() {
        return "HOLD_" + transactionId;
    }
}
