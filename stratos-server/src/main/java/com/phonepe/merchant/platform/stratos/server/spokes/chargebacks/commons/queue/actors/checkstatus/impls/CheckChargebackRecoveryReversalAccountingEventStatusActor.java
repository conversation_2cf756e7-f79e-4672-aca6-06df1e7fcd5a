package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.checkstatus.impls;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.clients.PlutusTransactionStatusClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AccountingEventUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.checkstatus.CheckChargebackAccountingEventStatusActor;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import java.util.Set;
import javax.inject.Singleton;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class CheckChargebackRecoveryReversalAccountingEventStatusActor extends
    CheckChargebackAccountingEventStatusActor {

    private final DisputeService disputeService;

    @Inject
    @SuppressWarnings("java:S107")
    public CheckChargebackRecoveryReversalAccountingEventStatusActor(
        final Map<ActionType, ActorConfig> actorConfigMap,
        final ConnectionRegistry connectionRegistry,
        final ObjectMapper mapper,
        final RetryStrategyFactory retryStrategyFactory,
        final ExceptionHandlingFactory exceptionHandlingFactory,
        final PlutusTransactionStatusClient plutusTransactionStatusClient,
        final DataProvider<StratosConfiguration> configDataProvider,
        final DisputeService disputeService,
        final EventIngester eventIngester) {

        super(ActionType.CHECK_CHARGEBACK_RECOVERY_REVERSAL_ACCOUNTING_EVENT_STATUS,
            actorConfigMap
                .get(ActionType.CHECK_CHARGEBACK_RECOVERY_REVERSAL_ACCOUNTING_EVENT_STATUS),
            connectionRegistry, mapper, retryStrategyFactory,
            exceptionHandlingFactory, DisputeWorkflowMessage.class,
            Set.of(JsonProcessingException.class),
            plutusTransactionStatusClient, configDataProvider, eventIngester);
        this.disputeService = disputeService;
    }

    @Override
    protected boolean handle(
        final DisputeWorkflowMessage disputeWorkflowMessage,
        final MessageMetadata messageMetadata) {

        final var disputeWorkflowId = disputeWorkflowMessage.getDisputeWorkflowId();
        final var transactionReferenceId = disputeWorkflowMessage.getTransactionReferenceId();

        final var disputeWorkflow = disputeService
            .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);
        final var dispute = disputeWorkflow.getDispute();

        final var financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            disputeWorkflow);

        checkAccountingEventStatusAndGenerateFoxtrotEvent(
            disputeWorkflow, dispute,
            EventGenerationType.CHARGEBACK_RECOVERY_REVERSAL,
            financialDisputeWorkflow.getAcceptedAmount());

        disputeService.triggerEvent(
            Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            transactionReferenceId, disputeWorkflowId,
            DisputeWorkflowEvent.ACCEPT_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        return true;
    }

    @Override
    protected String getEventId(final DisputeWorkflow disputeWorkflow,
            final EventGenerationType eventGenerationType,
            final long amount) {
        return AccountingEventUtils
            .toAccountingEventId(
                disputeWorkflow.getDisputeWorkflowId(),
                eventGenerationType);
    }
}
