package com.phonepe.merchant.platform.stratos.server.core.resources;


import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowDownloadRequest;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowHistoryRequest;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowReplayRequest;
import com.phonepe.merchant.platform.stratos.models.row.requests.StratosRowSignalRequest;
import com.phonepe.merchant.platform.stratos.models.row.response.RowHistoryResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Set;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/row")
@Tag(name = "Row Related APIs")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class RowResource {

    private final RowService rowService;
    private final OlympusIMClient olympusIMClient;

    @POST
    @AccessAllowed
    @ExceptionMetered
    @Path("/process")
    @Consumes(MediaType.APPLICATION_JSON)
    @Operation(summary = "Process Mis Report Signal for Pg Chargebacks")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void processSignal(
            @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
            @Valid final StratosRowSignalRequest rowSignalRequest) {
        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        AuthorizationUtils.authorizeRowSignal(olympusIMClient, userAuthDetails,
                rowSignalRequest.getRowSignalContext()
                        .getRowType());
        log.info("Processing signal with payload : {}", rowSignalRequest);
        rowService.processSignal(rowSignalRequest);
    }


    @POST
    @ExceptionMetered
    @Path("/history")
    @AccessAllowed
    @Consumes(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get Rows of Specific Type within Date Range")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public RowHistoryResponse history(@Valid RowHistoryRequest rowHistoryRequest,
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {
        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        AuthorizationUtils.authorizeRowHistory(olympusIMClient, userAuthDetails, rowHistoryRequest.getRowTypes());
        return rowService.history(rowHistoryRequest);
    }

    @RolesAllowed("dispute/row-replay")
    @POST
    @ExceptionMetered
    @Path("/replay")
    @Operation(summary = "Replay the given row")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void replay(@Valid RowReplayRequest rowReplayRequest) {
        rowService.replay(rowReplayRequest);
    }

    @POST
    @ExceptionMetered
    @Path("/download")
    @AccessAllowed
    @Produces({Constants.MEDIA_TYPE_CSV, MediaType.APPLICATION_JSON})
    @Operation(summary = "Download Rows of Specific Type within Date Range")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response download(@Valid RowDownloadRequest rowDownloadRequest,
        @Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal) {

        final UserAuthDetails userAuthDetails = serviceUserPrincipal.getUserAuthDetails();
        AuthorizationUtils.authorizeRowHistory(olympusIMClient, userAuthDetails,
            Set.of(rowDownloadRequest.getRowType()));
        return Response.ok(rowService.download(rowDownloadRequest))
            .header("Content-Disposition", "attachment;filename=\"row_summary.csv\"")
            .build();
    }
}