package com.phonepe.merchant.platform.stratos.server.spokes.toa.common.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.commons.ContextType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.toa.responses.ToaSummary;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.ToaAggregationCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.ToaAggregationKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.KillSwitchClient;
import com.phonepe.merchant.platform.stratos.server.core.configs.toa.ToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.ToaThresholdBreachNotificationEvent;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.ToaDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.ToaDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowStateNonMandatoryVisitor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.ToaSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.core.resolver.MerchantIdResolver;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.visitors.ToaFilterVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.registry.PayStrategyRegistry;
import com.phonepe.merchants.platform.notificationbundle.client.NotificationClient;
import com.phonepe.merchants.platform.notificationbundle.models.alerts.AlertTemplate;
import com.phonepe.merchants.platform.notificationbundle.models.alerts.AlertType;
import com.phonepe.merchants.platform.notificationbundle.models.alerts.receivers.AlertReceiver;
import com.phonepe.merchants.platform.notificationbundle.models.alerts.receivers.EmailReceiver;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.platform.killswitch.common.RecommendedAction;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

@Slf4j
@Singleton
public class ToaService {

    public static final String RECONCILE_FAILURE_MESSAGE = "Toa Reconcile is not supported for this state. Supported states are %s";
    public static final String RETRY_FAILURE_MESSAGE = "Toa Retry is not supported for this state. Supported states are %s";
    private final KillSwitchClient killSwitchClient;
    private final DisputeService disputeService;
    private final ToaFilterVisitor toaFilterVisitor;
    private final ToaDisputeMetadataRepository toaDisputeMetadataRepository;
    private final PayStrategyRegistry payStrategyRegistry;
    private final PaymentsService paymentsService;
    private final EventIngester eventIngester;
    private final ToaAggregationCommand toaAggregationCommand;
    private final Map<DisputeType, ToaConfig> toaConfigMap;
    private final NotificationClient notificationClient;
    private final ToaSummaryFileFormatVisitor toaSummaryFileFormatVisitor;
    private final DisputeWorkflowRepository disputeWorkflowRepository;
    private final MerchantIdResolver merchantIdResolver;
    private final Set<Integer> filterableStates = Set.of(DisputeWorkflowState.RECEIVED.ordinal(),
            DisputeWorkflowState.TOA_COMPLETED_EXTERNALLY.ordinal(), DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS.ordinal());

    @Inject
    @SuppressWarnings("java:S107")
    public ToaService(KillSwitchClient killSwitchClient,
                      DisputeService disputeService,
                      ToaFilterVisitor toaFilterVisitor,
                      ToaDisputeMetadataRepository toaDisputeMetadataRepository,
                      PayStrategyRegistry payStrategyRegistry,
                      PaymentsService paymentsService,
                      EventIngester eventIngester,
                      ToaAggregationCommand toaAggregationCommand,
                      Map<DisputeType, ToaConfig> toaConfigMap,
                      NotificationClient notificationClient,
                      ToaSummaryFileFormatVisitor toaSummaryFileFormatVisitor,
                      DisputeWorkflowRepository disputeWorkflowRepository,
                      MerchantIdResolver merchantIdResolver) {
        this.killSwitchClient = killSwitchClient;
        this.disputeService = disputeService;
        this.toaFilterVisitor = toaFilterVisitor;
        this.toaDisputeMetadataRepository = toaDisputeMetadataRepository;
        this.payStrategyRegistry = payStrategyRegistry;
        this.paymentsService = paymentsService;
        this.eventIngester = eventIngester;
        this.toaAggregationCommand = toaAggregationCommand;
        this.toaConfigMap = toaConfigMap;
        this.notificationClient = notificationClient;
        this.toaSummaryFileFormatVisitor = toaSummaryFileFormatVisitor;
        this.disputeWorkflowRepository = disputeWorkflowRepository;
        this.merchantIdResolver = merchantIdResolver;
    }

    public boolean isKillSwitchEnabled(final DisputeType disputeType) {

        var killSwitch = killSwitchClient.getToaKillSwitch(disputeType);
        return killSwitch.isPresent() &&
                killSwitch.get().getDuration() > 0 &&
                killSwitch.get().getRecommendedAction() == RecommendedAction.BLOCK;
    }

    public void initiatePay(final DisputeWorkflow disputeWorkflow) {
        val payStrategy = payStrategyRegistry.getPayStrategyFromDisputeType(disputeWorkflow.getDisputeType());
        payStrategy.pay(disputeWorkflow);
    }

    public MerchantTransactionState getToaPaymentStatus(DisputeWorkflow disputeWorkflow) {

        List<ToaDisputeMetadata> disputeMetadataList = getAllToaMetadata(disputeWorkflow);

        String merchantOrderId = disputeMetadataList.get(0).getMerchantOrderId();

        return paymentsService.getMerchantTransactionStatus(
                merchantIdResolver.getMerchantIdForDisputeWorkflow(disputeWorkflow), merchantOrderId);
    }

    public List<ToaDisputeMetadata> getAllToaMetadata(DisputeWorkflow disputeWorkflow) {
        return toaDisputeMetadataRepository.selectAllToaDisputeMetadata(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId());
    }

    public Set<DisputeWorkflowState> getAllStates() {

        return Arrays.stream(DisputeWorkflowState.values())
                // Get only the toa related states
                // all states between TOA_BLOCKED_DUE_TO_KS and TOA_FAILED_AFTER_MAX_AUTO_RETRY and RECEIVED state
                .filter(
                        state -> (state.ordinal() >= DisputeWorkflowState.TOA_OPENED.ordinal() &&
                                state.ordinal() <= DisputeWorkflowState.TOA_CLOSED.ordinal() || filterableStates.contains(state.ordinal())
                        ))
                .collect(Collectors.toSet());
    }

    public List<ToaSummary> filter(final DisputeFilter filter) {
        List<DisputeWorkflow> disputeWorkflowList = filter.accept(toaFilterVisitor);
        return disputeWorkflowList.stream() // NOSONAR
                .map(TransformationUtils::toToaSummary)
                .toList();
    }

    public GenericResponse<ToaSummary> reconcile(final String transactionReferenceId,
                                                 final String workflowId,
                                                 UserAuthDetails userAuthDetails) {

        val disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(transactionReferenceId,
                workflowId);

        if (!Constants.TOA_RECONCILE_STATES.contains(disputeWorkflow.getCurrentState())) {
            return GenericResponse.<ToaSummary>builder()
                    .success(false)
                    .data(TransformationUtils.toToaSummary(disputeWorkflow))
                    .message(String.format(RECONCILE_FAILURE_MESSAGE,
                            String.join(",", Constants.TOA_RECONCILE_STATES.stream().map(Enum::name).toList())))
                    .build();
        }

        var txnStatus = getToaPaymentStatus(disputeWorkflow);

        boolean isEventTriggered = txnStatus.visit(new MerchantTransactionState.MerchantTransactionStateVisitor<>() {
            @Override
            public Boolean visitCreated() {
                //still in pending
                return false;
            }

            @Override
            public Boolean visitCompleted() {
                triggerEvent(userAuthDetails, disputeWorkflow,
                        DisputeWorkflowEvent.PENDING_TO_COMPLETED);
                return true;
            }

            @Override
            public Boolean visitFailed() {
                triggerEvent(userAuthDetails, disputeWorkflow,
                        DisputeWorkflowEvent.PENDING_TO_FAILED);
                return true;
            }

            @Override
            public Boolean visitAccepted() {
                //still in pending
                return false;
            }

            @Override
            public Boolean visitCancelled() {
                return visitFailed();
            }

        });

        var response = GenericResponse.<ToaSummary>builder()
                .success(isEventTriggered)
                .message("Toa pay status is " + txnStatus.name())
                .build();
        // fetch latest workflow after event trigger
        if (isEventTriggered) {
            var latestDisputeWorkflow = disputeService.validateAndGetDisputeWorkflow(transactionReferenceId,
                    workflowId);
            response.setData(TransformationUtils.toToaSummary(latestDisputeWorkflow));
        } else {
            response.setData(TransformationUtils.toToaSummary(disputeWorkflow));
        }
        return response;
    }

    public GenericResponse<ToaSummary> retry(final String transactionReferenceId,
                                             final String workflowId,
                                             UserAuthDetails userAuthDetails) {
        final var disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
                transactionReferenceId, workflowId);
        final var toaRetryStates = Constants.TOA_RETRY_STATES;

        if (!toaRetryStates.contains(disputeWorkflow.getCurrentState())) {
            return GenericResponse.<ToaSummary>builder()
                    .success(false)
                    .data(TransformationUtils.toToaSummary(disputeWorkflow))
                    .message(String.format(RETRY_FAILURE_MESSAGE,
                            String.join(",", toaRetryStates.stream().map(Enum::name).toList())))
                    .build();
        }

        final String[] message = new String[1];

        boolean isEventTriggered = checkStatusOfToaAndInitiate(disputeWorkflow, userAuthDetails, message);

        // fetch latest workflow after event trigger
        DisputeWorkflow latestDisputeWorkflow = isEventTriggered ?
                disputeService.validateAndGetDisputeWorkflow(transactionReferenceId, workflowId) : disputeWorkflow;

        return GenericResponse.<ToaSummary>builder()
                .success(isEventTriggered)
                .data(TransformationUtils.toToaSummary(latestDisputeWorkflow))
                .message(message[0])
                .build();
    }

    public Boolean isMerchantTransactionStatusFailed(final String merchantOrderId, final String mId) {

        MerchantTransactionState merchantTransactionState = paymentsService.getMerchantTransactionStatus(
                mId, merchantOrderId);

        return Constants.TXN_FAILED_STATES.contains(merchantTransactionState);
    }

    private void triggerEvent(final UserAuthDetails userAuthDetails, final DisputeWorkflow disputeWorkflow, final DisputeWorkflowEvent disputeWorkflowEvent) {
        disputeService.triggerEvent(
                userAuthDetails,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                disputeWorkflowEvent,
                Constants.EMPTY_TRANSITION_CONTEXT);
    }

    public void validateToaAmountThresholdBreach(final DisputeWorkflow disputeWorkflow) {
        if (disputeWorkflow.getCurrentState() != DisputeWorkflowState.TOA_COMPLETED) {
            return;
        }

        long todayProcessedToaAmount = toaAggregationCommand.incrementByValueAndGetCounter(
                ToaAggregationKey.builder()
                        .disputeType(disputeWorkflow.getDisputeType())
                        .date(LocalDate.now())
                        .build(),
                disputeWorkflow.getDisputedAmount());

        val toaConfig = toaConfigMap.get(disputeWorkflow.getDisputeType());

        if (todayProcessedToaAmount > toaConfig.getToaPerDayNotificationThreshold()) {
            sendEmailForThresholdBreach(todayProcessedToaAmount, disputeWorkflow, toaConfig);
            if (toaConfig.isApplyKSOnDayLevelAmountThresholdBreached()) {
                engageKillSwitch(disputeWorkflow.getDisputeType(), "Day level amount threshold breached",
                        Constants.STRATOS_SYSTEM_USER_OLYMPUS);
            }
        }
    }

    public void sendEmailForThresholdBreach(final long breachAmount, final DisputeWorkflow disputeWorkflow, final ToaConfig toaConfig) {

        String date = DateTimeFormatter.ofPattern("dd-MM-yyyy").format(LocalDate.now());
        double thresholdAmountInRs = toaConfig.getToaPerDayNotificationThreshold() * 1.0 / 100;
        double breachAmountInRs = breachAmount * 1.0 / 100;
        double diffInRs = breachAmountInRs - thresholdAmountInRs;
        List<String> alertReceivers = toaConfig.getBreachNotificationEmailIds();


        AlertTemplate alertTemplate = AlertTemplate.builder()
                .alertType(AlertType.EMAIL)
                .templateBody(String.format(Constants.TOA_BREACH_EMAIL_TEMPLATE,
                        date, thresholdAmountInRs, breachAmountInRs, diffInRs))
                .templateSubject(String.format("%s Threshold Breach %s", disputeWorkflow.getDisputeType(), date))
                .build();
        try {
            alertReceivers.forEach(receiverEmailId -> notify(receiverEmailId,alertTemplate,disputeWorkflow));
        } catch (Exception e) {
            log.error("Sent email failed", e);
        }

        eventIngester.generateEvent(ToaThresholdBreachNotificationEvent.builder()
                .breachAmount(breachAmount)
                .subject(alertTemplate.getTemplateSubject())
                .toEmailId(String.join(",", toaConfig.getBreachNotificationEmailIds()))
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .build());
    }

    public boolean checkStatusOfToaAndInitiate(DisputeWorkflow disputeWorkflow, UserAuthDetails userAuthDetails, String[] message) {


        return disputeWorkflow.getCurrentState()
                .accept(new DisputeWorkflowStateNonMandatoryVisitor<>() {

                    @Override
                    public Boolean visitToaBlockedDueToKs() {
                        triggerEvent(userAuthDetails, disputeWorkflow, DisputeWorkflowEvent.TOA_BLOCKED_TO_RECEIVED);
                        message[0] = "TOA moved to RECEIVED state";
                        return true;
                    }

                    @Override
                    public Boolean visitToaInitiationFailed() {
                        // Re-initiate TOA in SYNC
                        try {
                            initiatePay(disputeWorkflow);
                            triggerEvent(userAuthDetails, disputeWorkflow, DisputeWorkflowEvent.RE_INITIATE_TOA);
                            eventIngester.generateEvent(
                                    FoxtrotEventUtils.toToaStatusEvent(disputeWorkflow, DisputeWorkflowEvent.TOA_INITIATED.name()));
                            return true;
                        } catch (Exception e) {
                            message[0] = e.getMessage();
                            log.info("TOA re-initiate failed", e);
                            return false;
                        }
                    }

                    @Override
                    public Boolean visitToaFailedAfterMaxAutoRetry() {
                        message[0] = "Retrying TOA after max auto retry reached is not allowed";
                        return false;
                    }
                });
    }

    public GenericResponse<ToaSummary> processToaExternally(String transactionReferenceId,
                                                            String disputeWorkflowId,
                                                            TransitionContext transitionContext,
                                                            UserAuthDetails userAuthDetails) {

        final var disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
                transactionReferenceId, disputeWorkflowId);
        ValidationUtils.validateExternallyCompletableToaState(disputeWorkflow);
        var disputeWorkflowEvent = disputeWorkflow.getCurrentState()
                .accept(new DisputeWorkflowStateNonMandatoryVisitor<DisputeWorkflowEvent>() {
                    @Override
                    public DisputeWorkflowEvent visitToaBlockedDueToKs() {
                        return DisputeWorkflowEvent.TOA_BLOCKED_TO_EXTERNAL_PROCESSED;
                    }

                    @Override
                    public DisputeWorkflowEvent visitToaInitiationFailed() {
                        return DisputeWorkflowEvent.INITIATION_FAILED_TO_EXTERNAL_PROCESSED;
                    }

                    @Override
                    public DisputeWorkflowEvent visitToaFailedAfterMaxAutoRetry() {
                        if (DisputeType.BBPS_TAT_BREACH_TOA.equals(disputeWorkflow.getDisputeType())) {
                            return ContextType.TOA_CONTEXT.equals(transitionContext.getContextType()) ?
                                    DisputeWorkflowEvent.MAX_RETRY_TO_PROCESSED_EXTERNALLY :
                                    DisputeWorkflowEvent.MAX_RETRY_TO_UNABLE_TO_PROCESS;
                        }
                        return DisputeWorkflowEvent.MAX_RETRY_TO_PROCESSED_EXTERNALLY;
                    }
                });

        disputeService.triggerEvent(
                userAuthDetails,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                disputeWorkflowEvent,
                transitionContext);

        var newDisputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
                transactionReferenceId, disputeWorkflowId);

        return GenericResponse.<ToaSummary>builder()
                .success(true)
                .data(TransformationUtils.toToaSummary(newDisputeWorkflow))
                .build();
    }

    public boolean engageKillSwitch(DisputeType disputeType, String reason, UserAuthDetails userAuthDetails) {
        if (Objects.isNull(reason) || reason.isBlank()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.EMPTY_COMMENT_NOT_ALLOWED, Map.of());
        }
        if (isKillSwitchEnabled(disputeType)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.KS_ALREADY_ENGAGED, Map.of());
        }
        return !Objects.isNull(killSwitchClient.engageKillSwitch(reason, userAuthDetails, disputeType));

    }

    public Boolean deactivateKillSwitch(final DisputeType disputeType) {
        var toaKillSwitch = killSwitchClient.getToaKillSwitch(disputeType);
        if (toaKillSwitch.isEmpty()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.KS_NOT_ENGAGED, Map.of(Constants.MESSAGE, "TOA Kill switch is not engaged."));
        }
        return !Objects.isNull(killSwitchClient.deactivateKillSwitch(toaKillSwitch.get().getId()));
    }

    public byte[] download(final FileFormat fileFormat, final DisputeFilter filter) {

        val disputePairs = filter.accept(toaFilterVisitor);
        val toaSummaries = disputePairs.stream()
                .map(TransformationUtils::toDisputeSummaryRow)
                .collect(Collectors.toList());

        return fileFormat.accept(toaSummaryFileFormatVisitor, toaSummaries);
    }

    public ToaSummary getToaStatusBasedOnDisputeReferenceId(final DisputeType disputeType, final String disputeReferenceId) {

        DisputeWorkflow disputeWorkflow = disputeWorkflowRepository.select(disputeType, disputeReferenceId)
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                        Map.of(Constants.MESSAGE, String.format("dispute workflow not found for disputeReferenceId %s", disputeReferenceId))));
        return TransformationUtils.toToaSummary(disputeWorkflow);

    }

    private void notify(String receiverEmailId, AlertTemplate alertTemplate, DisputeWorkflow disputeWorkflow) {
        AlertReceiver alertReceiver = EmailReceiver.builder()
                .id(disputeWorkflow.getDisputeWorkflowId())
                .emailId(receiverEmailId)
                .enableRetry(true)
                .build();
        notificationClient.sendNotification(alertReceiver, alertTemplate, null);
    }
}
