package com.phonepe.merchant.platform.stratos.server.core.visitors;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.RefundEligibilityResponse;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class RefundEligibilityStateVisitorV2 extends RefundEligibilityStateVisitor {
    private final DisputeWorkflow disputeWorkflow;

    @Override
    public RefundEligibilityResponse visitRecoverChargebackRequested() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }
    @Override
    public RefundEligibilityResponse visitRecoverChargebackRejected() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }
    @Override
    public RefundEligibilityResponse visitRecoverChargebackApproved() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverChargebackEventRaised() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitRecoverChargebackEventAccepted() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredChargebackRequested() {
        return processBasedOnAcceptance();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredChargebackApproved() {
        return processBasedOnAcceptance();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredChargebackEventRaised() {
        return processBasedOnAcceptance();
    }

    @Override
    public RefundEligibilityResponse visitReversalOfRecoveredChargebackEventAccepted() {
        return processBasedOnAcceptance();
    }


    @Override
    public RefundEligibilityResponse visitHold() {
        return processBasedOnAcceptance();
    }

    @Override
    public RefundEligibilityResponse visitEnd() {
        return processBasedOnAcceptance();
    }

    private RefundEligibilityResponse processBasedOnAcceptance(){
        long acceptedAmount = 0;
        if(disputeWorkflow instanceof FinancialDisputeWorkflow)
            acceptedAmount = ((FinancialDisputeWorkflow) disputeWorkflow).getAcceptedAmount();
        if(acceptedAmount > 0){
            return TransformationUtils.getFailedResponseForRefundEligibility();
        }
        return TransformationUtils.getProcessResponseForRefundEligibility();
    }
    @Override
    public RefundEligibilityResponse visitMerchantActionRequested() {
        return TransformationUtils.getHoldResponseForRefundEligibility();
    }

}