package com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.v2.actions.ResetAction;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class CancelWorkflowConfigurator implements WorkflowConfigurator<DisputeWorkflowState, DisputeWorkflowEvent> {

    private final ResetAction resetAction;

    @Override
    @SneakyThrows
    public void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .target(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
                .action(resetAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .target(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
                .action(resetAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
                .target(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
                .action(resetAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .target(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
                .action(resetAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.HOLD)
                .target(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
                .action(resetAction)
                .and();

    }
}
