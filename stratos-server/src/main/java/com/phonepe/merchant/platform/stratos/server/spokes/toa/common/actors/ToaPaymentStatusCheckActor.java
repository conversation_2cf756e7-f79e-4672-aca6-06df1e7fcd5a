package com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.configs.toa.ToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowRetryMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.services.ToaService;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.validator.ToaRetryValidator;
import com.phonepe.models.payments.merchant.MerchantTransactionState.MerchantTransactionStateVisitor;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.Map;
import java.util.Set;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class ToaPaymentStatusCheckActor extends Actor<ActionType, DisputeWorkflowRetryMessage> {

    private final DisputeService disputeService;
    private final EventIngester eventIngester;
    private final ToaService toaService;
    private final Provider<StateChangeHandlerActor> stateChangeHandlerActorProvider;
    private final Map<DisputeType, ToaConfig> toaConfigMap;
    private final ToaRetryValidator toaRetryValidator;

    @Inject
    @SuppressWarnings("java:S107")
    protected ToaPaymentStatusCheckActor(
            final Map<ActionType, ActorConfig> actorConfigMap,
            final ConnectionRegistry connectionRegistry,
            final ObjectMapper mapper,
            final RetryStrategyFactory retryStrategyFactory,
            final ExceptionHandlingFactory exceptionHandlingFactory, DisputeService disputeService,
            EventIngester eventIngester,
            ToaService toaService,
            Provider<StateChangeHandlerActor> stateChangeHandlerActorProvider,
            Map<DisputeType, ToaConfig> toaConfigMap,
            ToaRetryValidator toaRetryValidator) {
        super(ActionType.TOA_PAY_STATUS_CHECK_HANDLER,
                actorConfigMap.get(ActionType.TOA_PAY_STATUS_CHECK_HANDLER),
                connectionRegistry, mapper, retryStrategyFactory,
                exceptionHandlingFactory, DisputeWorkflowRetryMessage.class,
                Set.of(JsonProcessingException.class));
        this.disputeService = disputeService;
        this.eventIngester = eventIngester;
        this.toaService = toaService;
        this.stateChangeHandlerActorProvider = stateChangeHandlerActorProvider;
        this.toaConfigMap = toaConfigMap;
        this.toaRetryValidator = toaRetryValidator;
    }

    @Override
    protected boolean handle(final DisputeWorkflowRetryMessage disputeWorkflowRetryMessage, final MessageMetadata messageMetadata) throws Exception {

        val disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
                disputeWorkflowRetryMessage.getTransactionReferenceId(),
                disputeWorkflowRetryMessage.getDisputeWorkflowId());

        val paymentStatus = toaService.getToaPaymentStatus(disputeWorkflow);

        return paymentStatus.visit(new MerchantTransactionStateVisitor<>() {

            @Override
            public Boolean visitCreated() {
                return moveToaToPending(disputeWorkflowRetryMessage, disputeWorkflow);
            }

            @Override
            @SneakyThrows
            public Boolean visitCompleted() {
                moveToaStatus(disputeWorkflowRetryMessage,
                        DisputeWorkflowEvent.INITIATED_TO_COMPLETED);
                return true;
            }

            @Override
            public Boolean visitFailed() {
                moveToaStatus(disputeWorkflowRetryMessage,
                        DisputeWorkflowEvent.INITIATED_TO_FAILED);
                return true;
            }

            @Override
            public Boolean visitAccepted() {
                return visitCreated();
            }

            @Override
            public Boolean visitCancelled() {
                return visitFailed();
            }
        });
    }

    @SneakyThrows
    private boolean moveToaToPending(final DisputeWorkflowRetryMessage disputeWorkflowRetryMessage,
                                     final DisputeWorkflow disputeWorkflow) {

        long retryCount = disputeWorkflowRetryMessage.getRetryCount();
        retryCount++;
        if (toaRetryValidator.isPayStatusCheckMaxRetryReached(retryCount, disputeWorkflow.getDisputeType())) {
            moveToaStatus(disputeWorkflowRetryMessage, DisputeWorkflowEvent.INITIATED_TO_PENDING_AFTER_MAX_AUTO_RETRY);
        } else {
            // Retrying
            log.info("Retrying payment check for transactionId {} with retry count {}", disputeWorkflow.getTransactionReferenceId(), retryCount);
            this.publishWithDelay(DisputeWorkflowRetryMessage.builder()
                    .transactionReferenceId(disputeWorkflowRetryMessage.getTransactionReferenceId())
                    .retryCount(retryCount)
                    .disputeWorkflowId(disputeWorkflowRetryMessage.getDisputeWorkflowId())
                    .build(), getDelayInMillisecond(retryCount, toaConfigMap.get(disputeWorkflow.getDisputeType())));

            // publish retry event
            eventIngester.generateEvent(
                    FoxtrotEventUtils.toToaRetryEvent(disputeWorkflow, Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                            retryCount));
        }
        return true;
    }

    private long getDelayInMillisecond(long retryCount, ToaConfig toaConfig) {
        return (retryCount + 1) * toaConfig.getPayStatusCheckIncrementalDelayInSec() * 1000;
    }

    @SneakyThrows
    private void moveToaStatus(final DisputeWorkflowRetryMessage disputeWorkflowRetryMessage, final DisputeWorkflowEvent disputeWorkflowEvent) {
        stateChangeHandlerActorProvider.get().publish(DisputeWorkflowMessage.builder()
                .disputeWorkflowEvent(disputeWorkflowEvent)
                .transactionReferenceId(disputeWorkflowRetryMessage.getTransactionReferenceId())
                .disputeWorkflowId(disputeWorkflowRetryMessage.getDisputeWorkflowId())
                .build());
    }

}
