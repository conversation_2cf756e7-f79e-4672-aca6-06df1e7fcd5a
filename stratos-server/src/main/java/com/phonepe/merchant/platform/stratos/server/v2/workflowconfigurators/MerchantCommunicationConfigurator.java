package com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class MerchantCommunicationConfigurator implements WorkflowConfigurator<DisputeWorkflowState, DisputeWorkflowEvent> {

    private final UpdateDisputeStateAction updateDisputeStateAction;

    private final FraudCheckDisputeAction fraudCheckUpdateDisputeAction;

    private final UnblockRefundAction unblockRefundAction;

    @Override
    @SneakyThrows
    public void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
                .withExternal()
                .source(DisputeWorkflowState.RECOVER_HOLD_EVENT_ACCEPTED)
                .target(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .event(DisputeWorkflowEvent.REQUEST_MERCHANT_ACTION)
                .action(fraudCheckUpdateDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .target(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
                .event(DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
                .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
                .action(unblockRefundAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .target(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
                .event(DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
                .action(fraudCheckUpdateDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
                .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
                .action(fraudCheckUpdateDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
                .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
                .action(unblockRefundAction)
                .and();

    }
}
