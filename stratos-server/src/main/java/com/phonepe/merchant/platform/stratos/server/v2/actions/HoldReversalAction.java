package com.phonepe.merchant.platform.stratos.server.v2.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds.ChargebackRecoveryReversalFeedPublishActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.raiseevents.RaiseHoldRecoveryReversalAccountingEventActor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class HoldReversalAction extends UpdateDisputeStateBaseAction {

    private final Provider<RaiseHoldRecoveryReversalAccountingEventActor> raiseAccountingEventActor;

    private final Provider<ChargebackRecoveryReversalFeedPublishActor> chargebackRecoveryReversalFeedPublishActor;

    @Inject
    @SuppressWarnings("java:S107")
    public HoldReversalAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final Provider<RaiseHoldRecoveryReversalAccountingEventActor> raiseAccountingEventActor,
            final Provider<ChargebackRecoveryReversalFeedPublishActor> chargebackRecoveryReversalFeedPublishActor,
            final EventIngester eventIngester,
            final CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.raiseAccountingEventActor = raiseAccountingEventActor;
        this.chargebackRecoveryReversalFeedPublishActor = chargebackRecoveryReversalFeedPublishActor;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final DisputeWorkflow disputeWorkflow) {
        final var disputeWorkflowMessage = DisputeWorkflowMessage.builder()
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .build();
        chargebackRecoveryReversalFeedPublishActor.get().publish(disputeWorkflowMessage);
        raiseAccountingEventActor.get().publish(disputeWorkflowMessage);
    }
}
