package com.phonepe.merchant.platform.stratos.server.v2.statemachines.upi;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.CreditDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.TerminalStateListener;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseHoldRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.actions.UpiChargebackCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.ChargebackRecoveryAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.HoldReversalAction;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.BaseStateMachineV2;
import com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators.*;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
public class UPIFirstLevelChargebackV2 extends BaseStateMachineV2 {

    private final UpiChargebackCreateEntryAction upiChargebackCreateEntryAction;
    private final CreditDisputeAction creditDisputeAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final InternalMIDWorkflowConfigurator internalMIDWorkflowConfigurator;
    private final PartialFulfilmentWorkflowConfigurator partialFulfilmentWorkflowConfigurator;
    private final RaiseHoldRecoveryReversalAccountingEventAction raiseHoldRecoveryReversalAccountingEventAction;
    private final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction;
    private final AcceptDisputeAction acceptDisputeAction;
    private final ChargebackRecoveryAction recoverChargebackAction;
    private final HoldReversalAction holdReversalAction;

    @Inject
    @SuppressWarnings("java:S107")
    public UPIFirstLevelChargebackV2(
            final TransitionLockCommand transitionLockCommand,
            final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
            final ProactiveHoldWorkflowConfigurator proactiveHoldWorkflowConfigurator,
            final MerchantCommunicationConfigurator merchantCommunicationConfigurator,
            final FraudDetectionConfigurator fraudDetectionConfigurator,
            final UpiChargebackCreateEntryAction upiChargebackCreateEntryAction,
            final CreditDisputeAction creditDisputeAction,
            final UpdateDisputeStateAction updateDisputeStateAction,
            final InternalMIDWorkflowConfigurator internalMIDWorkflowConfigurator,
            final PartialFulfilmentWorkflowConfigurator partialFulfilmentWorkflowConfigurator,
            final FraudCheckDisputeAction fraudCheckDisputeAction,
            final CancelWorkflowConfigurator cancelWorkflowConfigurator,
            final ReverseHoldRecoverCBConfigurator reverseHoldRecoverCBConfigurator,
            final RaiseHoldRecoveryReversalAccountingEventAction raiseHoldRecoveryReversalAccountingEventAction,
            final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction,
            final AcceptDisputeAction acceptDisputeAction,
            final ChargebackRecoveryAction recoverChargebackAction,
            final HoldReversalAction holdReversalAction,
            final TerminalStateListener<DisputeWorkflowState, DisputeWorkflowEvent> terminalStateListener) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, reverseHoldRecoverCBConfigurator, proactiveHoldWorkflowConfigurator, merchantCommunicationConfigurator,
                fraudDetectionConfigurator, fraudCheckDisputeAction,cancelWorkflowConfigurator, terminalStateListener);
        this.upiChargebackCreateEntryAction = upiChargebackCreateEntryAction;
        this.creditDisputeAction = creditDisputeAction;
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.internalMIDWorkflowConfigurator = internalMIDWorkflowConfigurator;
        this.partialFulfilmentWorkflowConfigurator = partialFulfilmentWorkflowConfigurator;
        this.raiseHoldRecoveryReversalAccountingEventAction = raiseHoldRecoveryReversalAccountingEventAction;
        this.raiseAccountingEventAction = raiseAccountingEventAction;
        this.acceptDisputeAction = acceptDisputeAction;
        this.recoverChargebackAction = recoverChargebackAction;
        this.holdReversalAction = holdReversalAction;
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
                .disputeType(DisputeType.UPI_CHARGEBACK)
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .disputeWorkflowVersion(DisputeWorkflowVersion.V2)
                .build();
    }

    @Override
    @SneakyThrows
    public void addTransitions(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        internalMIDWorkflowConfigurator.configure(transitions);
        partialFulfilmentWorkflowConfigurator.configure(transitions);
        transitions
                .withInternal()
                .source(DisputeWorkflowState.RECEIVED)
                .event(DisputeWorkflowEvent.CREATE_ENTRY)
                .action(upiChargebackCreateEntryAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .target(DisputeWorkflowState.CREDIT_RECEIVED)
                .event(DisputeWorkflowEvent.RECEIVE_CREDIT)
                .action(creditDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
                .target(DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED)
                .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_CREDIT)
                .action(creditDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.RGCS_ACCEPTANCE_REQUIRED)
                .event(DisputeWorkflowEvent.REQUEST_RGCS_ACCEPTANCE)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RGCS_ACCEPTANCE_REQUIRED)
                .target(DisputeWorkflowState.RGCS_ACCEPTANCE_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_RGCS_ACCEPTANCE)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RGCS_ACCEPTANCE_COMPLETED)
                .target(DisputeWorkflowState.END)
                .event(DisputeWorkflowEvent.END_WORKFLOW)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .target(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_ACCEPTANCE)
                .action(acceptDisputeAction)
                .and()

        /* CREDIT_RECEIVED, PARTIAL_CREDIT_RECEIVED and ACCEPTANCE_COMPLETED are terminal states in UPI FL
         * after these, flow will be automated. Following transitions are for that automated flow
         */
                .withExternal()
                .source(DisputeWorkflowState.CREDIT_RECEIVED)
                .target(DisputeWorkflowState.HOLD)
                .event(DisputeWorkflowEvent.HOLD)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.CREDIT_RECEIVED)
                .target(DisputeWorkflowState.END)
                .event(DisputeWorkflowEvent.END_WORKFLOW)
                .action(updateDisputeStateAction)
                .and()

                .withInternal()
                .source(DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED)
                .event(DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK)
                .action(recoverChargebackAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED)
                .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT)
                .action(raiseAccountingEventAction)
                .and()

                .withInternal()
                .source(DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED)
                .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_HOLD)
                .action(holdReversalAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED)
                .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_HOLD_EVENT)
                .action(raiseHoldRecoveryReversalAccountingEventAction)
                .and()


                .withInternal()
                .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_HOLD)
                .action(holdReversalAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_HOLD_EVENT)
                .action(raiseHoldRecoveryReversalAccountingEventAction)
                .and()

                .withInternal()
                .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .event(DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK)
                .action(recoverChargebackAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT)
                .action(raiseAccountingEventAction)
                .and()
        ;
    }
}
