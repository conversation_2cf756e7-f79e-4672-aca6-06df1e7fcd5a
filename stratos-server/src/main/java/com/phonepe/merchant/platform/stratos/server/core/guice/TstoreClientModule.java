package com.phonepe.merchant.platform.stratos.server.core.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.configs.TstoreClientConfig;
import com.phonepe.platform.scroll.model.v2.CreatedSchemaParams;
import com.phonepe.tstore.client.TstoreClient;
import com.phonepe.tstore.client.bundle.TstoreClientBundle;
import java.util.Map;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class TstoreClientModule extends AbstractModule {

    private final TstoreClientBundle<StratosConfiguration> tstoreClientBundle;

    @Provides
    public TstoreClient provideTstoreClient() {
        return tstoreClientBundle.getTstoreClient();
    }

    @Provides
    public Map<Class<?>, CreatedSchemaParams> provideSchemaParamMapping() {
        return tstoreClientBundle.getSchemaParamsMapping();
    }

    @Provides
    public TstoreClientConfig provideTstoreClientConfig(final StratosConfiguration config) {
        return config.getTstoreClientConfig();
    }
}
