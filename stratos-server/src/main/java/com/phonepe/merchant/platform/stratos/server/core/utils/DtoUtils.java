package com.phonepe.merchant.platform.stratos.server.core.utils;


import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.EvidenceStatusDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeCategoryDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeIssuerDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileStateDto;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.RowStateDto;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirDisputeTypeDto;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeIssuer;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.FileState;
import com.phonepe.merchant.platform.stratos.server.core.models.FileType;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowType;
import javax.validation.constraints.NotNull;

import com.phonepe.stratos.kaizen.models.data.ActionStatus;
import lombok.experimental.UtilityClass;

@UtilityClass
public class DtoUtils {

    public FileType fileDtoToType(@NotNull final FileTypeDto fileType) {
        return FileType.valueOf(fileType.name());
    }

    public DisputeType disputeDtoToType(@NotNull final DisputeTypeDto disputeType) {
        return DisputeType.valueOf(disputeType.name());
    }

    public DisputeIssuer disputeIssuerDtoToIssuer(
        @NotNull final DisputeIssuerDto disputeIssuerDto) {
        return DisputeIssuer.valueOf(disputeIssuerDto.name());
    }


    public com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory disputeCategoryDtoToCategory(
        @NotNull final DisputeCategoryDto disputeCategoryDto) {
        return com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory.valueOf(disputeCategoryDto.name());
    }

    public FileTypeDto fileTypeToDto(@NotNull final FileType fileType) {
        return FileTypeDto.valueOf(fileType.name());
    }

    public DisputeTypeDto disputeTypeToDto(@NotNull final DisputeType disputeType) {
        return DisputeTypeDto.valueOf(disputeType.name());
    }

    public FileStateDto fileStateToDto(@NotNull final FileState fileState) {
        return FileStateDto.valueOf(fileState.name());
    }

    public RowStateDto rowStateToDto(@NotNull final RowState rowState) {
        return RowStateDto.valueOf(rowState.name());
    }

    public RowType rowDtoToType(@NotNull final RowTypeDto rowTypeDto) {
        return RowType.valueOf(rowTypeDto.name());
    }

    public DisputeStage disputeStageDtoToDisputeStage(@NotNull final DisputeStageDto disputeStageDto) {
        return DisputeStage.valueOf(disputeStageDto.name());
    }

    public DisputeStage disputeDataStageToDisputeStage(@NotNull final com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage disputeStage) {
        return DisputeStage.valueOf(disputeStage.name());
    }

    public DisputeStageDto disputeStageToStageDto(@NotNull final DisputeStage disputeStage) {
        return DisputeStageDto.valueOf(disputeStage.name());
    }


    public DisputeType udirDisputeDtoToType(@NotNull final UdirDisputeTypeDto disputeType) {
        return DisputeType.valueOf(disputeType.name());
    }

    public EvidenceStatusDto evidenceStatusToDto(@NotNull final ActionStatus status) {
        return EvidenceStatusDto.valueOf(status.name());
    }

    public com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategoryToCategoryModel(
            @NotNull final com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory disputeCategory){
        return com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory.valueOf(
                disputeCategory.name().split("_")[0]);
    }
    public com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage disputeStagetoStageModel(
            @NotNull DisputeStage stage) {
        return com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.valueOf(stage.name());
    }

    public com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType disputeTypeToTypeModel(
            @NotNull DisputeType type) {
        String disputeType = type.name();
        String typeModelValue = disputeType.substring(disputeType.lastIndexOf("_")+1);
        return com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType
                .valueOf(typeModelValue);
    }

    public com.phonepe.merchant.platform.stratos.models.commons.TransactionType disputeTypeToTransType(@NotNull DisputeType type) {
        String disputeType = type.name();
        String instrument = disputeType.substring(0,disputeType.lastIndexOf("_"));
        return com.phonepe.merchant.platform.stratos.models.commons.TransactionType.valueOf(instrument);
    }

    public DisputeType transactionTypeToDisputeType(
        @NotNull final TransactionType transactionType,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType disputeType) {
        return DisputeType.valueOf(StringUtils.join(String.valueOf(transactionType),
            String.valueOf(disputeType)));
    }

    public DisputeCategoryDto requestDisputeCategoryToDisputeCategoryDto(
        @NotNull final
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType disputeType) {
        return DisputeCategoryDto.valueOf(StringUtils.join(String.valueOf(disputeCategory),
            String.valueOf(disputeType)));
    }
}

