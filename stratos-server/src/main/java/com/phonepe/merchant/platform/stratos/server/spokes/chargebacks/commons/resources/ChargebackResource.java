package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.NpciChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.row.requests.PgMisRowOffsetRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services.ChargebackService;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Set;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v1/chargeback")
@Tag(name = "Chargeback Related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class ChargebackResource {

    private final ChargebackService chargebackService;
    private final RowService rowService;

    @GET
    @Path("/states")
    @ExceptionMetered
    @RolesAllowed("chargeback/summary")
    @Operation(summary = "Fetch All States of Chargeback Workflow")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Set<DisputeWorkflowState> getAllStates() {

        return chargebackService.getAllStates();
    }

    @POST
    @Path("/filter")
    @ExceptionMetered
    @RolesAllowed("chargeback/summary")
    @Operation(summary = "Fetch Chargeback Summary based on Filter Parameters")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public List<ChargebackSummary> getChargebackSummary(
        @Valid final DisputeFilter filter) {

        return chargebackService.filter(filter);
    }

    @POST
    @Path("/npci/chargeback/filter")
    @ExceptionMetered
    @RolesAllowed("npci-chargeback/summary")
    @Operation(summary = "Fetch Chargeback Summary based on Filter Parameters")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public List<NpciChargebackSummary> getNpciChargebackSummary(
        @Valid final DisputeFilter filter) {
        return chargebackService.npciChargebackFilter(filter);
    }

    @PUT
    @ExceptionMetered
    @Path("/ttl-breach")
    @RolesAllowed("chargeback/ttl-breach")
    @Operation(summary = "Update state of chargebacks which are breaching TTL on the same day as of API invocation")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void updateChargebackStateBreachingTtl(
        @Valid @NotNull final DateRange dateRange) {

        chargebackService.updateChargebackStateBreachingTtl(dateRange);
    }


    @POST
    @ExceptionMetered
    @Path("/mis/offset")
    @RolesAllowed("chargeback/row_offset")
    @Operation(summary = "Offset Pg Mis Rows")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public void offsetPgMisRow(@Valid PgMisRowOffsetRequest pgMisOffsetRequest) {
        rowService.offsetPgMisRow(pgMisOffsetRequest);
    }
}
