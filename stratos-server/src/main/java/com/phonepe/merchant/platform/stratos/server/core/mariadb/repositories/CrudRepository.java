package com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Sharded;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import io.appform.dropwizard.sharding.dao.LookupDao;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import lombok.RequiredArgsConstructor;
import org.hibernate.criterion.DetachedCriteria;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.UnaryOperator;

@RequiredArgsConstructor
public abstract class CrudRepository<T extends Sharded> {

    protected final RelationalDao<T> relationalDao;

    protected final LookupDao<T> lookupDao;

    public Optional<T> get(final String shardingKey) {
        try {
            return lookupDao.get(shardingKey);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, e);
        }
    }

    public List<T> select(final String shardingKey, final DetachedCriteria detachedCriteria) {
        return select(shardingKey, detachedCriteria, 0, 100_000);
    }

    public List<T> select(final String shardingKey, final DetachedCriteria detachedCriteria,
        final int start, final int numRows) {
        return select(shardingKey, detachedCriteria, start, numRows, Function.identity());
    }

    public <U> U select(final String shardingKey, final DetachedCriteria detachedCriteria,
        final int start, final int numRows, final Function<List<T>, U> handler) {
        try {
            return relationalDao.select(shardingKey, detachedCriteria, start, numRows, handler);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, e);
        }
    }

    public Optional<T> save(final T object) {
        return save(object, Function.identity());
    }

    public <U> Optional<U> save(final T object, final Function<T, U> handler) {
        if (object == null) {
            return Optional.empty();
        }
        Objects.requireNonNull(handler);
        try {
            return Optional.ofNullable(
                relationalDao.save(object.getShardingKey(), object, handler));
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, e);
        }
    }

    public boolean update(final String shardingKey, Function<Optional<T>, T> updater) {
        try {
            return lookupDao.update(shardingKey, updater);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, e);
        }
    }

    public boolean update(final String shardingKey, final DetachedCriteria detachedCriteria,
        final UnaryOperator<T> updater) {
        try {
            return relationalDao.update(shardingKey, detachedCriteria, updater);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, e);
        }
    }

    public List<T> scatterGather(final DetachedCriteria detachedCriteria) {
        return scatterGather(detachedCriteria, 0, 100_000);
    }

    public List<T> scatterGather(final DetachedCriteria detachedCriteria,
        final int start, final int numRows) {
        try {
            return relationalDao.scatterGather(detachedCriteria, start, numRows);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, e);
        }
    }
}
