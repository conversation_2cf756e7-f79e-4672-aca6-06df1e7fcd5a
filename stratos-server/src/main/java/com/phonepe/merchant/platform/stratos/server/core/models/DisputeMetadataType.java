package com.phonepe.merchant.platform.stratos.server.core.models;

import lombok.experimental.UtilityClass;

/**
 * This entity is persisted in DB by it's Ordinal Value Hence only append at the end and do not
 * change Order of existing values while adding new values
 */
public enum DisputeMetadataType {
    COMMENT {
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitComment();
        }
    },
    INSTITUTIONAL_CREDIT_DISPUTE_METADATA {
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitInstitututionalCredit();
        }
    },
    ACCOUNTING_EVENT {
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitAccountingEvent();
        }
    },
    INSTITUTIONAL_DEBIT_DISPUTE_METADATA {
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitInstitututionalDebit();
        }
    },
    UDIR_OUTGOING_COMPLAINT {
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitUdirOutgoingComplaint();
        }
    },
    TOA_DISPUTE_METADATA {
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitToaDisputeMetadata();
        }
    },

    FRA_ACTION_METADATA{
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitFraAction();
        }
    },

    NETBANKING_DISPUTE_METADATA {
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitNetBankingDisputeMetadata();
        }
    },
    WALLET_DISPUTE_METADATA {
        @Override
        public <T> T accept(DisputeMetadataTypeVisitor<T> visitor) {
            return visitor.visitWalletDisputeMetadata();
        }
    }
    ;

    public abstract <T> T accept(DisputeMetadataTypeVisitor<T> visitor);


    @UtilityClass
    public static final class Ordinals {

        public static final String COMMENT = "0";
        public static final String INSTITUTIONAL_CREDIT_DISPUTE_METADATA = "1";
        public static final String ACCOUNTING_EVENT = "2";
        public static final String INSTITUTIONAL_DEBIT_DISPUTE_METADATA = "3";
        public static final String UDIR_OUTGOING_COMPLAINT = "4";
        public static final String TOA_DISPUTE_METADATA = "5";
        public static final String FRA_ACTION_METADATA = "6";
        public static final String NB_DISPUTE_METADA = "7";
        public static final String WALLET_DISPUTE_METADATA = "8";
    }

    public interface DisputeMetadataTypeVisitor<T> {
        default T visitComment(){return null;}

        default T visitInstitututionalCredit(){return null;}

        default T visitAccountingEvent(){return null;}

        default T visitInstitututionalDebit(){return null;}

        default T visitUdirOutgoingComplaint(){return null;}

        default T visitToaDisputeMetadata(){return null;}
        default T visitFraAction(){return null;}
        default T visitNetBankingDisputeMetadata() {return null;}
        default T visitWalletDisputeMetadata() {return null;}
    }
}
