package com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners;

import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateContext.Stage;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;

import java.util.Map;

@Singleton
public class EventNotAcceptedListener<S, E>
    extends StateMachineListenerAdapter<S, E> implements StateMachineListener<S, E> {

    @Override
    public void stateContext(final StateContext<S, E> stateContext) {
        if (Stage.EVENT_NOT_ACCEPTED == stateContext.getStage()
            && !stateContext.getStateMachine().hasStateMachineError()) {
            stateContext.getStateMachine().setStateMachineError(
                    DisputeExceptionUtil.error(StratosErrorCodeKey.TRANSITION_NOT_ALLOWED, Map.of()));
        }
    }
}
