package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.PartialAcceptanceTransitionContext;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
public class WalletMerchantAcceptedChargebackAction extends UpdateDisputeStateBaseAction {

    private final Provider<StateChangeHandlerActor> stateChangeHandlerProvider;

    @Inject
    public WalletMerchantAcceptedChargebackAction(
        DisputeService disputeService,
        DisputeWorkflowRepository disputeWorkflowRepository,
        EventIngester eventIngester,
        Provider<StateChangeHandlerActor> stateChangeHandlerProvider,
        CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.stateChangeHandlerProvider = stateChangeHandlerProvider;
    }

    private long getAcceptedAmountFromSignal(
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var context = stateContext.getExtendedState()
            .get(TransitionContext.class,
                PartialAcceptanceTransitionContext.class);
        Objects.requireNonNull(context);

        return context.getAcceptedAmount();
    }

    private void validateAcceptedAmount(long acceptedAmount,
        FinancialDisputeWorkflow financialDisputeWorkflow) {

            if(acceptedAmount <= 0 || acceptedAmount > financialDisputeWorkflow.getDisputedAmount()) {
                log.error("Accepted amount validation fail for acceptedAmount "
                    + "is : {} and disputed amount is: {} and dispute workflow : {}", acceptedAmount,financialDisputeWorkflow.getDisputedAmount(), financialDisputeWorkflow);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ACCEPTED_AMOUNT, Map.of(
                Constants.MESSAGE, StratosErrorCodeKey.INVALID_ACCEPTED_AMOUNT.getKey(),
                Fields.disputedAmount, financialDisputeWorkflow.getDisputedAmount(),
                FinancialDisputeWorkflow.Fields.acceptedAmount,
                financialDisputeWorkflow.getAcceptedAmount()));
        }
    }

    private DisputeWorkflowEvent nextDisputeWorkflowEvent(
        FinancialDisputeWorkflow financialDisputeWorkflow) {
        return (financialDisputeWorkflow.getAcceptedAmount()
            == financialDisputeWorkflow.getDisputedAmount()) ?
            DisputeWorkflowEvent.FULLY_ACCEPT_CHARGEBACK
            : DisputeWorkflowEvent.PARTIAL_ACCEPT_CHARGEBACK;
    }

    @Override
    @SneakyThrows
    protected void transition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.
            getFinancialDisputeWorkflow(disputeWorkflow);
        final var acceptedAmount = getAcceptedAmountFromSignal(stateContext);
       validateAcceptedAmount(acceptedAmount, financialDisputeWorkflow);

        financialDisputeWorkflow.setAcceptedAmount(acceptedAmount);
        log.info("Transition to Merchant accepted chargeback for wallet and financialDisputeWorkflow : {}",
            financialDisputeWorkflow);

    }

    @Override
    @SneakyThrows
    protected void postTransition(final DisputeWorkflow disputeWorkflow) {

        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.
            getFinancialDisputeWorkflow(disputeWorkflow);

        DisputeWorkflowEvent disputeWorkflowEvent = nextDisputeWorkflowEvent(
            financialDisputeWorkflow);

        final var disputeWorkflowMessage = DisputeWorkflowMessage.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowEvent(disputeWorkflowEvent)
            .build();

        log.info("DisputeWorkflowMessage : {} for publishing in StateChangeHandlerActor for "
            + "financialDisputeWorkflow : {}", disputeWorkflowMessage, financialDisputeWorkflow);
        stateChangeHandlerProvider.get().publish(disputeWorkflowMessage);

    }
}
