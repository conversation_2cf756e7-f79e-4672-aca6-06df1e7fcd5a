package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeWorkflowKey;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.UpdateCommunicationIdRequest;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.KaizenService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.stratos.kaizen.models.data.ActionStatus;
import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import com.phonepe.verified.kaizen.models.data.*;
import com.phonepe.verified.kaizen.models.data.common.CompletionState;
import com.phonepe.verified.kaizen.models.data.common.UpdaterType;
import com.phonepe.verified.kaizen.models.events.TransitionEvent;
import com.phonepe.verified.kaizen.models.states.TransitionState;
import com.phonepe.verified.kaizen.services.ActionMetadataService;
import com.phonepe.verified.kaizen.services.ActionService;
import com.phonepe.verified.kaizen.services.WorkflowStepService;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredAction;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflow;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredWorkflowStep;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionMetadataRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.ActionRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowRepository;
import com.phonepe.verified.kaizen.storage.mariadb.repositories.WorkflowStepRepository;
import com.phonepe.verified.kaizen.utils.IdUtils;
import com.phonepe.zeus.models.Farm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.phonepe.verified.kaizen.utils.BuildUtils.primaryKey;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class KaizenServiceImpl implements KaizenService {

    private final DisputeService disputeService;

    private final WorkflowStepService workflowStepService;

    private final ActionService actionService;

    private final ActionMetadataService actionMetadataService;

    private final WorkflowStepRepository workflowStepRepository;

    private final ActionRepository actionRepository;

    private final WorkflowRepository workflowRepository;

    private final ActionMetadataRepository actionMetadataRepository;

    public Optional<StoredActionMetadata> uploadEvidence(String disputeWorkflowId, String documentId, Map<String, Object> metadata) {        String workflowId = getOrCreateWorkflowId(disputeWorkflowId);
        String workflowStepId = getOrCreateWorkflowStepId(workflowId);
        StoredAction action = createAction(workflowStepId);
        return createActionDetail(action.getActionId(), documentId, DocumentType.BANK_ACCOUNT, "", metadata);
    }

    public List<StoredDocumentUploadWithMetaDataActionMetadata> getAllEvidences(String kaizenWorkflowId) {
        try {
            var workflowStepIds = workflowStepService.getWorkflowStepsFromWorkflowId(kaizenWorkflowId).stream()
                    .map(StoredWorkflowStep::getWorkflowStepId).collect(Collectors.toSet());
            var actionIds = actionService.getActions(workflowStepIds).stream().map(StoredAction::getActionId)
                    .collect(Collectors.toSet());
            return actionMetadataService.getActionMetadataList(actionIds).stream()
                    .filter(e -> e.getActionMetadataType() == ActionMetadataType.DOCUMENT_UPLOAD_WITH_METADATA)
                    .map(StoredDocumentUploadWithMetaDataActionMetadata.class::cast).toList();
        }
        catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    public List<StoredDocumentUploadWithMetaDataActionMetadata> getAllEvidences(String kaizenWorkflowId, ActionStatus actionStatus) {
        return getAllEvidences(kaizenWorkflowId).stream().filter(e -> e.getStatus() == actionStatus).toList();
    }

    public Optional<StoredActionMetadata> deleteEvidence(String actionId) {
        try {
            Optional<StoredDocumentUploadWithMetaDataActionMetadata> actionDetail = actionMetadataService
                    .getActionMetadataList(actionId)
                    .stream().filter(e -> e.getActionMetadataType() == ActionMetadataType.DOCUMENT_UPLOAD_WITH_METADATA)
                    .map(StoredDocumentUploadWithMetaDataActionMetadata.class::cast).findFirst();
            if (actionDetail.isEmpty()) {
                log.error("Kaizen action detail is not present");
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.KAIZEN_ACTION_DETAIL_NOT_FOUND);
            }
            StoredDocumentUploadWithMetaDataActionMetadata storedActionDetail = actionDetail.get();
            storedActionDetail.setStatus(ActionStatus.INACTIVE);
            return actionMetadataRepository.save(storedActionDetail);
        }
        catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    private StoredWorkflowStep buildWorkflowStep(String workflowId) {
        return StoredWorkflowStep.builder()
                .primaryKey(primaryKey())
                .workflowId(workflowId)
                .workflowStepId(IdUtils.createIdInSameShard("WS", workflowId, workflowStepRepository::getShardId))
                .profileStepId(Constants.STRATOS_EVIDENCE_UPLOAD)
                .currentState(TransitionState.CREATED)
                .currentEvent(TransitionEvent.CREATE_ENTRY)
                .lastUpdatedBy(Constants.STRATOS_SYSTEM_USER)
                .lastUpdaterType(UpdaterType.SYSTEM)
                .build();
    }

    private StoredAction buildAction(String workflowStepId) {
        return StoredAction.builder()
                .primaryKey(primaryKey())
                .workflowStepId(workflowStepId)
                .actionId(IdUtils.createIdInSameShard("A", workflowStepId, actionRepository::getShardId))
                .actionType(ActionType.DOCUMENT_UPLOAD)
                .stateMachineVersion("0")
                .actionMappingId("0")
                .screenMappingId("DISPUTE_PROOF_UPLOAD")
                .currentState("")
                .currentEvent("")
                .completed(true)
                .completionState(CompletionState.SUCCESS)
                .lastUpdatedBy(Constants.STRATOS_SYSTEM_USER)
                .lastUpdaterType(UpdaterType.SYSTEM)
                .build();
    }

    private StoredWorkflow buildWorkflow(String merchantId) {
        return StoredWorkflow.builder()
                .primaryKey(primaryKey())
                .workflowId(IdUtils.createIdInSameShard("W", merchantId, workflowRepository::getShardId))
                .entityId(merchantId)
                .phoneNumber("9100012345")
                .emailId("")
                .entityType(EntityType.MERCHANT)
                .profileId("")
                .callerFarmId(Farm.NB6)
                .currentState(TransitionState.CREATED)
                .currentEvent(TransitionEvent.CREATE_ENTRY)
                .tag("")
                .lastUpdatedBy(Constants.STRATOS_SYSTEM_USER)
                .lastUpdaterType(UpdaterType.SYSTEM)
                .build();
    }

    private StoredDocumentUploadWithMetaDataActionMetadata buildActionDetail(String actionId, String documentId,
                                                                 DocumentType documentType, String documentLabel,
                                                                 Map<String, Object> metadata) {
        return StoredDocumentUploadWithMetaDataActionMetadata.builder()
                .actionId(actionId)
                .documentId(documentId)
                .documentType(documentType)
                .documentLabel(documentLabel)
                .status(ActionStatus.ACTIVE)
                .metadata(metadata==null?null:MapperUtils.serializeToBytes(metadata))
                .build();
    }

    private String getOrCreateWorkflowId(String disputeWorkflowId) {
        DisputeWorkflow disputeWorkflow = disputeService.getDisputeWorkflow(disputeWorkflowId);
        if (StringUtils.isEmpty(disputeWorkflow.getCommunicationId())) {
            StoredWorkflow workflow = buildWorkflow(disputeWorkflow.getDispute().getMerchantId());
            workflowRepository.save(workflow);
            updateCommunicationId(disputeWorkflowId, disputeWorkflow.getTransactionReferenceId(),
                    workflow.getWorkflowId());
            return workflow.getWorkflowId();
        }
        else {
            return disputeWorkflow.getCommunicationId();
        }
    }

    private String getOrCreateWorkflowStepId(String workflowId) {
        Optional<StoredWorkflowStep> workflowStep = workflowStepService.getWorkflowSteps(workflowId,
                Constants.STRATOS_EVIDENCE_UPLOAD).stream().findFirst();
        if (workflowStep.isEmpty()) {
            StoredWorkflowStep  newWorkflowStep= buildWorkflowStep(workflowId);
            workflowStepRepository.save(newWorkflowStep);
            return newWorkflowStep.getWorkflowStepId();
        }
        else {
            return workflowStep.get().getWorkflowStepId();
        }
    }

    private StoredAction createAction(String workflowStepId) {
        StoredAction action = buildAction(workflowStepId);
        actionRepository.save(action);
        return action;
    }

    private Optional<StoredActionMetadata> createActionDetail(String actionId, String documentId,
                                                              DocumentType documentType, String documentLabel, Map<String, Object> metadata) {
        StoredDocumentUploadWithMetaDataActionMetadata actionDetail = buildActionDetail(actionId, documentId, documentType,
                documentLabel, metadata);
        return actionMetadataRepository.save(actionDetail);
    }

    private void updateCommunicationId(String disputeWorkflowId, String transactionReferenceId, String kaizenWorkflowId) {
        var olympusUserDetails = Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails();
        disputeService.updateCommunicationIdInDisputeWorkflow(UpdateCommunicationIdRequest.builder()
                        .communicationId(kaizenWorkflowId)
                        .disputeWorkflowKeys(List.of(DisputeWorkflowKey.builder()
                                .disputeWorkflowId(disputeWorkflowId)
                                .transactionReferenceId(transactionReferenceId)
                                .build()))
                        .build(),
                olympusUserDetails.getUserId(),
                UserType.SYSTEM);
    }
}