package com.phonepe.merchant.platform.stratos.server.core.models;

/**
 * This entity is persisted in DB by it's Ordinal Value Hence only append at the end and do not
 * change Order of existing values while adding new values
 */
public enum DisputeStage {
    FIRST_LEVEL{
        @Override
        public <T> T accept(final DisputeStageVisitor<T> visitor){
            return visitor.visitFirstLevel();
        }

        @Override
        public DisputeStage getPreviousStage() {
            return null;
        }

        @Override
        public DisputeStage getNextStage() {
            return DisputeStage.PRE_ARBITRATION;
        }

    },
    PRE_ARBITRATION {
        @Override
        public <T> T accept(final DisputeStageVisitor<T> visitor){
            return visitor.visitPreArbitration();
        }

        @Override
        public DisputeStage getPreviousStage() {
            return DisputeStage.FIRST_LEVEL;
        }

        @Override
        public DisputeStage getNextStage() {
            return null;
        }

    };

    public abstract <T> T accept(DisputeStageVisitor<T> visitor);
    public abstract DisputeStage getPreviousStage();
    public abstract DisputeStage getNextStage();
}