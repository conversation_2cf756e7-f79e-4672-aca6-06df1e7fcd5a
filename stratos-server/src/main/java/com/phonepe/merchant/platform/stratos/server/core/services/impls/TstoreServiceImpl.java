package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeFeed;
import com.phonepe.merchant.platform.stratos.models.feeds.EntityType;
import com.phonepe.merchant.platform.stratos.models.feeds.MetaType;
import com.phonepe.merchant.platform.stratos.server.core.configs.TstoreClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.services.TstoreService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.platform.schema.models.SchemaParams;
import com.phonepe.platform.scroll.model.State;
import com.phonepe.platform.scroll.model.v2.BaseEntity;
import com.phonepe.platform.scroll.model.v2.CreatedSchemaParams;
import com.phonepe.platform.scroll.model.v2.Notification;
import com.phonepe.platform.scroll.model.v2.NotificationEntity;
import com.phonepe.tstore.client.TstoreClient;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TstoreServiceImpl implements TstoreService {

    private final TstoreClient tstoreClient;

    private final TstoreClientConfig tstoreClientConfig;

    private final Map<Class<?>, CreatedSchemaParams> schemaParamsMapping;

    @Override
    @MonitoredFunction
    public void createFeed(final String entityId, final DisputeFeed disputeFeed) {

        final var baseEntity = toBaseEntity(entityId, disputeFeed);
        final var notification = toNotification(entityId, disputeFeed);
        final var notificationEntity = getNotificationEntity(baseEntity, notification);

        final var published = tstoreClient.putEntityAndNotify(notificationEntity,
            notificationEntity.getNotification().getUnitId(), notificationEntity.getNotification().getEntityId());

        if (!published) {
            final var error = DisputeExceptionUtil.error(StratosErrorCodeKey.FEED_PUBLISH_ERROR, Map.of());
            log.error("Failed to publish feed entity: {}", disputeFeed, error);
            throw error;
        }
    }

    private SchemaParams getSchemaParams() {
        if (Objects.isNull(schemaParamsMapping.get(DisputeFeed.class))) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.FEED_SCHEMA_PARAMS_NOT_FOUND, Map.of());
        }
        return schemaParamsMapping.get(DisputeFeed.class).getEntitySchemaParams();
    }

    private BaseEntity toBaseEntity(final String entityId, final DisputeFeed disputeFeed) {
        return BaseEntity.builder()
            .entityId(entityId)
            .type(EntityType.DISPUTE.name())
            .metaId(disputeFeed.getMerchantId())
            .metaType(MetaType.MERCHANT.name())
            .data(disputeFeed)
            .section(Constants.TRANSACTION)
            .state(State.COMPLETED)
            .createdAt(System.currentTimeMillis())
            .updatedAt(System.currentTimeMillis())
            .build();
    }

    private Notification toNotification(
        final String entityId,
        final DisputeFeed disputeFeed) {
        return Notification.builder()
            .notificationReceivers(Map.of())
            .unitId(disputeFeed.getGlobalPaymentId())
            .entityId(entityId)
            .namespaceId(tstoreClientConfig.getNamespace())
            .type(EntityType.DISPUTE.name())
            .metaId(disputeFeed.getMerchantId())
            .notificationChannels(List.of())
            .createdAt(new Date())
            .build();
    }

    private NotificationEntity getNotificationEntity(final BaseEntity baseEntity,
        final Notification notification) {
        return NotificationEntity.builder()
            .notification(notification)
            .baseEntity(baseEntity)
            .schemaParams(getSchemaParams())
            .createdAt(new Date())
            .build();
    }
}
