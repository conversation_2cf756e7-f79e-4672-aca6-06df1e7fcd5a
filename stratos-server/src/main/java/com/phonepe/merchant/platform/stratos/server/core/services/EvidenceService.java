package com.phonepe.merchant.platform.stratos.server.core.services;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceIdentifierParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceUploadPayload;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceDetail;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceResponse;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;

import java.io.InputStream;

public interface EvidenceService {
    EvidenceDetail upload(EvidenceUploadPayload payload, InputStream inputStream, FormDataContentDisposition fileMetaData);
    EvidenceResponse listEvidences(EvidenceFilterParams filterParams);

    EvidenceDetail delete(EvidenceIdentifierParams evidenceIdentifierParams);
}
