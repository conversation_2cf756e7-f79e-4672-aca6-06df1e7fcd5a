package com.phonepe.merchant.platform.stratos.server.v2.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Slf4j
@Singleton
public class InitiateMerchantCommunicationAction extends UpdateDisputeStateBaseAction {
    private final Provider<StateChangeHandlerActor> stateChangeHandlerActorProvider;

    @Inject
    @SuppressWarnings("java:S107")
    public InitiateMerchantCommunicationAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final EventIngester eventIngester,
            final CallbackActor callbackActor,
            Provider<StateChangeHandlerActor> stateChangeHandlerActorProvider) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.stateChangeHandlerActorProvider = stateChangeHandlerActorProvider;
    }

    @Override
    @SneakyThrows
    protected void postTransition(final DisputeWorkflow dw) {
        final DisputeWorkflowMessage.DisputeWorkflowMessageBuilder message =
                DisputeWorkflowMessage.builder()
                        .disputeWorkflowId(dw.getDisputeWorkflowId())
                        .transactionReferenceId(dw.getTransactionReferenceId());

        if(dw.getRespondBy().isBefore(LocalDateTime.now().toLocalDate().atStartOfDay())){
            message.disputeWorkflowEvent(
                    DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL);
        }
        else {
            message.disputeWorkflowEvent(
                    DisputeWorkflowEvent.REQUEST_MERCHANT_ACTION);
        }
        stateChangeHandlerActorProvider.get().publish(message.build());

    }
}
