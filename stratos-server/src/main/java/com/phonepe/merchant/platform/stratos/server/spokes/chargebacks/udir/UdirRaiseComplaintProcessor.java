package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir;


import static com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirOutgoingRaiseComplaintResponse;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirRaiseComplaintResponse;
import com.phonepe.merchant.platform.stratos.server.core.configs.DisputeWorkflowStateConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.NonFinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.models.common.enums.PaymentState;
import com.phonepe.models.payments.common.PaymentFlags.OptionFlag;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.destination.DestinationType;
import com.phonepe.models.payments.pay.instrument.AccountPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentType;
import com.phonepe.models.payments.upi.udircomplaint.UdirOutgoingComplaintRequest;
import com.phonepe.payments.upiclientmodel.enums.ComplaintRequestType;
import com.phonepe.payments.upiclientmodel.enums.ComplaintRequestType.ComplaintRequestVisitor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Map;
import java.util.Set;


@Singleton
@Slf4j
public class UdirRaiseComplaintProcessor {

    private final DisputeService disputeService;
    private final PaymentsService paymentsService;
    private final Map<DisputeStage, Set<DisputeWorkflowState>> retryableStates;
    private final IdHelper idHelper;

    private final EventIngester eventIngester;

    @Inject
    public UdirRaiseComplaintProcessor(
        final DisputeService disputeService,
        final PaymentsService paymentsService,
        final DisputeWorkflowStateConfig disputeWorkflowStateConfig,
        final IdHelper idHelper, final EventIngester eventIngester) {
        this.disputeService = disputeService;
        this.paymentsService = paymentsService;
        this.retryableStates = disputeWorkflowStateConfig.getRetryableStates()
            .get(DisputeType.UDIR_OUTGOING_COMPLAINT);
        this.idHelper = idHelper;
        this.eventIngester = eventIngester;
    }


    public UdirRaiseComplaintResponse process(final String paymentTransactionId,
        final ComplaintRequestType requestCode,
        final long complaintAmount, final DisputeStage disputeStage, final String complaintId) {
        var transactionType = DtoUtils.disputeTypeToTransType(DisputeType.UDIR_OUTGOING_COMPLAINT);
        var disputeType = DtoUtils.disputeTypeToTypeModel(DisputeType.UDIR_OUTGOING_COMPLAINT);

        disputeService.acquireDisputeCreationLock(paymentTransactionId,
            transactionType, disputeType,
            FIRST_LEVEL);

        try {
            final var transactionDetail = paymentsService.transactionDetailFromOriginalTransactionId(
                    paymentTransactionId);

            ValidationUtils.validateAmount(transactionDetail.getSentPayment(), paymentTransactionId, complaintAmount,
                    DisputeType.UDIR_OUTGOING_COMPLAINT);

            checkPreviousComplaints(paymentTransactionId, disputeStage);

            final var disputeCatagory = validateComplaintRequest(requestCode, transactionDetail);

            final var dispute = buildDispute(complaintAmount, disputeStage, disputeCatagory, paymentTransactionId,
                    complaintId);
            final var disputeWorkflow = buildDisputeWorkflow(complaintAmount, disputeStage, paymentTransactionId,
                    dispute.getDisputeId());

            disputeService.createEntry(dispute, disputeWorkflow);

            UdirOutgoingComplaintRequest udirOutgoingComplaintRequest = UdirOutgoingComplaintRequest.builder()
                    .requestDate(Date.from(disputeWorkflow.getRaisedAt()
                            .toInstant(ZoneOffset.UTC)))
                    .complaintId(disputeWorkflow.getDisputeWorkflowId())
                    .transactionId(paymentTransactionId)
                    .requestType(requestCode)
                    .adjAmount(complaintAmount)
                    .build();

            eventIngester.generateEvent(
                    FoxtrotEventUtils.toUdirOutgoingComplaintRequestEvent(udirOutgoingComplaintRequest));

            try {
                paymentsService.raiseUdirComplaint(udirOutgoingComplaintRequest);

            } catch (final Exception e) {
                if (!(e instanceof DisputeException  //NOSONAR
                        && ((DisputeException) e).getErrorCode() == StratosErrorCodeKey.HYSTRIX_TIMEOUT)) {

                    disputeService.triggerEvent(Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                            DisputeWorkflowEvent.PAYMENT_CALL_FAILED, Constants.EMPTY_TRANSITION_CONTEXT);
                    throw e;
                } else {
                    log.info("Payment Raise Complaint Call timeout :{}", paymentTransactionId);
                }
            }

            return UdirOutgoingRaiseComplaintResponse.builder()
                    .disputeStage(DtoUtils.disputeStageToStageDto(disputeStage))
                    .transactionId(disputeWorkflow.getTransactionReferenceId())
                    .complaintId(complaintId)
                    .stratosComplaintId(disputeWorkflow.getDisputeWorkflowId())
                    .build();
        } finally {
            disputeService.releaseDisputeCreationLock(
                    paymentTransactionId, transactionType, disputeType, FIRST_LEVEL);

        }
    }


    private void checkPreviousComplaints(final String transactionReferenceId,
        final DisputeStage disputeStage) {

        final Set<DisputeWorkflowState> complaintEndingStates = retryableStates.get(disputeStage);

        disputeService.getAllDisputeWorkflows(
                transactionReferenceId,
                DisputeType.UDIR_OUTGOING_COMPLAINT, disputeStage)
            .stream()
            .filter(disputeWorkflow -> !complaintEndingStates.contains(
                disputeWorkflow.getCurrentState()))
            .findFirst().ifPresent(disputeWorkflow -> {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.PREVIOUS_DISPUTE_WORKFLOW_NOT_ENDED,
                    Map.of(
                        Constants.MESSAGE,
                        "Previous Complaint not ended yet",
                        Constants.TRANSACTION_ID,
                        transactionReferenceId)
                );
            });
    }

    private DisputeCategory validateComplaintRequest(
        final ComplaintRequestType complaintRequestType,
        final TransactionDetail transactionDetail) {
        final var sentPayment = transactionDetail.getSentPayment();

        return complaintRequestType.accept(new ComplaintRequestVisitor<>() {
            @Override
            public DisputeCategory visitComplaintReqP2PDRC() {
                return validateDrcAndGetCategory(sentPayment);
            }

            @Override
            public DisputeCategory visitComplaintReqNoFulfillmentApprovedTxn() {
                return validateP2MExternalAndGetCategory(sentPayment);
            }

            @Override
            public DisputeCategory visitComplaintReqP2MDRC() {
                return validateDrcAndGetCategory(sentPayment);
            }

            @Override
            public DisputeCategory visitComplaintReqP2PDEEMED() {
                return validateDeemedAndGetCategory(sentPayment);
            }

            @Override
            public DisputeCategory visitComplaintReqNoFulfillmentCreditPending() {
                return validateP2MExternalAndGetCategory(sentPayment);
            }

            @Override
            public DisputeCategory visitComplaintReqMerchantDeemed() {
                return validateDeemedAndGetCategory(sentPayment);
            }

            @Override
            public DisputeCategory visitComplaintReqMerchantConfirmationPending() {
                return validateP2MExternalAndGetCategory(sentPayment);
            }

            @Override
            public DisputeCategory visitComplaintReqFulfillmentIssuePaidAgain() {
                return validateP2MExternalAndGetCategory(sentPayment);
            }
        });
    }


    private DisputeWorkflow buildDisputeWorkflow(
        final long complaintAmount, final DisputeStage disputeStage,
        final String paymentTransactionId, final String disputeId) {
        final var raisedAt = LocalDateTime.now();
        final var ttlInDays = 2;
        return NonFinancialDisputeWorkflow.builder()
            .key(StorageUtils.primaryKey())
            .disputeWorkflowId(idHelper.disputeWorkflowId(paymentTransactionId))
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .disputeSourceType(SourceType.API)
            .disputeSourceId("CS")
            .transactionReferenceId(paymentTransactionId)
            .disputeType(DisputeType.UDIR_OUTGOING_COMPLAINT)
            .disputeStage(disputeStage)
            .currentEvent(DisputeWorkflowEvent.CREATE_ENTRY)
            .currentState(DisputeWorkflowState.RECEIVED)
            .disputedAmount(complaintAmount)
            .raisedAt(raisedAt)
            .respondBy(raisedAt.plusDays(ttlInDays))
            .gandalfUserId(Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails().getUserId())
            .userType(UserType.SYSTEM)
            .disputeId(disputeId)
            .build();
    }

    private DisputeCategory validateDeemedAndGetCategory(final SentPayment sentPayment) {
        final var accountPaymentInstrument = getAccountPaymentInstrument(sentPayment);
        if (accountPaymentInstrument.getTransactionState() != PaymentState.DEEMED) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_UDIR_COMPLAINT,
                Map.of(
                    Constants.MESSAGE,
                    "Transaction State does not match the with the complaintRequestType"
                ));
        }
        return DisputeCategory.UDIR_DEEMED;
    }

    private AccountPaymentInstrument getAccountPaymentInstrument(final SentPayment sentPayment) {
        final var paymentInstrumentList = sentPayment.getPaidFrom();
        return paymentInstrumentList.stream().filter(
                paymentInstrument -> PaymentInstrumentType.ACCOUNT == paymentInstrument.getType())
            .filter(AccountPaymentInstrument.class::isInstance)
            .map(AccountPaymentInstrument.class::cast).findFirst()
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION, Map.of(
                Constants.MESSAGE, "Unable to find Account Payment"
            )));
    }

    private DisputeCategory validateDrcAndGetCategory(
        final SentPayment sentPayment) { //Payments to provide a better way to test drc
        final boolean isValid;
        final Set<String> reversalCodes = Set.of("RR", "96");
        final Set<String> adjustmentCodes = Set.of("102", "103", "104", "144");

        final var accountPaymentInstrument = getAccountPaymentInstrument(sentPayment);

        if (accountPaymentInstrument.getReversalResponseCode() != null) {
            isValid = reversalCodes.contains(accountPaymentInstrument.getReversalResponseCode())
                && !sentPayment.getPaymentFlags().contains(OptionFlag.PAYMENT_DEEMED)
                && (accountPaymentInstrument.getAdjustmentCode() == null
                || !adjustmentCodes.contains(
                accountPaymentInstrument.getAdjustmentCode()));
        } else {
            isValid = false;
        }

        if (!isValid) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_UDIR_COMPLAINT,
                Map.of(
                    Constants.MESSAGE,
                    "Transaction State does not match the with the complaint request type"
                ));
        }
        return DisputeCategory.UDIR_DRC;
    }

    private DisputeCategory validateP2MExternalAndGetCategory(final SentPayment sentPayment) {
        var isPresent = sentPayment.getTo().stream()
            .filter(receiver -> receiver.getType() == DestinationType.VPA)
            .anyMatch(receiver -> !"0000".equals(receiver.getMcc()));

        if (!isPresent) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_UDIR_COMPLAINT,
                Map.of(
                    Constants.MESSAGE,
                    "Transaction State does not match the with the complaint request type"
                ));
        }
        return DisputeCategory.UDIR_SERVICE;
    }

    private Dispute buildDispute(final long amount,
        final DisputeStage disputeStage, final DisputeCategory disputeCategory,
        final String paymentTransactionId, final String complaintId) {
        return Dispute.builder()
            .key(StorageUtils.primaryKey())
            .disputeId(idHelper.disputeId(paymentTransactionId))
            .transactionReferenceId(paymentTransactionId)
            .disputeType(DisputeType.UDIR_OUTGOING_COMPLAINT)
            .currentDisputeStage(disputeStage)
            .transactionAmount(amount)
            .disputeCategory(disputeCategory)
            .disputeIssuer(DisputeIssuer.PHONEPE_CUSTOMER)
            .disputeReferenceId(complaintId)
            .build();
    }


}
