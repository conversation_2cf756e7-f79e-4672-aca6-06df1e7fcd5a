package com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.toa.responses.ToaSummary;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.P2pmToaAggregationCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.P2pmToaAggregationKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.KillSwitchClient;
import com.phonepe.merchant.platform.stratos.server.core.configs.P2pmToaConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.P2pmToaThresholdBreachNotificationEvent;
import com.phonepe.merchant.platform.stratos.server.core.events.type.StratosErrorEvent;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.P2pmToaConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.PrimaryKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.ToaDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.ToaDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowStateNonMandatoryVisitor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.ToaSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.visitors.ToaFilterVisitor;
import com.phonepe.merchants.platform.notificationbundle.client.NotificationClient;
import com.phonepe.merchants.platform.notificationbundle.models.alerts.AlertTemplate;
import com.phonepe.merchants.platform.notificationbundle.models.alerts.AlertType;
import com.phonepe.merchants.platform.notificationbundle.models.alerts.receivers.AlertReceiver;
import com.phonepe.merchants.platform.notificationbundle.models.alerts.receivers.EmailReceiver;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import com.phonepe.models.payments.merchant.MerchantTransactionState.MerchantTransactionStateVisitor;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.platform.killswitch.common.RecommendedAction;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Singleton
public class P2pmToaService {

    public static final String RECONCILE_FAILURE_MESSAGE = "P2PM Toa Reconcile is not supported for this state. Supported states are %s";
    private final ToaFilterVisitor toaFilterVisitor;
    private final DisputeService disputeService;
    private final ToaDisputeMetadataRepository toaDisputeMetadataRepository;
    private final P2pmToaAggregationCommand p2pmToaAggregationCommand;
    private final PaymentsService paymentsService;
    private final KillSwitchClient killSwitchClient;
    private final NotificationClient notificationClient;
    private final IdHelper idHelper;
    private final P2pmToaConfig p2PMToaConfig;
    private final EventIngester eventIngester;
    private final ToaSummaryFileFormatVisitor toaSummaryFileFormatVisitor;

    @Inject
    @SuppressWarnings("java:S107")
    public P2pmToaService(
        final ToaFilterVisitor toaFilterVisitor,
        final DisputeService disputeService,
        final ToaDisputeMetadataRepository toaDisputeMetadataRepository,
        final P2pmToaAggregationCommand p2pmToaAggregationCommand,
        final PaymentsService paymentsService,
        final KillSwitchClient killSwitchClient,
        final NotificationClient notificationClient,
        final IdHelper idHelper,
        @P2pmToaConfiguration final P2pmToaConfig p2PMToaConfig,
        final ToaSummaryFileFormatVisitor toaSummaryFileFormatVisitor,
        final EventIngester eventIngester) {
        this.toaFilterVisitor = toaFilterVisitor;
        this.disputeService = disputeService;
        this.toaDisputeMetadataRepository = toaDisputeMetadataRepository;
        this.p2pmToaAggregationCommand = p2pmToaAggregationCommand;
        this.paymentsService = paymentsService;
        this.killSwitchClient = killSwitchClient;
        this.notificationClient = notificationClient;
        this.idHelper = idHelper;
        this.p2PMToaConfig = p2PMToaConfig;
        this.eventIngester = eventIngester;
        this.toaSummaryFileFormatVisitor = toaSummaryFileFormatVisitor;
    }

    public Set<DisputeWorkflowState> getAllStates() {

        return Arrays.stream(DisputeWorkflowState.values())
            // Get only the toa related states
            // all states between TOA_BLOCKED_DUE_TO_KS and P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY and RECEIVED state
            .filter(
                state -> (state.ordinal() >= DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS.ordinal() &&
                    state.ordinal()
                        <= DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY.ordinal()
                )
                    || state.ordinal() == DisputeWorkflowState.RECEIVED.ordinal())
            .collect(Collectors.toSet());
    }

    public List<ToaSummary> filter(final DisputeFilter filter) {
        List<DisputeWorkflow> disputeWorkflowList = filter.accept(toaFilterVisitor);
        return disputeWorkflowList.stream() // NOSONAR
            .map(TransformationUtils::toToaSummary)
            .toList();
    }

    public byte[] download(final FileFormat fileFormat, final DisputeFilter filter) {

        final var disputePairs = filter.accept(toaFilterVisitor);
        final var p2pmToaSummaries = disputePairs.stream()
            .map(TransformationUtils::toDisputeSummaryRow)
            .collect(Collectors.toList());

        return fileFormat.accept(toaSummaryFileFormatVisitor, p2pmToaSummaries);
    }

    public GenericResponse<ToaSummary> reconcile(final String transactionReferenceId,
        final String workflowId,
        UserAuthDetails userAuthDetails) {
        // get current dispute workflow
        var disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(transactionReferenceId,
            workflowId);

        // Gracefully ignoring invalid reconcilable state
        if (!Constants.P2PM_TOA_RECONCILE_STATES.contains(disputeWorkflow.getCurrentState())){
            return GenericResponse.<ToaSummary>builder()
                .success(false)
                .data(TransformationUtils.toToaSummary(disputeWorkflow))
                .message(String.format(RECONCILE_FAILURE_MESSAGE,
                    String.join(",",Constants.P2PM_TOA_RECONCILE_STATES.stream().map(Enum::name).toList())))
                .build();
        }

        var txnStatus = getP2pmToaPayStatus(disputeWorkflow);

        boolean isEventTriggered = txnStatus.visit(new MerchantTransactionStateVisitor<>() {
            @Override
            public Boolean visitCreated() {
                //still in pending
                return false;
            }

            @Override
            public Boolean visitCompleted() {
                disputeService.triggerEvent(
                    userAuthDetails,
                    transactionReferenceId,
                    workflowId,
                    DisputeWorkflowEvent.PENDING_TO_COMPLETED,
                    Constants.EMPTY_TRANSITION_CONTEXT);
                return true;
            }

            @Override
            public Boolean visitFailed() {
                disputeService.triggerEvent(
                    userAuthDetails,
                    transactionReferenceId,
                    workflowId,
                    DisputeWorkflowEvent.PENDING_TO_FAILED,
                    Constants.EMPTY_TRANSITION_CONTEXT);
                return true;
            }

            @Override
            public Boolean visitAccepted() {
                //still in pending
                return false;
            }

            @Override
            public Boolean visitCancelled() {
                return visitFailed();
            }
        });

        // fetch latest workflow after event trigger
        if (isEventTriggered) {
            disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(transactionReferenceId,
                workflowId);
        }

        return GenericResponse.<ToaSummary>builder()
            .success(isEventTriggered)
            .data(TransformationUtils.toToaSummary(disputeWorkflow))
            .message("P2PM Toa pay status is " + txnStatus.name())
            .build();

    }

    public boolean isP2pmToaKillSwitchEnabled() {
        var p2pmToaKillSwitch = killSwitchClient.getP2pmToaKillSwitch();
        return p2pmToaKillSwitch.isPresent() &&
            p2pmToaKillSwitch.get().getDuration() > 0 &&
            p2pmToaKillSwitch.get().getRecommendedAction() == RecommendedAction.BLOCK;
    }

    public boolean engageP2pmToaKillSwitch(String reason, UserAuthDetails userAuthDetails){
        if(Objects.isNull(reason) || reason.isBlank()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.EMPTY_COMMENT_NOT_ALLOWED, Map.of());
        }
        if (isP2pmToaKillSwitchEnabled()){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.KS_ALREADY_ENGAGED, Map.of());
        }
        return !Objects.isNull(killSwitchClient.engageP2pmKillSwitch(reason, userAuthDetails));

    }

    public Boolean deactivateP2pmKillSwitch() {
        var p2pmToaKillSwitch = killSwitchClient.getP2pmToaKillSwitch();
        if(p2pmToaKillSwitch.isEmpty()){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.KS_NOT_ENGAGED, Map.of(Constants.MESSAGE, "P2PM TOA Kill switch is not engaged."));
        }
        return !Objects.isNull(killSwitchClient.deactivateKillSwitch(p2pmToaKillSwitch.get().getId()));
    }

    public int getToaRetryCount(DisputeWorkflow disputeWorkflow) {
        List<ToaDisputeMetadata> disputeMetadataList = getAllToaMetadata(disputeWorkflow);
        return disputeMetadataList.size();
    }

    public List<ToaDisputeMetadata> getAllToaMetadata(DisputeWorkflow disputeWorkflow) {
        return toaDisputeMetadataRepository.selectAllToaDisputeMetadata(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId());
    }

    public void initiateP2PmPay(DisputeWorkflow disputeWorkflow) {
        int retryCount = getToaRetryCount(disputeWorkflow);
        var merchantOrderId = idHelper.merchantOrderId(retryCount,disputeWorkflow.getTransactionReferenceId());
        initiateP2PmPay(merchantOrderId, disputeWorkflow);
    }

    private void initiateP2PmPay(final String merchantOrderId, DisputeWorkflow disputeWorkflow) {
        var payResponse = paymentsService.p2pmToaPay(
            disputeWorkflow.getDisputedAmount(), disputeWorkflow.getDispute().getMerchantId(),
            merchantOrderId,
            disputeWorkflow.getTransactionReferenceId()
        );
        toaDisputeMetadataRepository.save(buildToaDisputeMetadata(merchantOrderId,disputeWorkflow,payResponse.getTransactionId()));
    }

    public GenericResponse<ToaSummary> retry(final String transactionReferenceId,
        final String workflowId,
        UserAuthDetails userAuthDetails) {
        final var disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
            transactionReferenceId, workflowId);

        ValidationUtils.validateRetryableP2pmToaState(disputeWorkflow.getCurrentState());

        final String[] message = new String[1];

        boolean isEventTriggered = disputeWorkflow.getCurrentState()
            .accept(new DisputeWorkflowStateNonMandatoryVisitor<>() {

                @Override
                public Boolean visitToaBlockedDueToKs() {
                    disputeService.triggerEvent(
                        userAuthDetails,
                        transactionReferenceId,
                        disputeWorkflow.getDisputeWorkflowId(),
                        DisputeWorkflowEvent.TOA_BLOCKED_TO_RECEIVED,
                        Constants.EMPTY_TRANSITION_CONTEXT);
                    message[0] = "TOA moved to RECEIVED state";
                    return true;
                }

                @Override
                public Boolean visitP2pmToaInitiationFailed() {
                    // Re-initiate TOA in SYNC
                    try {
                        initiateP2PmPay(disputeWorkflow);
                        disputeService.triggerEvent(
                            userAuthDetails,
                            disputeWorkflow.getTransactionReferenceId(),
                            disputeWorkflow.getDisputeWorkflowId(),
                            DisputeWorkflowEvent.RE_INITIATE_TOA,
                            Constants.EMPTY_TRANSITION_CONTEXT);
                        return true;
                    } catch (Exception e) {
                        message[0] = e.getMessage();
                        log.info("TOA re-initiate failed", e);
                        return false;
                    }
                }

                @Override
                public Boolean visitP2pmToaFailedAfterMaxAutoRetry() {
                    try {
                        var metadataList = getAllToaMetadata(disputeWorkflow);
                        int retryCount = metadataList.size();
                        // check last payment status
                        boolean isMerchantTransactionStatusFailed = isMerchantTransactionStatusFailed(
                            metadataList.get(0).getMerchantOrderId(),
                            disputeWorkflow);

                        if (isMerchantTransactionStatusFailed) {
                            String newMerchantOrderId = idHelper.merchantOrderId(retryCount,
                                disputeWorkflow.getTransactionReferenceId());
                            initiateP2PmPay(newMerchantOrderId, disputeWorkflow);
                        } else {
                            eventIngester.generateEvent(StratosErrorEvent.builder()
                                .errorCode(StratosErrorCodeKey.TOA_INVALID_RE_INITIATE)
                                .message(String.format(
                                    "A Pending/Successful pay txn has reached the failed after max retry state. "
                                        + "DEBUG THIS. disputeWorkFlowId: %s",
                                    disputeWorkflow.getDisputeWorkflowId()))
                                .build());
                            log.info(
                                "A Pending/Successful pay txn has reached the failed after max retry state "
                                    + "which should not happen. DEBUG THIS, txnId: {}, disputeWorkFlowId: {}",
                                disputeWorkflow.getTransactionReferenceId(),
                                disputeWorkflow.getDisputeWorkflowId());
                        }
                        disputeService.triggerEvent(
                            userAuthDetails,
                            disputeWorkflow.getTransactionReferenceId(),
                            disputeWorkflow.getDisputeWorkflowId(),
                            DisputeWorkflowEvent.RETRY_FAILED_TOA,
                            Constants.EMPTY_TRANSITION_CONTEXT);
                    } catch (Exception e) {
                        message[0] = e.getMessage();
                        return false;
                    }

                    return true;
                }
            });

        // fetch latest workflow after event trigger
        DisputeWorkflow newDisputeWorkflow = null;
        if (isEventTriggered) {
            newDisputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
                transactionReferenceId, workflowId);
        } else {
            newDisputeWorkflow = disputeWorkflow;
        }

        return GenericResponse.<ToaSummary>builder()
            .success(isEventTriggered)
            .data(TransformationUtils.toToaSummary(newDisputeWorkflow))
            .message(message[0])
            .build();
    }

    public MerchantTransactionState getP2pmToaPayStatus(DisputeWorkflow disputeWorkflow) {

        List<ToaDisputeMetadata> disputeMetadataList = getAllToaMetadata(disputeWorkflow);

        String merchantOrderId = disputeMetadataList.get(0).getMerchantOrderId();

        return paymentsService.getMerchantTransactionStatus(
            disputeWorkflow.getDispute().getMerchantId(), merchantOrderId);
    }

    public boolean isMerchantTransactionStatusFailed(String merchantOrderId,
        DisputeWorkflow disputeWorkflow) {
        MerchantTransactionState merchantTransactionStatus = paymentsService.getMerchantTransactionStatus(
            disputeWorkflow.getDispute().getMerchantId(), merchantOrderId);

        return merchantTransactionStatus.visit(new MerchantTransactionStateVisitor<>() {
            @Override
            public Boolean visitCreated() {
                return false;
            }

            @Override
            public Boolean visitCompleted() {
                return false;
            }

            @Override
            public Boolean visitFailed() {
                return true;
            }

            @Override
            public Boolean visitAccepted() {
                return false;
            }

            @Override
            public Boolean visitCancelled() {
                return visitFailed();
            }

        });
    }

    private ToaDisputeMetadata buildToaDisputeMetadata(String merchantOrderId,
        DisputeWorkflow disputeWorkflow, String disbursementTransactionId) {
        return ToaDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(disputeWorkflow.getKey().getPartitionId())
                .build())
            .disputeMetadataId(
                idHelper.disputeMetaDataId(disputeWorkflow.getTransactionReferenceId()))
            .disbursementTransactionId(disbursementTransactionId)
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .merchantOrderId(merchantOrderId)
            .build();
    }

    public GenericResponse<ToaSummary> processToaExternally(String transactionReferenceId,
        String disputeWorkflowId, TransitionContext transitionContext, UserAuthDetails userAuthDetails) {

        final var disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
            transactionReferenceId, disputeWorkflowId);
        ValidationUtils.validateExternallyCompletableP2pmToaState(
            disputeWorkflow.getCurrentState());
        var disputeWorkflowEvent = disputeWorkflow.getCurrentState()
            .accept(new DisputeWorkflowStateNonMandatoryVisitor<DisputeWorkflowEvent>() {
                @Override
                public DisputeWorkflowEvent visitToaBlockedDueToKs() {
                    return DisputeWorkflowEvent.TOA_BLOCKED_TO_EXTERNAL_PROCESSED;
                }

                @Override
                public DisputeWorkflowEvent visitP2pmToaInitiationFailed() {
                    return DisputeWorkflowEvent.INITIATION_FAILED_TO_EXTERNAL_PROCESSED;
                }

                @Override
                public DisputeWorkflowEvent visitP2pmToaFailedAfterMaxAutoRetry() {
                    return DisputeWorkflowEvent.MAX_RETRY_TO_PROCESSED_EXTERNALLY;
                }
            });

        disputeService.triggerEvent(
            userAuthDetails,
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            disputeWorkflowEvent,
            transitionContext);

        var newDisputeWorkflow = disputeService.validateAndGetDisputeWorkflow(
            transactionReferenceId, disputeWorkflowId);

        return GenericResponse.<ToaSummary>builder()
            .success(true)
            .data(TransformationUtils.toToaSummary(newDisputeWorkflow))
            .build();
    }

    public void validateToaAmountThresholdBreach(DisputeWorkflow disputeWorkflow) {
        if (disputeWorkflow.getCurrentState() != DisputeWorkflowState.P2PM_TOA_COMPLETED) {
            return;
        }

        long todayProcessedToaAmount = p2pmToaAggregationCommand.incrementByValueAndGetCounter(
            P2pmToaAggregationKey.builder()
                .date(LocalDate.now())
                .build(),
            disputeWorkflow.getDisputedAmount()
        );

        if (todayProcessedToaAmount > p2PMToaConfig.getP2pmToaPerDayNotificationThreshold()) {
            sentEmailForThresholdBreach(todayProcessedToaAmount, disputeWorkflow);
        }
    }

    public void sentEmailForThresholdBreach(long breachAmount, DisputeWorkflow disputeWorkflow) {

        String date = DateTimeFormatter.ofPattern("dd-MM-yyyy").format(LocalDate.now());
        double thresholdAmountInRs = p2PMToaConfig.getP2pmToaPerDayNotificationThreshold() * 1.0 / 100;
        double breachAmountInRs = breachAmount * 1.0 / 100;
        double diffInRs = breachAmountInRs - thresholdAmountInRs;

        AlertReceiver alertReceiver = EmailReceiver.builder()
            .emailId(p2PMToaConfig.getBreachNotificationEmailId())
            .id(disputeWorkflow.getDisputeWorkflowId())
            .enableRetry(true)
            .build();

        AlertTemplate alertTemplate = AlertTemplate.builder()
            .alertType(AlertType.EMAIL)
            .templateBody(String.format(Constants.TOA_BREACH_EMAIL_TEMPLATE,
                date, thresholdAmountInRs, breachAmountInRs, diffInRs))
            .templateSubject("P2PM TOA Threshold Breach " + date)
            .build();

        try {
            notificationClient.sendNotification(alertReceiver, alertTemplate, null);
        }catch (Exception e){
            log.error("Sent email failed",e);
            //ADD Events for this
        }

        eventIngester.generateEvent(P2pmToaThresholdBreachNotificationEvent.builder()
            .breachAmount(breachAmount)
            .subject(alertTemplate.getTemplateSubject())
            .toEmailId(p2PMToaConfig.getBreachNotificationEmailId())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .build());
    }
}
