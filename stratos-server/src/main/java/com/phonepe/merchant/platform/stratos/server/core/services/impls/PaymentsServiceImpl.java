package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.models.common.enums.PaymentState;
import com.phonepe.models.common.enums.ResponseCode;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import com.phonepe.models.payments.pay.PaymentProcessorResult;
import com.phonepe.models.payments.pay.ReversedTransaction;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.upi.udircomplaint.UdirOutgoingComplaintRequest;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.models.response.PaymentResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PaymentsServiceImpl implements PaymentsService {

    private static final Set<ResponseCode> ACCEPTABLE_NON_SUCCESS_CODE = Set
        .of(ResponseCode.NON_REVERSIBLE_FLOW, ResponseCode.MULTIPLE_PAYMENT_SOURCES_PRESENT);

    private final PaymentsTxnlClient paymentsTxnlClient;

    @Override
    public boolean isFullyReversed(final String originalTransactionId) {

        final var transactionDetailResponse = paymentsTxnlClient
            .getTransactionDetails(originalTransactionId);

        if (!transactionDetailResponse.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENTS_CLIENT_ERROR, Map.of(
                Constants.MESSAGE, "Error occurred while fetching transaction details",
                Constants.TRANSACTION_ID, originalTransactionId));
        }

        final var transactionDetail = transactionDetailResponse.getData();

        if (transactionDetail.getAlreadyReversedTransactions() == null) {
            return false;
        }

        final var reversedAmount = transactionDetail.getAlreadyReversedTransactions().stream()
            .filter(rt -> rt.getPaymentState() == PaymentState.COMPLETED)
            .mapToLong(ReversedTransaction::getAmount)
            .sum();

        return transactionDetail.getReceivedPayment().getAmount() == reversedAmount;
    }

    @Override
    public boolean hasRgcsReversalTransactions(final String originalTransactionId) {

        final var rgcsReversalTransactionsResponse = paymentsTxnlClient
            .getRgcsReversalTransactions(originalTransactionId);

        if (!rgcsReversalTransactionsResponse.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENTS_CLIENT_ERROR, Map.of(
                Constants.MESSAGE, "Error occurred while fetching RGCS reversal details",
                Constants.TRANSACTION_ID, originalTransactionId));
        }

        final var rgcsReversalTransactions = rgcsReversalTransactionsResponse.getData();

        return rgcsReversalTransactions != null && !rgcsReversalTransactions.isEmpty();
    }

    @Override
    public void blockReversals(final String originalTransactionId) {

        final var blockReversalsResponse = paymentsTxnlClient
            .blockReversals(originalTransactionId);

        handleBlockUnblockReversalsResponse(originalTransactionId, blockReversalsResponse);
    }

    @Override
    public Void unblockReversals(final String originalTransactionId) {

        final var blockReversalsResponse = paymentsTxnlClient
            .unblockReversals(originalTransactionId);

        handleBlockUnblockReversalsResponse(originalTransactionId, blockReversalsResponse);
        return null;
    }

    @Override
    public String getGlobalPaymentId(final String originalTransactionId) {

        final var transactionDetailResponse = paymentsTxnlClient
            .getTransactionDetails(originalTransactionId);

        if (!transactionDetailResponse.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENTS_CLIENT_ERROR, Map.of(
                Constants.MESSAGE, "Error occurred while fetching transaction details",
                Constants.TRANSACTION_ID, originalTransactionId));
        }

        final var transactionDetail = transactionDetailResponse.getData();

        return transactionDetail.getReceivedPayment().getGlobalPaymentId();
    }

    @Override
    public TransactionDetail transactionDetailFromUpiId(final String upiTransactionId,
        final String utr,
        final LocalDateTime txnDate) {

        final var response = paymentsTxnlClient.getPaymentsIdFromUpiTxnId(upiTransactionId, utr,
            txnDate);

        if (!response.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_UPI_ID, Map.of(Constants.MESSAGE,
                    "Invalid upi txn Id " + upiTransactionId));
        }

        final var detailResponse = paymentsTxnlClient.getTransactionDetails(response.getData());

        if (!detailResponse.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_UPI_ID,
                Map.of(Constants.MESSAGE, "Invalid payments Id " + response.getData()));
        }

        return detailResponse.getData();
    }

    @Override
    public TransactionDetail transactionDetailFromOriginalTransactionId(
        final String originalTransactionId) {

        log.info("Calling payments for transaction details for transactionId {}", originalTransactionId);
        final var detailResponse = paymentsTxnlClient.getTransactionDetails(originalTransactionId);

        if (!detailResponse.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_PAYMENTS_ID,
                Map.of(Constants.MESSAGE, "Invalid payments Id " + originalTransactionId,
                    Constants.SERVICE_NAME, "PaymentsService"));
        }
        log.info("Transaction details from payments {}, for transactionId {}", detailResponse.getData(), originalTransactionId);
        return detailResponse.getData();
    }

    private void handleBlockUnblockReversalsResponse(
        final String originalTransactionId,
        final GenericResponse<ResponseCode> blockReversalsResponse) {

        final var responseCode = blockReversalsResponse.getData();

        // Either Success = False and Response Code is Acceptable
        // Or Success = True and Response Code is Success
        // Then Valid Combination
        // Else Throw Error
        if (!blockReversalsResponse.isSuccess()
            && ACCEPTABLE_NON_SUCCESS_CODE.contains(responseCode)) {
            return;
        }
        if (blockReversalsResponse.isSuccess() && ResponseCode.SUCCESS == responseCode) {
            return;
        }

        throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENTS_CLIENT_ERROR, Map.of(
                Constants.MESSAGE, "Error occurred while blocking/unblocking reversals",
                Constants.TRANSACTION_ID, originalTransactionId));
    }


    @Override
    public void raiseUdirComplaint(UdirOutgoingComplaintRequest udirOutgoingComplaintRequest) {
        paymentsTxnlClient.raiseUdirComplaint(udirOutgoingComplaintRequest);
    }

    @Override
    public PaymentProcessorResult p2pmToaPay(final long amount,final String merchantId, final String merchantOrderId, final String originalTxnId){


        var payResponse = paymentsTxnlClient.createP2pmTransaction(
            amount, merchantId, merchantOrderId, originalTxnId);

        if(!payResponse.isSuccess())  {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENT_INITIATION_FAILED,Map.of(
                Constants.ORIGINAL_TXN_ID, originalTxnId,
                Constants.MERCHANT_ID, merchantId,
                Constants.MERCHANT_ORDER_ID, merchantOrderId,
                Constants.PAYMENT_RESPONSE_CODE, payResponse.getCode()
                ));
        }

        return payResponse.getData();
    }

    @Override
    public MerchantTransactionState getMerchantTransactionStatus(String merchantId, String merchantTransactionId) {
        GenericResponse<PaymentResponse> merchantTransactionStatus = paymentsTxnlClient.getMerchantTransactionStatus(
            merchantId, merchantTransactionId);

        if (!merchantTransactionStatus.isSuccess()){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENT_MERCHANT_TXN_STATUS_CHECK_FAILED,Map.of(
                Constants.MERCHANT_ID, merchantId,
                Constants.MERCHANT_ORDER_ID, merchantTransactionId,
                Constants.PAYMENT_RESPONSE_CODE, merchantTransactionStatus.getCode()
            ));
        }
        log.debug("Merchant Transaction Status for merchantTxnId {} is {}",merchantTransactionId, merchantTransactionStatus);
       return merchantTransactionStatus.getData().getPaymentState();
    }
}
