package com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseHoldRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.InitiateMerchantCommunicationAction;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PartialFulfilmentWorkflowConfigurator implements WorkflowConfigurator<DisputeWorkflowState, DisputeWorkflowEvent> {

    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final AcceptPartialDisputeAction acceptPartialDisputeAction;
    private final FraudCheckDisputeAction fraudCheckUpdateDisputeAction;
    private final RaiseHoldRecoveryAccountingEventAction raiseAccountingEventAction;
    private final InitiateMerchantCommunicationAction initiateCommunicationAction;

    @Override
    @SneakyThrows
    public void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {

        transitions
                .withExternal()
                .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
                .target(DisputeWorkflowState.SUSPECTED_FRAUD)
                .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
                .target(DisputeWorkflowState.FRAUD_REJECTED)
                .event(DisputeWorkflowEvent.FRAUD_REJECT)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .target(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
                .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS)
                .action(fraudCheckUpdateDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
                .target(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT)
                .action(acceptPartialDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
                .target(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT)
                .action(acceptPartialDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
                .target(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
                .action(initiateCommunicationAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED)
                .target(DisputeWorkflowState.MERCHANT_ACTION_REQUESTED)
                .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
                .action(initiateCommunicationAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
                .target(DisputeWorkflowState.RECOVER_PARTIAL_HOLD_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_RECOVER_HOLD_EVENT)
                .action(raiseAccountingEventAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RECOVER_PARTIAL_HOLD_EVENT_RAISED)
                .target(DisputeWorkflowState.RECOVER_PARTIAL_HOLD_EVENT_ACCEPTED)
                .event(DisputeWorkflowEvent.ACCEPT_RECOVER_HOLD_EVENT)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.RECOVER_PARTIAL_HOLD_EVENT_ACCEPTED)
                .target(DisputeWorkflowState.HOLD)
                .event(DisputeWorkflowEvent.HOLD)
                .action(updateDisputeStateAction)
                .and()
        ;

    }
}
