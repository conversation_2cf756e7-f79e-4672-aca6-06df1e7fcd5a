package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.server.service.PenaltyClassService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.WardenClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.FileCheckerReviewData;
import com.phonepe.merchant.platform.stratos.server.core.models.FileState;
import com.phonepe.merchant.platform.stratos.server.core.models.WardenWorkflowType;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.FileProcessorActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.FileProcessorMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.WardenService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.services.warden.core.models.data.context.JsonNodeWorkflowContext;
import com.phonepe.services.warden.core.models.responses.config.WardenWorkflowConfig;
import com.phonepe.services.warden.core.models.responses.instance.WardenWorkflowInstance;
import com.phonepe.services.warden.models.callback.ReviewCallbackRequest;
import com.phonepe.warden.workflow.models.enums.WorkflowInstanceState;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class WardenServiceImpl implements WardenService {
    private final WardenClient wardenClient;
    private final FileRepository fileRepository;
    private final EventIngester eventIngester;
    private final Provider<FileProcessorActor> fileProcessorActorProvider;

    private final PenaltyClassService penaltyClassService;

    @Override
    public WardenWorkflowInstance raiseFileUploadRequest(File file, String authToken) {
        FileCheckerReviewData data = FileCheckerReviewData.builder()
                .fileType(file.getFileType())
                .disputeType(file.getDisputeType())
                .rowCount(file.getRowCount())
                .fileId(file.getFileId())
                .fileName(file.getFileName())
                .createdAt(file.getCreatedAt())
                .build();
        final ObjectNode objectNode = MapperUtils.convertValue(
                data, new TypeReference<>() {});
        WardenWorkflowInstance response = wardenClient.createWorkflow(
                WardenWorkflowType.CHARGEBACK_FILE_UPLOAD.toString(),
                objectNode,
                authToken);
        if (response.getState().equals(WorkflowInstanceState.FAILED)){
            log.error("Exception while creating warden workflow instance for file : {}", file.getFileName());
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.MAKER_REQUEST_FAILED, Map.of(
                Constants.MESSAGE, "Creating maker operation for file upload failed"));
        }
        return response;
    }

    @Override
    public WardenWorkflowInstance raisePenaltyClassCreation(PenaltyClass penaltyClass, String workflowType , String authToken) {
        final ObjectNode objectNode = MapperUtils.convertValue(
                penaltyClass, new TypeReference<>() {});
        WardenWorkflowInstance response = wardenClient.createWorkflow(
                workflowType,
                objectNode,
                authToken);
        if (response.getState().equals(WorkflowInstanceState.FAILED)){
            log.error("Exception while creating warden workflow instance for penalty class : {}", penaltyClass.getId());
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.MAKER_REQUEST_FAILED, Map.of(
                    Constants.MESSAGE, "Creating maker operation for penalty class creation failed"));
        }
        return response;
    }


    @Override
    public void processCallback(ReviewCallbackRequest callbackRequest) {
        String workflowId = callbackRequest.getWorkflowId();
        try {
            WardenWorkflowInstance wardenWorkflowInstance = getWorkflowDetailsFromWorkflowId(workflowId);
            String workflowType = wardenWorkflowInstance.getWorkflowConfig()
                    .getWorkflowType();
            if (StringUtils.containsAnyIgnoreCase(
                    workflowType,WardenWorkflowType.CHARGEBACK_FILE_UPLOAD.toString())) {
                handleFileUploadCallback(wardenWorkflowInstance);
            } else if (StringUtils.containsAnyIgnoreCase(
                    workflowType,WardenWorkflowType.PENALTY_CLASS_CREATION.toString())) {
                handlePenaltyClassCreationCallback(wardenWorkflowInstance);
            } else {
                log.error("Unsupported workflow type in warden workflow : {}", callbackRequest);
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.CALLBACK_PROCESSING_FAILED,
                        Map.of(Constants.MESSAGE, "Unsupported workflow type in warden workflow type"));
            }
        } catch (Exception e) {
            log.error("Exception while processing warden callback for workflowId : {}", workflowId, e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.CALLBACK_PROCESSING_FAILED,
                    Map.of(Constants.MESSAGE, "Error while processing Warden callback"));
        }
    }

    private void handleFileUploadCallback(WardenWorkflowInstance wardenWorkflowInstance) throws Exception {
        JsonNodeWorkflowContext jsonNodeWorkflowContext =
                (JsonNodeWorkflowContext) wardenWorkflowInstance
                        .getData().getReviewContext();
        FileCheckerReviewData reviewData = MapperUtils.convertValue(
                jsonNodeWorkflowContext.getData(),
                new TypeReference<>() {});
        String fileId = reviewData.getFileId();
        if(!WorkflowInstanceState.APPROVED.equals(wardenWorkflowInstance.getState())){
            log.info("File is not in APPROVED state. File : {} ",wardenWorkflowInstance);
            processUnApprovedCallback(fileId, wardenWorkflowInstance);
        }
        else{
            log.debug("File is in APPROVED state, processing the file : {}",fileId);
            fileProcessorActorProvider.get().publish(FileProcessorMessage.builder()
                    .fileId(fileId)
                    .build());
        }
    }

    private void handlePenaltyClassCreationCallback(WardenWorkflowInstance wardenWorkflowInstance) {
        String reviewContextType = wardenWorkflowInstance.getData().getReviewContext().getType();
        if(StringUtils.equals(JsonNodeWorkflowContext.JSON_NODE,reviewContextType)){
            JsonNodeWorkflowContext jsonNodeWorkflowContext = (JsonNodeWorkflowContext) wardenWorkflowInstance.getData().getReviewContext();
            if(!WorkflowInstanceState.APPROVED.equals(wardenWorkflowInstance.getState())){
                log.error("Penalty class creation is not in APPROVED state. Penalty class :");
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.MAKER_REQUEST_FAILED, Map.of(
                        Constants.MESSAGE, "Penalty class creation is not in APPROVED state"));
            }else {
                PenaltyClass penaltyClass = MapperUtils.convertValue(jsonNodeWorkflowContext.getData(), new TypeReference<>() {});
                log.debug("Penalty class creation is in APPROVED state, processing the penalty class : {}", penaltyClass);
                penaltyClassService.activatePenaltyClass(penaltyClass);
            }
        }
    }


    @Override
    public WardenWorkflowConfig createNewWardenWorkflowConfig(String workflowType,
                                                              String userAuthToken,
                                                              List<UserDetails> userDetails) {
        try{
            log.info("Creating new warden workflow for workflowType {} authdetail {} user {}",workflowType,userAuthToken, userDetails);
            return wardenClient.createNewWorkFlowType(
                    workflowType,
                    userAuthToken,
                    userDetails);
        }catch (Exception exception){
            log.error("Exception in creating the new warden workflow for workflowType {} user {}",workflowType, userDetails, exception);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR, Map.of(
                    Constants.MESSAGE, "Error while creating new warden workflow"));
        }
    }

    private void processUnApprovedCallback(String fileId,  WardenWorkflowInstance wardenWorkflowInstance) {
        FileState state;
        switch (wardenWorkflowInstance.getState()){
            case REJECTED -> state = FileState.REJECTED;
            case FAILED, DISCARDED -> state = FileState.FAILED;
            default -> state = FileState.ACCEPTED;
        }
        fileRepository.updateFile(fileId, fileTemp -> {
            fileTemp.setFileState(state);
            //file state event
            eventIngester.generateEvent(
                FoxtrotEventUtils.toFileProcessStateEvent(fileTemp)
            );
            return fileTemp;
        });
    }
    private WardenWorkflowInstance getWorkflowDetailsFromWorkflowId(String workflowId){
        try {
            WardenWorkflowInstance wardenWorkflowInstance = wardenClient
                    .getWorkflowDetailsFromWorkflowId(workflowId);
            log.info("Workflow details received for workflow id {}",workflowId);
            return wardenWorkflowInstance;
        } catch (Exception e) {
            log.error("Exception while getting warden workflow details for workflowId : {}",
                    workflowId,e);
            throw e;
        }
    }

}
