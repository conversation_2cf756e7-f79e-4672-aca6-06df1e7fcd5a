package com.phonepe.merchant.platform.stratos.server.v2.actions;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.AmountHoldService;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds.ChargebackRecoveryFeedPublishActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.raiseevents.RaiseHoldRecoveryAccountingEventActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

import java.util.Optional;

@Slf4j
@Singleton
public class ProactiveHoldAction extends UpdateDisputeStateBaseAction {
    private final Provider<RaiseHoldRecoveryAccountingEventActor> raiseAccountingEventActor;
    private final Provider<ChargebackRecoveryFeedPublishActor> chargebackRecoveryFeedPublishActor;
    private final StateChangeHandlerActor stateChangeHandlerActor;
    private final PaymentsService paymentsService;
    private final EdcService edcService;
    private final AmountHoldService amountHoldService;

    @Inject
    @SuppressWarnings("java:S107")
    public ProactiveHoldAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final EventIngester eventIngester,
            final Provider<RaiseHoldRecoveryAccountingEventActor> raiseAccountingEventActor,
            final Provider<ChargebackRecoveryFeedPublishActor> chargebackRecoveryFeedPublishActor,
            final PaymentsService paymentsService,
            final EdcService edcService,
            final CallbackActor callbackActor, StateChangeHandlerActor stateChangeHandlerActor,
            final AmountHoldService amountHoldService) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.raiseAccountingEventActor = raiseAccountingEventActor;
        this.chargebackRecoveryFeedPublishActor = chargebackRecoveryFeedPublishActor;
        this.paymentsService = paymentsService;
        this.edcService = edcService;
        this.stateChangeHandlerActor = stateChangeHandlerActor;
        this.amountHoldService = amountHoldService;
    }

    @Override
    protected void transition(final DisputeWorkflow disputeWorkflow,
                              final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {
        disputeWorkflow.getDisputeType().accept(new DisputeTypeVisitor<Void>() {
            @Override
            public Void visitUpiChargeback() {
                paymentsService.blockReversals(disputeWorkflow.getTransactionReferenceId());
                return null;
            }

            @Override
            public Void visitPgChargeback() {
                return visitUpiChargeback();
            }

            @Override
            public Void visitUdirOutgoingComplaint() {
                return unsupportedDisputeType();
            }

            @Override
            public Void visitUdirIncomingComplaint() {
                return unsupportedDisputeType();
            }

            @Override
            public Void visitP2PMToa() {
                return unsupportedDisputeType();
            }

            @Override
            public Void visitEdcChargeback() {
                edcService.blockReversals(disputeWorkflow.getDispute().getMerchantTransactionId());
                return null;
            }

            @Override
            public Void visitNetBankingChargeback() {
                return visitUpiChargeback();
            }

            @Override
            public Void visitNotionalCreditToa() {
                return unsupportedDisputeType();
            } //TO-DO: add unsupported exception

            @Override
            public Void visitBbpsTatBreachToa() {return unsupportedDisputeType();}

            @Override
            public Void visitWalletChargeback() {
                return unsupportedDisputeType();
            }
            private Void unsupportedDisputeType(){
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_CREATION,
                    Map.of(Constants.MESSAGE,
                            "Proactive hold is not supported for current dispute type"
                    ));
            }
        });
    }

    @Override
    @SneakyThrows
    protected void postTransition(DisputeWorkflow currentStageDisputeWorkflow) {
        String transactionId = currentStageDisputeWorkflow.getTransactionReferenceId();
        if(!amountHoldService.holdExist(transactionId)){
            merchantRecovery(currentStageDisputeWorkflow);
        }
        else {
            final DisputeWorkflowMessage disputeWorkflowMessage = DisputeWorkflowMessage.builder()
                .disputeWorkflowId(currentStageDisputeWorkflow.getDisputeWorkflowId())
                .transactionReferenceId(currentStageDisputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowEvent(DisputeWorkflowEvent.ACCEPT_RECOVER_HOLD_EVENT)
                .build();
            stateChangeHandlerActor.publish(disputeWorkflowMessage);
        }
        closePreviousWorkflow(currentStageDisputeWorkflow);
    }

    @SneakyThrows
    private void closePreviousWorkflow(DisputeWorkflow currentStageDisputeWorkflow) {
        DisputeStage previousDisputeStage = currentStageDisputeWorkflow.getDisputeStage().getPreviousStage();
        if (previousDisputeStage != null) {
            Optional<DisputeWorkflow> previousStageWorkflow = disputeService.getDisputeWorkflowOptional(
                currentStageDisputeWorkflow.getTransactionReferenceId(), currentStageDisputeWorkflow.getDisputeType(),
                previousDisputeStage);
            if (previousStageWorkflow.isPresent() &&
                    !(previousStageWorkflow.get().getCurrentState().equals(DisputeWorkflowState.END))) {
                DisputeWorkflow dw = previousStageWorkflow.get();
                final DisputeWorkflowMessage previousWorkflowMessage = DisputeWorkflowMessage.builder()
                    .disputeWorkflowId(dw.getDisputeWorkflowId())
                    .disputeWorkflowEvent(DisputeWorkflowEvent.END_WORKFLOW)
                    .transactionReferenceId(dw.getTransactionReferenceId())
                    .build();
                stateChangeHandlerActor.publish(previousWorkflowMessage);
            }
        }
    }

    @SneakyThrows
    private void merchantRecovery(final DisputeWorkflow currentStagedisputeWorkflow) {
        final DisputeWorkflowMessage disputeWorkflowMessage = DisputeWorkflowMessage.builder()
                .transactionReferenceId(currentStagedisputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(currentStagedisputeWorkflow.getDisputeWorkflowId())
                .build();
        chargebackRecoveryFeedPublishActor.get().publish(disputeWorkflowMessage); // is feed publishing necessary now?
        raiseAccountingEventActor.get().publish(disputeWorkflowMessage);
    }
}
