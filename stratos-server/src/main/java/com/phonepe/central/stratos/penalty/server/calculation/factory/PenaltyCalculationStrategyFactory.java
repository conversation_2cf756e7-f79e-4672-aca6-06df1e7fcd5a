package com.phonepe.central.stratos.penalty.server.calculation.factory;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.growth.calculation.Calculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.PenaltyConversionDateFixedAmountCalculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.PenaltyConversionDateFixedPercentageCalculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.TransactionDateFixedAmountCalculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.TransactionDateFixedPercentageCalculation;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.PenaltyCalculationStrategy;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.impl.*;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyCalculationStrategyFactory {

    private final Injector guiceInjector;

    public PenaltyCalculationStrategy getStrategy(Calculation calculation) {
        return calculation.accept(new Calculation.CalculationVisitor<PenaltyCalculationStrategy>() {
            @Override
            public PenaltyCalculationStrategy visit(PenaltyConversionDateFixedPercentageCalculation calculation) {
                return guiceInjector.getInstance(PenaltyConversionDateFixedPercentageCalculationStrategy.class);
            }

            @Override
            public PenaltyCalculationStrategy visit(PenaltyConversionDateFixedAmountCalculation calculation) {
                return guiceInjector.getInstance(PenaltyConversionDateFixedAmountCalculationStrategy.class);
            }

            @Override
            public PenaltyCalculationStrategy visit(TransactionDateFixedPercentageCalculation calculation) {
                return guiceInjector.getInstance(TransactionDateFixedPercentageCalculationStrategy.class);
            }

            @Override
            public PenaltyCalculationStrategy visit(TransactionDateFixedAmountCalculation calculation) {
                return guiceInjector.getInstance(TransactionDateFixedAmountCalculationStrategy.class);
            }
        });
    }
}
