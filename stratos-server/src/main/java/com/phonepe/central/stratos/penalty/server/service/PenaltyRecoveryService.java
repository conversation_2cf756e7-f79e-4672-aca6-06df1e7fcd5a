package com.phonepe.central.stratos.penalty.server.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.recovery.PenalizedEntity;
import com.phonepe.central.stratos.penalty.recovery.impl.MerchantPenalizedEntity;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEventHeader;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEventTransaction;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEventType;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingTransactionType;
import com.phonepe.merchant.platform.stratos.server.core.clients.PlutusEventIngestionClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.penalty.PenaltyRecoverySuccessEvent;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyRecoveryService {

    private final PenaltyClassService penaltyClassService;

    private final PlutusEventIngestionClient plutusEventIngestionClient;

    private final EventIngester eventIngester;

    public void initiateRecovery(final String penaltyDisbursementId,
                                 final Penalty penalty,
                                 final Long amountToBeRecovered) {
        try {
            log.info("Initiating penalty recovery for penaltyId {} and penaltyClassId {}", penalty.getPenaltyId(),
                    penalty.getPenaltyClassId());
            PenalizedEntity penalizedEntity = penaltyClassService.getPenalizedEntity(penalty.getPenaltyClassId());
            penalizedEntity.accept(merchantPenalizedEntity -> {
                AccountingEvent accountingEvent = createAccountingEvent(merchantPenalizedEntity, penaltyDisbursementId,
                        penalty, amountToBeRecovered);
                plutusEventIngestionClient.sendAccountingEvent(accountingEvent);
                log.info("Accounting event sent successfully for penaltyId {} is {} ", penalty.getPenaltyId(),
                        accountingEvent);
                sendPenaltyRecoverySuccessEvent(merchantPenalizedEntity, penalty, penaltyDisbursementId,
                        amountToBeRecovered);
                return null;
            });
        } catch (Exception exception) {
            log.error("Exception in initiating penalty recovery for penaltyId {} and penaltyClassId {}",
                    penalty.getPenaltyId(), penalty.getPenaltyClassId(), exception);
            sendPenaltyRecoveryFailureEvent(penalty, penaltyDisbursementId, amountToBeRecovered,
                    DisputeExceptionUtil.propagate(exception));
        }
    }


    private AccountingEvent createAccountingEvent(final MerchantPenalizedEntity merchantPenalizedEntity,
                                                  final String penaltyDisbursementId,
                                                  final Penalty penalty,
                                                  final long amount) {

        return AccountingEvent.builder()
                .header(AccountingEventHeader.builder()
                        .eventType(AccountingEventType.PENALTY_RECOVERY)
                        .transactionId(penaltyDisbursementId)
                        .externalTransactionId(penalty.getTransactionId())
                        .paymentId(penalty.getTransactionId())
                        .transactionDate(System.currentTimeMillis())
                        .build())
                .transaction(AccountingEventTransaction.builder()
                        .type(AccountingTransactionType.PENALTY_RECOVERY)
                        .amount(amount)
                        .mcc(merchantPenalizedEntity.getMcc())
                        .merchant(merchantPenalizedEntity.getMerchantId())
                        .originalTransactionId(penalty.getTransactionId())
                        .build())
                .build();
    }

    private void sendPenaltyRecoveryFailureEvent(final Penalty penalty,
                                                 final String penaltyDisbursementId,
                                                 final Long amountToBeRecovered,
                                                 final DisputeException exception) {
        TenantInfo tenantInfo = fetchTenantInfo(penalty.getPenaltyClassId());
        eventIngester.generateEvent(
                FoxtrotEventUtils.getPenaltyRecoveryFailureEvent(penalty, penaltyDisbursementId, amountToBeRecovered,
                        tenantInfo, exception));
    }

    private void sendPenaltyRecoverySuccessEvent(final MerchantPenalizedEntity merchantPenalizedEntity,
                                                 final Penalty penalty,
                                                 final String penaltyDisbursementId,
                                                 final Long amountToBeRecovered) {
        PenaltyRecoverySuccessEvent penaltyRecoverySuccessEvent = createCloneOfAccountingEventForPenaltyRecovery(
                merchantPenalizedEntity, penalty, penaltyDisbursementId, amountToBeRecovered);
        eventIngester.generateEvent(penaltyRecoverySuccessEvent);
    }

    private TenantInfo fetchTenantInfo(String penaltyClassId) {
        return penaltyClassService.getTenantInfo(penaltyClassId);
    }

    private PenaltyRecoverySuccessEvent createCloneOfAccountingEventForPenaltyRecovery(final MerchantPenalizedEntity merchantPenalizedEntity,
                                                                                       final Penalty penalty,
                                                                                       final String penaltyDisbursementId,
                                                                                       final Long amountToBeRecovered) {
        TenantInfo tenantInfo = fetchTenantInfo(penalty.getPenaltyClassId());
        return PenaltyRecoverySuccessEvent.builder()
                .penaltyClassId(penalty.getPenaltyClassId())
                .penaltyId(penalty.getPenaltyId())
                .transactionId(penalty.getTransactionId())
                .disbursementId(penaltyDisbursementId)
                .amountRecovered(amountToBeRecovered)
                .tenantName(tenantInfo.getName())
                .tenantSubCategory(tenantInfo.getSubCategory())
                .mcc(merchantPenalizedEntity.getMcc())
                .merchantId(merchantPenalizedEntity.getMerchantId())
                .build();

    }

}
