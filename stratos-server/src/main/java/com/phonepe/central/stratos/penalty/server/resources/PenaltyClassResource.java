package com.phonepe.central.stratos.penalty.server.resources;


import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCreateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassSearchRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassUpdateRequest;
import com.phonepe.central.stratos.penalty.server.service.PenaltyAuthorizationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyClassService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.PenaltyPermissionsConstants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.services.warden.core.models.responses.instance.WardenWorkflowInstance;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

@Slf4j
@Path("/v1/penaltyclass")
@Tag(name = "Penalty Class Related APIs")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class PenaltyClassResource {

    private final PenaltyClassService penaltyClassService;
    private final PenaltyAuthorizationService penaltyAuthorizationService;

    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_CLASS_CREATE_PERMISSION)
    @Path("/")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Operation(summary = "Create Penalty class config for tenant and subcategory")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response createPenaltyClassConfig(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                             @HeaderParam(Constants.AUTHORIZATION) @Parameter(required = true, hidden = true) String authToken,
                                             @NotNull @Valid PenaltyClassCreateRequest penaltyClassCreateRequest) {
        try {
            log.info("Inside the class create penalty class config");
            boolean isAuthorised = penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(
                    serviceUserPrincipal.getUserAuthDetails(), penaltyClassCreateRequest.getTenant());
            if (!isAuthorised) {
                return Response.status(Status.FORBIDDEN)
                        .entity("User is not authorized to create penalty class config for tenant" + " tenant info: "
                                + penaltyClassCreateRequest.getTenant())
                        .build();
            }
            log.info("Input requests for creation of penalty class config is  {}",
                    penaltyClassCreateRequest.getTenant());
            Optional<PenaltyClass> response = penaltyClassService.createConfig(penaltyClassCreateRequest);
            if (response.isEmpty()) {
                return Response.status(Response.Status.NO_CONTENT)
                        .build();
            }
            return Response.ok(response.get())
                    .build();

        } catch (DisputeException exception) {
            log.error("Dispute Exception in creating penalty class create  request {} {}", penaltyClassCreateRequest,
                    exception);
            throw exception;
        } catch (Exception exception) {
            log.error("Exception in creating penalty class create  request {} {}", penaltyClassCreateRequest,
                    exception);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNABLE_TO_CREATE_PENALTY_CLASS);
        }

    }

    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_CLASS_VIEW_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/search")
    @Operation(summary = "Search penalty class configs")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response getPenaltyClasses(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                      @NotNull @Valid PenaltyClassSearchRequest penaltySearchRequest) {
        try {
            List<TenantInfo> permissibleTenants = penaltyAuthorizationService.getPermissibleTenants(
                    serviceUserPrincipal, penaltySearchRequest.getTenantName(),
                    penaltySearchRequest.getTenantSubCategory(),
                    PenaltyPermissionsConstants.PENALTY_CLASS_VIEW_PERMISSION);
            if (CollectionUtils.isEmpty(permissibleTenants)) {
                return Response.status(Status.FORBIDDEN)
                        .entity("User is not authorized to view penalty class config for tenant")
                        .build();
            }

            log.info("Searching for penalty class config for  search request {}", penaltySearchRequest);
            List<PenaltyClass> listOfClassConfigs = penaltyClassService.getClassConfigs(penaltySearchRequest,
                    permissibleTenants);
            return Response.ok(listOfClassConfigs)
                    .build();
        } catch (Exception exception) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PENALTY_GET_ERROR, exception);
        }
    }

    @GET
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_INSTANCE_RECONCILIATION_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/search/{tenantName}")
    @Operation(summary = "Search penalty class configs by tenant name")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response getPenaltiesByTenant(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                         @PathParam("tenantName") @NotNull final String tenantName) {
        try {
            log.info("Searching for penalty class config for tenant {}", tenantName);

            List<String> listOfPenaltyClassId = penaltyClassService.getClassIdsFor(tenantName);
            return Response.ok(listOfPenaltyClassId)
                    .build();
        } catch (Exception exception) {
            log.error("Exception in searching penalty class config for tenant {} ", tenantName, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PENALTY_GET_ERROR, exception);
        }
    }


    @PUT
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_CLASS_UPDATE_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/")
    @Operation(summary = "Update Penalty class config with Basic details")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response updatePenaltyClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                       @NotNull @Valid PenaltyClassUpdateRequest penaltyClassUpdateRequest) {
        try {
            List<TenantInfo> permissibleTenants = penaltyAuthorizationService.getPermissibleTenants(
                    serviceUserPrincipal, "", "", PenaltyPermissionsConstants.PENALTY_CLASS_VIEW_PERMISSION);
            if (CollectionUtils.isEmpty(permissibleTenants)) {
                return Response.status(Status.FORBIDDEN)
                        .entity("User is not authorized to update penalty class config for tenant")
                        .build();
            }
            log.info("Input requests are {} ", penaltyClassUpdateRequest);
            String response = penaltyClassService.updateClass(penaltyClassUpdateRequest, permissibleTenants);
            return Response.ok(response)
                    .build();
        } catch (Exception exception) {
            log.error("Exception in processing the request {} ", penaltyClassUpdateRequest, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.UNABLE_TO_UPDATE_PENALTY_CLASS, exception);
        }
    }

    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_CLASS_UPDATE_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/duplicate/{penaltyClassId}")
    @Operation(summary = "Duplicate Penalty class config with version upgrade")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response duplicatePenaltyClass(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                          @PathParam("penaltyClassId") final String penaltyClassId) {
        try {
            List<TenantInfo> permissibleTenants = penaltyAuthorizationService.getPermissibleTenants(
                    serviceUserPrincipal, "", "", PenaltyPermissionsConstants.PENALTY_CLASS_CREATE_PERMISSION);
            if (CollectionUtils.isEmpty(permissibleTenants)) {
                return Response.status(Status.FORBIDDEN)
                        .entity("User is not authorized to duplicate penalty class config for tenant")
                        .build();
            }
            log.info("Duplicating penalty class config for class id {} ", penaltyClassId);
            PenaltyClass response = penaltyClassService.duplicate(penaltyClassId);
            return Response.ok(response)
                    .build();
        } catch (Exception exception) {
            log.error("Exception in duplicating penalty ClassId  the request {} ", penaltyClassId, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.UNABLE_TO_UPDATE_PENALTY_CLASS, exception);
        }
    }


    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_CLASS_UPDATE_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/warden/config/{tenantName}/{subcategory}/{version}")
    @Operation(summary = "Add user list as a checker for penalty class config")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response createPenaltyClassConfigWorkflowConfig(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                                           @HeaderParam(Constants.AUTHORIZATION) @Parameter(required = true, hidden = true) String authToken,
                                                           @PathParam("tenantName") final String tenantName,
                                                           @PathParam("subcategory") final String subcategory,
                                                           @PathParam("version") final int version,
                                                           @NotNull @Valid List<HumanUserDetails> userList) {
        try {
            boolean isAuthorised = penaltyAuthorizationService.isTenantAuthorizedToCreatePenaltyClass(
                    serviceUserPrincipal.getUserAuthDetails(), TenantInfo.builder()
                            .name(tenantName)
                            .subCategory(subcategory)
                            .build());
            if (!isAuthorised) {
                return Response.status(Status.FORBIDDEN)
                        .entity("User is not authorized to create maker and checker config for tenant"
                                + " tenant info: " + tenantName + " subcategory: " + subcategory + " version: "
                                + version)
                        .build();
            }
            log.info("Input requests for creation of penalty class config is  {} {} {}", tenantName, subcategory,
                    version);
            List<UserDetails> userDetails = userList.stream()
                    .map(UserDetails.class::cast)
                    .collect(Collectors.toList());
            WardenWorkflowInstance wardenInstance = penaltyClassService.createWardenCallbackConfigOnboardForClass(
                    TenantInfo.builder()
                            .name(tenantName)
                            .subCategory(subcategory)
                            .build(), version, authToken, userDetails);
            return Response.ok(wardenInstance)
                    .build();

        } catch (DisputeException exception) {
            log.error("Exception in creating penalty warden class config  request {} {} {}", tenantName, subcategory,
                    version, exception);
            throw exception;
        }

    }

    @GET
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_INSTANCE_RECONCILIATION_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/tenants")
    @Operation(summary = "Search penalty class configs by tenant name")
    public Response getAllTenants(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal){
        try {
            log.info("Received request to fetch all onboarded tenants");
            Set<String> tenants = penaltyClassService.getAllOnboardedTenants();
            return Response.ok(tenants)
                    .build();
        } catch (Exception exception) {
            log.error("Exception while fetching all onboarded tenants", exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PENALTY_GET_ERROR, exception);
        }
    }

}
