package com.phonepe.central.stratos.penalty.server.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.response.PenaltyStateCheckResponse;
import com.phonepe.central.stratos.penalty.server.client.PenaltyClient;
import com.phonepe.central.stratos.penalty.server.config.client.ClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ClientPenaltyResolutionConfig.ClientPenaltyResolutionVisitor;
import com.phonepe.central.stratos.penalty.server.config.client.NoOpsClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.PassThroughProbableClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ServiceClientImplClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ServiceClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.generator.DynamicHttpExecutorBuilderFactoryGenerator;
import com.phonepe.growth.mustang.MustangEngine;
import com.phonepe.growth.mustang.common.RequestContext;
import com.phonepe.growth.mustang.criteria.Criteria;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import io.appform.functionmetrics.MonitoredFunction;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyClientService {

    private final PenaltyClassService penaltyClassService;
    private final RangerHubConfiguration rangerHubConfiguration;
    private final DynamicHttpExecutorBuilderFactoryGenerator httpExecutorBuilderFactoryGenerator;
    private final OlympusIMClient olympusIMClient;
    private final MustangEngine mustangEngine;

    private final Injector injector;

    @MonitoredFunction
    public Boolean isPenaltyResolved(final String penaltyClassId,
                                     final String transactionId) {
        log.info("Checking is penalty is contender for penalty for classId {} and transactionId {}", penaltyClassId,
                transactionId);
        PenaltyStateCheckResponse clientStatusResponse = getPenaltyResolvedStatusCheck(penaltyClassId,
                transactionId);
        if (BooleanUtils.isTrue(clientStatusResponse.getIsResolved())) {
            log.info("Penalty  is already resolved so removing it from  life state {} and transaction ID {}",
                    penaltyClassId, transactionId);
            return true;
        }
        return false;
    }

    @MonitoredFunction
    public Boolean isProbableResolved(final PenaltyProbable penaltyProbable) {
        log.info("Checking is penaltyProbable is resolved {}", penaltyProbable);
        PenaltyStateCheckResponse clientStatusResponse = getProbableResolvedStatusCheck(penaltyProbable);
        log.info("Client response for penaltyProbable {} is {}", penaltyProbable, clientStatusResponse);
        if (BooleanUtils.isTrue(clientStatusResponse.getIsResolved())) {
            log.info("Penalty probable is already resolved so removing it from  life state {}", penaltyProbable);
            return true;
        }
        return false;
    }

    private PenaltyStateCheckResponse getProbableResolvedStatusCheck(PenaltyProbable penaltyProbable) {
        ClientPenaltyResolutionConfig clientPenaltyResolutionConfig = penaltyClassService.getClientResolutionConfig(
                penaltyProbable.getPenaltyClassId());
        return clientPenaltyResolutionConfig.accept(new ClientPenaltyResolutionVisitor<>() {
            @Override
            public PenaltyStateCheckResponse visit(NoOpsClientPenaltyResolutionConfig config) {
                return getNo_ops(penaltyProbable.getTransactionId());
            }

            @Override
            public PenaltyStateCheckResponse visit(PassThroughProbableClientPenaltyResolutionConfig config) {
                return getPassThrough(penaltyProbable.getTransactionId(), Boolean.FALSE);
            }

            @Override
            public PenaltyStateCheckResponse visit(ServiceClientPenaltyResolutionConfig config) {
                return getPenaltyStateCheckServiceResponse(config, penaltyProbable.getTransactionId());
            }

            @Override
            public PenaltyStateCheckResponse visit(ServiceClientImplClientPenaltyResolutionConfig config) {
                return getPenaltyClientResolutionResponse(config, penaltyProbable.getTransactionId());
            }
        });

    }


    @MonitoredFunction
    private PenaltyStateCheckResponse getPenaltyResolvedStatusCheck(String penaltyClassId,
                                                                    String transactionId) {

        ClientPenaltyResolutionConfig clientPenaltyResolutionConfig = penaltyClassService.getClientResolutionConfig(
                penaltyClassId);
        return clientPenaltyResolutionConfig.accept(new ClientPenaltyResolutionVisitor<>() {
            @Override
            public PenaltyStateCheckResponse visit(NoOpsClientPenaltyResolutionConfig config) {
                return getNo_ops(transactionId);
            }

            @Override
            public PenaltyStateCheckResponse visit(PassThroughProbableClientPenaltyResolutionConfig config) {
                return getPassThrough(transactionId, Boolean.TRUE);
            }

            @Override
            public PenaltyStateCheckResponse visit(ServiceClientPenaltyResolutionConfig config) {
                return getPenaltyStateCheckServiceResponse(config, transactionId);
            }

            @Override
            public PenaltyStateCheckResponse visit(ServiceClientImplClientPenaltyResolutionConfig config) {
                return getPenaltyClientResolutionResponse(config, transactionId);
            }
        });

    }

    private PenaltyStateCheckResponse getPassThrough(String transactionId,
                                                     Boolean isPenaltyResolved) {
        return PenaltyStateCheckResponse.builder()
                .isResolved(isPenaltyResolved)
                .message("Pass through probable")
                .transactionId(transactionId)
                .build();
    }

    @SuppressWarnings("java:S100")
    private PenaltyStateCheckResponse getNo_ops(String transactionId) {
        return PenaltyStateCheckResponse.builder()
                .isResolved(Boolean.TRUE)
                .message("No Ops")
                .transactionId(transactionId)
                .build();
    }

    private PenaltyStateCheckResponse getPenaltyStateCheckServiceResponse(ServiceClientPenaltyResolutionConfig config,
                                                                          String transactionId) {
        HttpConfiguration httpConfiguration = HttpClientUtils.getConfig(rangerHubConfiguration,
                config.getRangerHubClientId());
        HttpExecutorBuilderFactory httpExecutorBuilderFactory = httpExecutorBuilderFactoryGenerator.generateHttpExecutorBuilderFactory(
                httpConfiguration);
        final var url = String.format(config.getApiPath(), transactionId);
        Object result = HttpClientUtils.executeGet(httpExecutorBuilderFactory, "getPenaltyStateCheckServiceResponse",
                url, new TypeReference<>() {
                }, olympusIMClient);
        log.info("Response from penalty state check service for transactionId {} is {}", transactionId, result);
        return PenaltyStateCheckResponse.builder()
                .isResolved(evaluateResponse(config.getEvaluationCriteria(), result))
                .message(String.valueOf(result))
                .transactionId(transactionId)
                .build();
    }

    private Boolean evaluateResponse(Criteria evaluationCriteria, Object clientResponse) {
        return mustangEngine.evaluate(evaluationCriteria, RequestContext.builder()
                .node(MapperUtils.convertToJsonNode(MapperUtils.convertToJsonNode(clientResponse)))
                .build());
    }

    private PenaltyStateCheckResponse getPenaltyClientResolutionResponse(ServiceClientImplClientPenaltyResolutionConfig config, String  transactionId) {
        try {
            Object penaltyClientInstance = injector.getInstance(Class.forName(config.getGuiceClientClass()));
            if(penaltyClientInstance instanceof PenaltyClient) {
                Object result = ((PenaltyClient) penaltyClientInstance).getPenaltyTransactionResponse(
                        transactionId);
                return PenaltyStateCheckResponse.builder()
                        .isResolved(evaluateResponse(config.getEvaluationCriteria(), result))
                        .message(String.valueOf(result))
                        .transactionId(transactionId)
                        .build();
            }
        } catch (Exception e) {
            log.error("Class not found for guice client class {} in penalty transactionId {}", config.getGuiceClientClass(), transactionId, e);
            throw DisputeExceptionUtil.error(e, StratosErrorCodeKey.PENALTY_CLIENT_RESOLUTION_ERROR, Map.of("Message", "Failed to get guice client instance for class " + config.getGuiceClientClass()));
        }
        return null;
    }
}

