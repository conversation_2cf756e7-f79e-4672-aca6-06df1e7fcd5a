package com.phonepe.central.stratos.penalty.server.config.disbursement.toa;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementMode;
import com.phonepe.central.stratos.penalty.server.config.disbursement.PenaltyDisbursementCategoryType;
import com.phonepe.central.stratos.penalty.server.config.disbursement.PenaltyDisbursementConfig;
import com.phonepe.models.payments.pay.context.UserTargetType;
import com.phonepe.models.payments.pay.destination.Destination;
import com.phonepe.payments.fundtransferservice.model.upi.UpiPayProfile;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonPropertyOrder({"type", "upiPayProfile", "userTargetType", "destinationType"})
public class PhonePePenaltyToADisbursementConfig extends PenaltyDisbursementConfig {

    @NotNull
    private final UpiPayProfile upiPayProfile;
    @NotNull
    private final UserTargetType userTargetType;

    @Builder
    @JsonCreator
    public PhonePePenaltyToADisbursementConfig(@JsonProperty("upiPayProfile") UpiPayProfile upiPayProfile,
                                               @JsonProperty("userTargetType") UserTargetType userTargetType,
                                               @JsonProperty("destination") Destination destination) {
        super(PenaltyDisbursementCategoryType.PHONEPE_PENALTY, PenaltyDisbursementMode.ToA, destination);
        this.upiPayProfile = upiPayProfile;
        this.userTargetType = userTargetType;
    }


    @Override
    public <T> T accept(PenaltyDisbursementConfigVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
