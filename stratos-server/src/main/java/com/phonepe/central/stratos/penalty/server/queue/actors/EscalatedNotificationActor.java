package com.phonepe.central.stratos.penalty.server.queue.actors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.central.stratos.notification.CommunicationRequest;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevel;
import com.phonepe.central.stratos.penalty.escalation.EscalationType;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.server.queue.messages.EscalatedEntityNotificationQueueMessage;
import com.phonepe.central.stratos.penalty.server.service.EscalationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyProbableService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyService;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class EscalatedNotificationActor extends Actor<ActionType, EscalatedEntityNotificationQueueMessage> {

    private final PenaltyService penaltyService;

    private final PenaltyProbableService penaltyProbableService;

    private final EscalationService escalationService;

    private final EventIngester eventIngester;

    private final ObjectMapper mapper;

    @Inject
    protected EscalatedNotificationActor(
            final Map<ActionType, ActorConfig> actorConfigMap,
            final ConnectionRegistry connectionRegistry,
            final ObjectMapper mapper,
            final RetryStrategyFactory retryStrategyFactory,
            final ExceptionHandlingFactory exceptionHandlingFactory,
            final PenaltyService penaltyService,
            final PenaltyProbableService penaltyProbableService,
            final EscalationService escalationService,
            final EventIngester eventIngester) {
        super(ActionType.ESCALATED_NOTIFICATION_HANDLER, actorConfigMap.get(ActionType.ESCALATED_NOTIFICATION_HANDLER),
                connectionRegistry, mapper, retryStrategyFactory,
                exceptionHandlingFactory, EscalatedEntityNotificationQueueMessage.class,
                Set.of(JsonProcessingException.class));
        this.penaltyService = penaltyService;
        this.penaltyProbableService = penaltyProbableService;
        this.mapper = mapper;
        this.escalationService = escalationService;
        this.eventIngester = eventIngester;
    }

    @Override
    protected boolean handle(EscalatedEntityNotificationQueueMessage escalatedEntityNotificationQueueMessage,
                             MessageMetadata messageMetadata){
        log.info("Processing escalation message for mappingId {}, escalation type {}, escalation level {} " +
                        "and number of entities {}",
                escalatedEntityNotificationQueueMessage.getMappingId(),
                escalatedEntityNotificationQueueMessage.getEscalationType(),
                escalatedEntityNotificationQueueMessage.getEscalationLevel(),
                escalatedEntityNotificationQueueMessage.getEscalatedEntities().size());
        try {
            escalatedEntityNotificationQueueMessage.getEscalationType().accept(() -> {
                handlePenaltyEscalationMessage(escalatedEntityNotificationQueueMessage);
                return null;
            });
        } catch (Exception exception){
            log.error("Error while processing EscalatedEntityNotificationMessage: {}",
                    escalatedEntityNotificationQueueMessage, exception);
            DisputeException disputeException = DisputeExceptionUtil.propagate(exception);
            sendEscalationFailureEvent(escalatedEntityNotificationQueueMessage, disputeException);
            throw disputeException;
        }
        return true;
    }

    private void handlePenaltyEscalationMessage(EscalatedEntityNotificationQueueMessage
                                                        escalatedEntityNotificationQueueMessage) {
        CommunicationRequest communicationRequest = escalatedEntityNotificationQueueMessage.getCommunicationRequest();
        DateRangeRequest dateRangeRequest = escalatedEntityNotificationQueueMessage.getDateRangeRequest();
        escalatedEntityNotificationQueueMessage.getEscalationLevel().accept(
                new EscalationLevel.EscalationLevelVisitor<>() {

            @Override
            public Void visitL1() {
                penaltyProbableService.sendNotificationForPenaltyDueShortly(getPenaltyProbablesFromQueueMessage(
                        escalatedEntityNotificationQueueMessage), communicationRequest, dateRangeRequest);
                return null;
            }

            @Override
            public Void visitL2() {
                visitL1(); // L2 notification is same as L1
                return null;
            }

            @Override
            public Void visitL3() {
                penaltyService.sendNotificationForPenaltiesCreation(getPenaltiesFromQueueMessage(
                        escalatedEntityNotificationQueueMessage), communicationRequest, dateRangeRequest);
                return null;
            }

            @Override
            public Void visitL4() {
                penaltyService.sendNotificationForPenaltiesGrowing(getPenaltiesFromQueueMessage(
                        escalatedEntityNotificationQueueMessage), communicationRequest, dateRangeRequest);
                return null;
            }
        });
    }

    private List<PenaltyProbable> getPenaltyProbablesFromQueueMessage(EscalatedEntityNotificationQueueMessage<?>
                                                              escalatedEntityNotificationQueueMessage) {
        return escalatedEntityNotificationQueueMessage.getEscalatedEntities().stream()
                .map(e -> mapper.convertValue(e, PenaltyProbable.class)).toList();
    }

    private List<Penalty> getPenaltiesFromQueueMessage(EscalatedEntityNotificationQueueMessage<?>
                                               escalatedEntityNotificationQueueMessage) {
        return escalatedEntityNotificationQueueMessage.getEscalatedEntities().stream()
                .map(e -> mapper.convertValue(e, Penalty.class)).toList();
    }

    private void sendEscalationFailureEvent(EscalatedEntityNotificationQueueMessage escalatedEntityNotificationQueueMessage,
                                            DisputeException e) {
        TenantInfo tenantInfo = fetchTenantInfo(escalatedEntityNotificationQueueMessage);
        eventIngester.generateEvent(
                FoxtrotEventUtils.getEscalationFailureEvent(tenantInfo, escalatedEntityNotificationQueueMessage, e));
    }

    private TenantInfo fetchTenantInfo(EscalatedEntityNotificationQueueMessage escalatedEntityNotificationQueueMessage) {
        return escalationService.getEscalations(escalatedEntityNotificationQueueMessage.getMappingId(),
                        escalatedEntityNotificationQueueMessage.getEscalationLevel())
                .getTenantInfo();
    }
}
