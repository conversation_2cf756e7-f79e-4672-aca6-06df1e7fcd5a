package com.phonepe.central.stratos.penalty.server.config.disbursement;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementMode;
import com.phonepe.central.stratos.penalty.server.config.disbursement.toa.MerchantPenaltyToADisbursementConfig;
import com.phonepe.central.stratos.penalty.server.config.disbursement.toa.PhonePePenaltyToADisbursementConfig;
import com.phonepe.models.payments.pay.destination.Destination;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(name = PenaltyDisbursementCategoryType.MERCHANT_PENALTY_TEXT, value = MerchantPenaltyToADisbursementConfig.class),
        @JsonSubTypes.Type(name = PenaltyDisbursementCategoryType.PHONEPE_PENALTY_TEXT, value = PhonePePenaltyToADisbursementConfig.class),})
public abstract class PenaltyDisbursementConfig {

    @NotNull
    private final PenaltyDisbursementCategoryType type;
    @NotNull
    private final PenaltyDisbursementMode disbursementMode;

    private final Destination destination;


    public abstract <T> T accept(PenaltyDisbursementConfigVisitor<T> visitor);

    public interface PenaltyDisbursementConfigVisitor<T> {

        T visit(final MerchantPenaltyToADisbursementConfig config);

        T visit(final PhonePePenaltyToADisbursementConfig config);
    }

}
