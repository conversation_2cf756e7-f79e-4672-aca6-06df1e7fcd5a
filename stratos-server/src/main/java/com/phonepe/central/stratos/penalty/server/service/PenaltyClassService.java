package com.phonepe.central.stratos.penalty.server.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.escalation.EscalationMatrix;
import com.phonepe.central.stratos.penalty.escalation.EscalationType;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.recovery.PenalizedEntity;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassBasicUpdateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCreateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCriteriaUpdateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassSearchRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassUpdateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassUpdateRequest.PenaltyClassUpdateRequestVisitor;
import com.phonepe.central.stratos.penalty.response.EscalationResponse;
import com.phonepe.central.stratos.penalty.server.config.PenaltyTenantVersionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.disbursement.PenaltyDisbursementConfig;
import com.phonepe.central.stratos.penalty.server.convertor.EscalationConvertors;
import com.phonepe.central.stratos.penalty.server.convertor.PenaltyClassConvertors;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassDetailEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyClassDetailRepository;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyClassRepository;
import com.phonepe.central.stratos.penalty.server.util.PenaltyConvertorUtils;
import com.phonepe.central.stratos.penalty.server.util.PenaltyDBUtils;
import com.phonepe.central.stratos.penalty.server.util.PenaltyValidationUtil;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.services.WardenService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.services.warden.core.models.responses.config.WardenWorkflowConfig;
import com.phonepe.services.warden.core.models.responses.instance.WardenWorkflowInstance;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyClassService {

    private final PenaltyClassRepository penaltyClassRepository;

    private final PenaltyClassDetailRepository penaltyClassDetailRepository;

    private final EscalationService escalationService;

    private final Map<String, PenaltyTenantVersionConfig> penaltyValidationConfigsMap;

    private final WardenService wardenService;

    private final EventIngester eventIngester;

    public List<PenaltyClass> getClassConfigs(PenaltyClassSearchRequest searchRequest,
                                              List<TenantInfo> tenantInfoList) {
        List<PenaltyClassEntity> entities = penaltyClassRepository.getClassConfigs(searchRequest, tenantInfoList);
        return entities.stream()
                .map(penaltyClassEntity -> {
                    List<PenaltyClassDetail> details = this.getClassDetail(penaltyClassEntity.getPenaltyClassId());
                    List<EscalationResponse> escalationList = escalationService.getEscalations(
                            penaltyClassEntity.getPenaltyClassId());
                    EscalationMatrix escalationMatrix = EscalationMatrix.builder()
                            .escalationType(EscalationType.PENALTY)
                            .levelConfig(escalationList.stream()
                                    .map(EscalationConvertors::convert)
                                    .collect(Collectors.toSet()))
                            .build();
                    PenaltyClass penaltyClass = PenaltyConvertorUtils.classEntityToPenaltyClass(penaltyClassEntity,
                            details);
                    penaltyClass.setEscalationMatrix(escalationMatrix);
                    return penaltyClass;
                })
                .collect(Collectors.toList());
    }


    public List<PenaltyClassDetail> getClassDetail(String penaltyClassId) {
        List<PenaltyClassDetailEntity> classDetails = penaltyClassDetailRepository.getClassDetailsConfig(
                penaltyClassId);
        return classDetails.stream()
                .map(PenaltyConvertorUtils::classDetailEntityToPenaltyClassDetails)
                .collect(Collectors.toList());

    }

    public List<String> getClassIdsFor(String tenantName) {
        List<PenaltyClassEntity> entities = penaltyClassRepository.getClassConfigsForTenant(tenantName);
        return entities.stream()
                .map(PenaltyClassEntity::getPenaltyClassId)
                .collect(Collectors.toList());
    }

    public TenantInfo getTenantInfo(final String penaltyClassId) {
        Optional<PenaltyClass> classOptional = this.getClassFor(penaltyClassId);
        return classOptional.map(PenaltyClass::getTenant)
                .orElseThrow(() -> DisputeExceptionUtil.propagate(new RuntimeException(
                        "Tenant info is not present for class with classId  : " + penaltyClassId)));
    }

    public Optional<PenaltyClass> getClassFor(String penaltyClassId) {
        Optional<PenaltyClassEntity> entityOptional = penaltyClassRepository.getClassConfig(penaltyClassId);
        return entityOptional.map(entity -> {
            PenaltyClass result = PenaltyConvertorUtils.classEntityToPenaltyClass(entity,
                    this.getClassDetail(penaltyClassId));
            EscalationMatrix escalationMatrix = escalationService.getEscalationMatrix(entity.getPenaltyClassId());
            result.setEscalationMatrix(escalationMatrix);
            return result;
        });
    }

    public Optional<PenaltyClass> createConfig(final PenaltyClassCreateRequest penaltyClassCreateRequest) {
        PenaltyClassEntity entity = PenaltyConvertorUtils.classCreatRequestToClassEntity(penaltyClassCreateRequest);
        PenaltyClass penaltyClass = PenaltyConvertorUtils.classEntityToPenaltyClass(entity);
        try {
            if (entity != null) {
                validateIfTenantHasSameNamedPenaltyClass(penaltyClassCreateRequest);
                List<PenaltyClass> classConfigs = this.getClassConfigs(entity.getTenant_name(),
                        entity.getTenant_subcategory_name());
                entity.setVersion(PenaltyDBUtils.createNewClassVersion(classConfigs));
                Optional<PenaltyClassEntity> entityOptional = penaltyClassRepository.save(entity, storeEntity -> {
                    storePenaltyClassDetails(penaltyClassCreateRequest.getDetails(), storeEntity.getPenaltyClassId());
                    escalationService.createEscalation(penaltyClassCreateRequest.getTenant(),
                            penaltyClassCreateRequest.getEscalationMatrix(), storeEntity.getPenaltyClassId());
                    return storeEntity;
                });
                if (entityOptional.isPresent()) {
                    penaltyClass = PenaltyConvertorUtils.classEntityToPenaltyClass(entityOptional.get());
                    sendPenaltyClassSuccessEvent(penaltyClass);
                    return Optional.of(penaltyClass);
                }
            }
        } catch (Exception exception) {
            DisputeException disputeException = DisputeExceptionUtil.propagate(exception);
            sendPenaltyClassFailureEvent(penaltyClass, disputeException);
            throw disputeException;
        }
        return Optional.empty();
    }

    private boolean validateIfTenantHasSameNamedPenaltyClass(PenaltyClassCreateRequest penaltyClassCreateRequest) {
        List<PenaltyClass> listOfTenantClasses = this.getClassConfigs(penaltyClassCreateRequest.getTenant()
                .getName(), penaltyClassCreateRequest.getTenant()
                .getSubCategory());
        return listOfTenantClasses.stream()
                .anyMatch(penaltyClass -> {
                    if (penaltyClass.getName()
                            .equalsIgnoreCase(penaltyClassCreateRequest.getName())) {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_NAME_ALREADY_EXISTS);
                    }
                    return false;
                });
    }


    public String updateClass(PenaltyClassUpdateRequest penaltyClassUpdateRequest,
                              List<TenantInfo> tenantInfoList) {
        try {
            Optional<PenaltyClassEntity> entityOptional = penaltyClassRepository.getClassConfig(
                    penaltyClassUpdateRequest.getPenaltyClassId(), tenantInfoList);
            return entityOptional.map(existingStoredEntity -> penaltyClassUpdateRequest.accept(
                            new PenaltyClassUpdateRequestVisitor<String>() {
                                final PenaltyClass penaltyClass = PenaltyConvertorUtils.classEntityToPenaltyClass(
                                        existingStoredEntity);

                                @Override
                                public String visit(PenaltyClassBasicUpdateRequest updateRequest) {
                                    if (updateRequest.getState() != null && existingStoredEntity.isActive()) {
                                        PenaltyValidationUtil.isClassEligibleToChangeStage(
                                                existingStoredEntity.getPenaltyClassId());
                                    }
                                    PenaltyClassEntity entity = PenaltyConvertorUtils.classUpdateBasicRequestToEntity(
                                            existingStoredEntity, updateRequest);
                                    Optional<PenaltyClassEntity> resultEntity = penaltyClassRepository.save(entity);
                                    return resultEntity.map(PenaltyClassEntity::getPenaltyClassId)
                                            .orElseThrow(() -> new RuntimeException("Unable to update penalty class"));
                                }

                                @Override
                                public String visit(PenaltyClassCriteriaUpdateRequest updateRequest) {
                                    PenaltyValidationUtil.validateForActiveCriteriaUpdate(existingStoredEntity);
                                    List<PenaltyClassDetailEntity> dbDetails = penaltyClassDetailRepository.getClassDetailsConfig(
                                            updateRequest.getPenaltyClassId());
                                    for (int i = 0; i < updateRequest.getDetails().size(); i++) {
                                        if (!dbDetails.isEmpty() && i < dbDetails.size()) {
                                            updateDBDetail(updateRequest, dbDetails, i);
                                        } else {
                                            storePenaltyClassDetails(updateRequest.getDetails()
                                                    .get(i));
                                        }
                                    }

                                    return updateRequest.getPenaltyClassId();
                                }
                            }))
                    .orElseThrow(() -> new RuntimeException("Penalty entity is not present to updated"));
        } catch (Exception exception) {
            DisputeException disputeException = DisputeExceptionUtil.error(exception,
                    StratosErrorCodeKey.UNABLE_TO_UPDATE_PENALTY_CLASS);
            sendPenaltyClassUpdateFailureEvent(penaltyClassUpdateRequest, disputeException);
            throw disputeException;
        }
    }

    private void storePenaltyClassDetails(PenaltyClassDetail source) {
        PenaltyClassDetailEntity entity = PenaltyClassConvertors.convert(source);
        penaltyClassDetailRepository.save(entity);
    }

    private void updateDBDetail(PenaltyClassCriteriaUpdateRequest updateRequest,
                           List<PenaltyClassDetailEntity> dbDetails,
                           int index) {
        PenaltyClassDetail detail = updateRequest.getDetails()
                .get(index);
        penaltyClassDetailRepository.updateCriteria(updateRequest.getPenaltyClassId(),
                dbDetails.get(index).getId(), detail.getLabel(),
                detail.getPenaltyCap(), detail.getCriteria(), detail.getGrowthRate());
    }

    private Optional<PenaltyTenantVersionConfig> getPenaltyTenantVersionConfig(String penaltyClassId) {
        return getClassFor(penaltyClassId).map(classConfig -> {
                    String key = classConfig.getTenant()
                            .getName() + ":" + classConfig.getTenant()
                            .getSubCategory() + ":" + classConfig.getVersion();
                    return Optional.ofNullable(penaltyValidationConfigsMap.get(key));
                })
                .orElseThrow(() -> new RuntimeException("Unable to get class config for classId : " + penaltyClassId));
    }

    public ClientPenaltyResolutionConfig getClientResolutionConfig(String penaltyClassId) {
        return getPenaltyTenantVersionConfig(penaltyClassId).map(
                        PenaltyTenantVersionConfig::getClientPenaltyResolutionConfig)
                .orElseThrow(() -> DisputeExceptionUtil.error(
                        StratosErrorCodeKey.PENALTY_CLASS_CLIENT_RESOLUTION_CONFIG_NOT_FOUND,
                        "Client Resolution Config is not present for class with classId  : " + penaltyClassId));
    }

    public PenaltyDisbursementConfig getClassDisbursementConfig(final String penaltyClassId) {
        return getPenaltyTenantVersionConfig(penaltyClassId).map(PenaltyTenantVersionConfig::getDisbursementConfig)
                .orElseThrow(() -> new RuntimeException(
                        "Client Disbursement Config is not present for class with classId  : " + penaltyClassId));
    }

    public PenalizedEntity getPenalizedEntity(final String penaltyClassId) {
        return getPenaltyTenantVersionConfig(penaltyClassId).map(PenaltyTenantVersionConfig::getPenaltyRecoveryConfig)
                .orElseThrow(() -> new RuntimeException(
                        "Recovery Config is not present for class with classId  : " + penaltyClassId));
    }

    public Set<String> getAllOnboardedTenants() {
        return penaltyClassRepository.selectAll().stream()
                .map(PenaltyClassEntity::getTenant_name)
                .collect(Collectors.toSet());
    }

    private List<PenaltyClass> getClassConfigs(String tenantName,
                                               String tenantSubCategory) {
        PenaltyClassSearchRequest searchRequest = PenaltyClassSearchRequest.builder()
                .tenantName(tenantName)
                .tenantSubCategory(tenantSubCategory)
                .build();
        List<PenaltyClassEntity> entities = penaltyClassRepository.getClassConfigs(searchRequest,
                List.of(TenantInfo.builder()
                        .name(tenantName)
                        .subCategory(tenantSubCategory)
                        .build()));
        return entities.stream()
                .map(penaltyClassEntity -> {
                    List<PenaltyClassDetail> details = this.getClassDetail(penaltyClassEntity.getPenaltyClassId());
                    return PenaltyConvertorUtils.classEntityToPenaltyClass(penaltyClassEntity, details);
                })
                .collect(Collectors.toList());
    }

    private void storePenaltyClassDetails(List<PenaltyClassDetail> details,
                                          String penaltyClassId) {
        details.forEach(detail -> {
            PenaltyClassDetailEntity penaltyClassDetailEntity = PenaltyConvertorUtils.classCreatRequestToClassDetailEntity(
                    penaltyClassId, detail);
            penaltyClassDetailRepository.save(penaltyClassDetailEntity);
        });
    }

    public List<PenaltyClassDetail> getClassDetailAbovePenaltyCap(final String penaltyClassId,
                                                                  final long amount) {
        List<PenaltyClassDetail> eligibleDetails = penaltyClassDetailRepository.getClassDetailsConfig(penaltyClassId,
                        amount)
                .stream()
                .map(PenaltyConvertorUtils::classDetailEntityToPenaltyClassDetails)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eligibleDetails)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_CRITERIA_NOT_MET,
                    "Penalty amount is not eligible for cap" + " for classId : " + penaltyClassId);
        }
        return eligibleDetails;

    }

    public void activatePenaltyClass(final PenaltyClass penaltyClass) {
        try {
            penaltyClassRepository.activatePenaltyClass(penaltyClass.getId());
            log.info("Penalty class activated successfully : {} ", penaltyClass.getId());
            sendPenaltyClassSuccessEvent(penaltyClass);
        } catch (Exception exception) {
            log.error("Exception while activating penalty class : {} ", penaltyClass.getName(), exception);
            DisputeException disputeException = DisputeExceptionUtil.propagate(exception);
            sendPenaltyClassFailureEvent(penaltyClass, disputeException);
            throw disputeException;
        }
    }

    public WardenWorkflowInstance createWardenCallbackConfigOnboardForClass(final TenantInfo tenantInfo,
                                                                            final Integer version,
                                                                            String authToken,
                                                                            List<UserDetails> userDetails) {
        String workflowType =
                "PENALTY_CLASS_CREATION" + "_" + tenantInfo.getName() + "_" + tenantInfo.getSubCategory() + "_"
                        + version;
        WardenWorkflowConfig wardenWorkflowConfig = wardenService.createNewWardenWorkflowConfig(workflowType, authToken,
                userDetails);
        List<PenaltyClass> penaltyClassList = this.getClassConfigs(PenaltyClassSearchRequest.builder()
                .version(version)
                .build(), List.of(tenantInfo));
        WardenWorkflowInstance wardenInstance = wardenService.raisePenaltyClassCreation(penaltyClassList.get(0),
                workflowType, authToken);
        log.info("Warden instance is created with config : {} and instance : {}", wardenWorkflowConfig, wardenInstance);
        return wardenInstance;
    }

    public PenaltyClass duplicate(final String penaltyClassId) {
        Optional<PenaltyClass> classOptional = this.getClassFor(penaltyClassId);
        if (classOptional.isPresent()) {
            Optional<PenaltyClass> newClassConfig = this.createConfig(PenaltyClassCreateRequest.builder()
                    .tenant(classOptional.get()
                            .getTenant())
                    .name(classOptional.get()
                            .getName() + "_copy")
                    .description(classOptional.get()
                            .getDescription())
                    .details(classOptional.get()
                            .getDetails())
                    .escalationMatrix(classOptional.get()
                            .getEscalationMatrix())
                    .build());
            return newClassConfig.orElseThrow(
                    () -> DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_DUPLICATION_FAILED,
                            "Unable to duplicate penalty class with classId : " + penaltyClassId));
        }
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_NOT_FOUND,
                "Penalty class not found for classId : " + penaltyClassId);
    }

    private void sendPenaltyClassUpdateFailureEvent(PenaltyClassUpdateRequest request,
                                                    DisputeException exception) {
        PenaltyClass penaltyClass = getClassFor(request.getPenaltyClassId()).orElseThrow(
                () -> DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_CLASS_NOT_FOUND,
                        "Penalty class not found for classId : " + request.getPenaltyClassId()));
        sendPenaltyClassFailureEvent(penaltyClass, exception);
    }

    private void sendPenaltyClassFailureEvent(PenaltyClass penaltyClass,
                                              DisputeException disputeException) {
        eventIngester.generateEvent(FoxtrotEventUtils.getPenaltyClassFailureEvent(penaltyClass, disputeException));
    }

    private void sendPenaltyClassSuccessEvent(PenaltyClass penaltyClass) {
        eventIngester.generateEvent(FoxtrotEventUtils.getPenaltyClassSuccessEvent(penaltyClass));
    }
}
