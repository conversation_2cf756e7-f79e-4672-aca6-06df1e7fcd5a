package com.phonepe.central.stratos.penalty.server.mariadb.repository;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyDisbursementEntity;
import com.phonepe.central.stratos.penalty.server.util.PenaltyDBUtils;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.RelationalDaoCrudRepository;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
public class PenaltyDisbursementRepository extends RelationalDaoCrudRepository<PenaltyDisbursementEntity> {

    private static final Integer MAX_PAGE_SIZE = 2000;


    @Inject
    public PenaltyDisbursementRepository(RelationalDao<PenaltyDisbursementEntity> relationalDao) {
        super(relationalDao);
    }

    public Optional<PenaltyDisbursementEntity> getDisbursementForPenaltyId(String penaltyClassId,
                                                                           String penaltyId) {
        try {
            final var detachedCriteria = PenaltyDBUtils.getPenaltyDisbursementCriteria(penaltyClassId)
                    .add(Restrictions.eq(PenaltyDisbursementEntity.Fields.penaltyId, penaltyId));
            return this.relationalDao.select(String.valueOf(penaltyClassId), detachedCriteria, 0, MAX_PAGE_SIZE)
                    .stream()
                    .findFirst();
        } catch (Exception exception) {
            log.error("Exception in getting the disbursement  for mappingId {} penaltyID {}", penaltyClassId, penaltyId,
                    exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }
    }

    public Optional<PenaltyDisbursementEntity> getDisbursementForTransactionId(String penaltyClassId,
                                                                           String transactionId) {
        try {
            final var detachedCriteria = PenaltyDBUtils.getPenaltyDisbursementCriteria(penaltyClassId)
                    .add(Restrictions.eq(PenaltyDisbursementEntity.Fields.transactionId, transactionId));
            return this.relationalDao.select(String.valueOf(penaltyClassId), detachedCriteria, 0, MAX_PAGE_SIZE)
                    .stream()
                    .findFirst();
        } catch (Exception exception) {
            log.error("Exception in getting the disbursement  for mappingId {} penaltyID {}", penaltyClassId, transactionId,
                    exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }
    }
}
