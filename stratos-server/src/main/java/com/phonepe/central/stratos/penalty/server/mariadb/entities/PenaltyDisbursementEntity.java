package com.phonepe.central.stratos.penalty.server.mariadb.entities;

import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementMode;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementState;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "penalty_disbursements")
@FieldNameConstants
public class PenaltyDisbursementEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotEmpty
    @Column(name = "penalty_class_id", nullable = false)
    private String penaltyClassId;

    @NotEmpty
    @Column(name = "penalty_id", nullable = false)
    private String penaltyId;

    @NotEmpty
    @Column(name = "beneficiary_id", nullable = false)
    private String beneficiaryId;

    @NotEmpty
    @Column(name = "transaction_id", nullable = false)
    private String transactionId;

    @NotEmpty
    @Column(name = "disbursement_id", nullable = false)
    private String disbursementId;

    @Column(name = "disbursement_txn_id", nullable = false)
    private String disbursementTransactionId;


    @NotNull
    @Column(name = "disbursement_amount", nullable = false)
    private Long disbursementAmount;


    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PenaltyDisbursementState status;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "disbursement_mode", nullable = false)
    private PenaltyDisbursementMode disbursementMode;

    @Column(name = "partition_id", nullable = false, updatable = true)
    private int partitionId;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;

    @Override
    public String getKey() {
        return getPenaltyClassId();
    }

    public boolean isDisbursementCompleted() {
        return status != null && status == PenaltyDisbursementState.COMPLETED;
    }
}
