package com.phonepe.central.stratos.penalty.server.mariadb.entities;

import com.phonepe.central.stratos.penalty.meta.PenaltyClassState;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "penalty_class")
@FieldNameConstants
@SuppressWarnings("java:S116")
public class PenaltyClassEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotNull
    @Column(name = "penalty_class_id", nullable = false)
    private String penaltyClassId;

    @NotEmpty
    @Column(name = "name", nullable = false)
    private String name;

    @NotEmpty
    @Column(name = "description", nullable = false)
    private String description;

    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version = 1;


    @NotEmpty
    @Column(name = "tenant_name", updatable = false, nullable = false)
    private String tenant_name;

    @NotEmpty
    @Column(name = "tenant_subcategory_name", updatable = false, nullable = false)
    private String tenant_subcategory_name;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private PenaltyClassState state;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;

    @Override
    public String getKey() {
        return getPenaltyClassId();
    }
    public boolean isActive() {
        return state.equals(PenaltyClassState.ACTIVE);
    }
}
