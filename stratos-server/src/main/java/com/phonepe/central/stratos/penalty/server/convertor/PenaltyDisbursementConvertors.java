package com.phonepe.central.stratos.penalty.server.convertor;

import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursement;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementState;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyDisbursementEntity;
import com.phonepe.central.stratos.penalty.server.util.PenaltyDBUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.Date;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
@UtilityClass
public class PenaltyDisbursementConvertors {

    public static final String PENALTY_DISBURSEMENT_ID_PREFIX = "PD";

    public PenaltyDisbursementEntity convert(Penalty source) {
        if (source != null) {
            return PenaltyDisbursementEntity.builder()
                    .disbursementAmount(source.getFinalPenaltyAmount())
                    .disbursementId(IdGenerator.generate(PENALTY_DISBURSEMENT_ID_PREFIX)
                            .getId())
                    .penaltyClassId(source.getPenaltyClassId())
                    .beneficiaryId(source.getBeneficiary()
                            .getId())
                    .penaltyId(source.getPenaltyId())
                    .transactionId(source.getTransactionId())
                    .status(PenaltyDisbursementState.CREATED)
                    .partitionId(PenaltyDBUtils.getMonthBasedPartition(new Date()))
                    .build();
        }
        return null;
    }

    public PenaltyDisbursement convert(PenaltyDisbursementEntity source) {
        if (source != null) {
            return PenaltyDisbursement.builder()
                    .disbursementId(source.getDisbursementId())
                    .beneficiaryId(source.getBeneficiaryId())
                    .disbursedAmount(source.getDisbursementAmount())
                    .disbursementMode(source.getDisbursementMode())
                    .disbursementTransactionId(source.getDisbursementTransactionId())
                    .penaltyId(source.getPenaltyId())
                    .penaltyClassId(source.getPenaltyClassId())
                    .status(source.getStatus())
                    .transactionId(source.getTransactionId())
                    .build();
        }
        return null;
    }
}
