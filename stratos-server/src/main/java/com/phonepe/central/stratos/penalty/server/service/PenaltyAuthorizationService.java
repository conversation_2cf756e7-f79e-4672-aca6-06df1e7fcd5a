package com.phonepe.central.stratos.penalty.server.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.merchant.platform.stratos.server.core.utils.PenaltyPermissionsConstants;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.authz.enums.TenantType;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyAuthorizationService {

    public static final String CIG_STRATOS_PENALTY = "CIG_STRATOS_CIG_PENALTY";
    private final OlympusIMClient olympusIMClient;

    private final PenaltyClassService penaltyClassService;

    public static final String DELIMITER = "_";


    public List<TenantInfo> getAuthorizedTenantInfo(final UserAuthDetails userAuthDetails,
                                                    final String permission) {
        final String componentInstanceId = olympusIMClient.getOlympusIMConfig()
                .getAuthConfig()
                .getComponentInstanceId();
        Map<String, Map<TenantType, Map<String, String>>> encodedTenantPermission = userAuthDetails.getEncodedTenantPermissionsForComponentInstances();
        if (MapUtils.isNotEmpty(encodedTenantPermission) && encodedTenantPermission.containsKey(componentInstanceId)
                && encodedTenantPermission.get(componentInstanceId)
                .containsKey(TenantType.CUSTOM)) {
            Map<String, String> customPermission = encodedTenantPermission.get(componentInstanceId)
                    .get(TenantType.CUSTOM);
            return customPermission.keySet()
                    .stream()
                    .filter(s -> s.contains(CIG_STRATOS_PENALTY))
                    .filter(s -> olympusIMClient.verifyPermissionForTenant(userAuthDetails, s, TenantType.CUSTOM,
                            permission))
                    .map(s -> s.replace(CIG_STRATOS_PENALTY + DELIMITER, ""))
                    .filter(StringUtils::isNotBlank)
                    .filter(tenantInfoString -> {
                        String[] tenantInfoArray = tenantInfoString.split(DELIMITER);
                        return tenantInfoArray.length == 2;
                    })
                    .map(tenantInfoString -> {
                        String[] tenantInfoArray = tenantInfoString.split(DELIMITER);
                        return TenantInfo.builder()
                                .name(tenantInfoArray[0])
                                .subCategory(tenantInfoArray[1])
                                .build();
                    })
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public boolean isTenantAuthorizedToCreatePenaltyClass(final UserAuthDetails userAuthDetails,
                                                          final TenantInfo tenantInfo) {
        List<TenantInfo> tenantInfoList = getAuthorizedTenantInfo(userAuthDetails,
                PenaltyPermissionsConstants.PENALTY_CLASS_CREATE_PERMISSION);
        return !tenantInfoList.isEmpty() && tenantInfoList.stream()
                .anyMatch(tenant -> tenant.equals(tenantInfo));
    }

    public boolean isTenantAuthorizedToRegisterProbable(final UserAuthDetails userAuthDetails,
                                                        final String penaltyClassId) {
        List<TenantInfo> tenantInfoList = getAuthorizedTenantInfo(userAuthDetails,
                PenaltyPermissionsConstants.PENALTY_PROBABLE_CREATE_PERMISSION);
        return !tenantInfoList.isEmpty() && tenantInfoList.stream()
                .anyMatch(tenant -> tenant.equals(penaltyClassService.getTenantInfo(penaltyClassId)));
    }

    public boolean isTenantAuthorizedToViewProbable(final UserAuthDetails userAuthDetails,
                                                    final String penaltyClassId) {
        List<TenantInfo> tenantInfoList = getAuthorizedTenantInfo(userAuthDetails,
                PenaltyPermissionsConstants.PENALTY_PROBABLE_VIEW_PERMISSION);
        return !tenantInfoList.isEmpty() && tenantInfoList.stream()
                .anyMatch(tenant -> tenant.equals(penaltyClassService.getTenantInfo(penaltyClassId)));
    }

    public boolean isTenantAuthorizedToViewPenaltyInstance(final UserAuthDetails userAuthDetails,
                                                           final String penaltyClassId) {
        List<TenantInfo> tenantInfoList = getAuthorizedTenantInfo(userAuthDetails,
                PenaltyPermissionsConstants.PENALTY_INSTANCE_VIEW_PERMISSION);
        return !tenantInfoList.isEmpty() && tenantInfoList.stream()
                .anyMatch(tenant -> tenant.equals(penaltyClassService.getTenantInfo(penaltyClassId)));
    }

    public List<TenantInfo> getPermissibleTenants(ServiceUserPrincipal serviceUserPrincipal,
                                                  String tenantName,
                                                  String subCategory,
                                                  String permission) {
        List<TenantInfo> listOfTenants = this.getAuthorizedTenantInfo(serviceUserPrincipal.getUserAuthDetails(),
                permission);
        return listOfTenants.stream()
                .filter(tenantInfo -> {
                    boolean isMatched = true;
                    if (StringUtils.isNotBlank(tenantName)) {
                        isMatched = tenantInfo.getName()
                                .equalsIgnoreCase(tenantName);
                    }
                    if (StringUtils.isNotBlank(subCategory)) {
                        isMatched = isMatched && tenantInfo.getSubCategory()
                                .equalsIgnoreCase(subCategory);
                    }
                    return isMatched;
                })
                .collect(Collectors.toList());
    }
}
