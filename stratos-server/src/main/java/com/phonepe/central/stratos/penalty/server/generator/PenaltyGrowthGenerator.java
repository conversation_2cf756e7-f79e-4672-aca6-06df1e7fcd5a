package com.phonepe.central.stratos.penalty.server.generator;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.penalty.growth.GrowthUnit;
import com.phonepe.central.stratos.penalty.growth.GrowthUnit.Visitor;
import com.phonepe.central.stratos.penalty.growth.calculation.Calculation;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.request.TransactionContext;
import com.phonepe.central.stratos.penalty.server.calculation.factory.PenaltyCalculationStrategyFactory;
import com.phonepe.central.stratos.penalty.server.calculation.strategy.PenaltyCalculationStrategy;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity;
import com.phonepe.central.stratos.penalty.server.service.PenaltyClassService;
import com.phonepe.central.stratos.penalty.server.util.PenaltyEvaluatorUtil;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.OptionalLong;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PenaltyGrowthGenerator {

    private final PenaltyClassService penaltyClassService;

    private final PenaltyCalculationStrategyFactory penaltyCalculationStrategyFactory;

    public Long generateGrowthAmount(PenaltyEntity penaltyEntity) {
        log.info("Generating the growth amount for penalty with penaltyEntity {}", penaltyEntity);
        List<PenaltyClassDetail> classDetails = penaltyClassService.getClassDetail(penaltyEntity.getPenaltyClassId());
        return classDetails.stream()
                .map(penaltyClassDetail -> {
                    Long calculatedPenaltyAmount = Math.max(penaltyEntity.getInitialPenaltyAmount()
                            ,getPenaltyAmount(penaltyEntity, penaltyClassDetail));
                    return penaltyClassDetail.getPenaltyCap() > 0
                           ? Math.min(calculatedPenaltyAmount, penaltyClassDetail.getPenaltyCap())
                           : calculatedPenaltyAmount;
                })
                .mapToLong(Long::longValue)
                .min()
                .orElse(0L);
    }

    public Date generatePenaltyScheduleTime(PenaltyEntity penaltyEntity) {
        log.info("Generating the growth amount for penalty with penaltyEntity {}", penaltyEntity);
        List<PenaltyClassDetail> classDetails = penaltyClassService.getClassDetail(penaltyEntity.getPenaltyClassId());
        OptionalLong minimalTime = classDetails.stream()
                .map(penaltyClassDetail -> getScheduleTime(penaltyEntity.getUpdatedAt(), penaltyEntity.getCreatedAt(),
                        penaltyClassDetail))
                .mapToLong(Long::longValue)
                .min();
        return minimalTime.stream()
                .mapToObj(Date::new)
                .findAny()
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.UNABLE_TO_GET_PENALTY_SCHEDULE_TIME));
    }

    private Long getScheduleTime(final LocalDateTime updatedAt,
                                 final LocalDateTime createdAt,
                                 final PenaltyClassDetail penaltyClassDetail) {

        LocalDateTime penaltyGrowthStartTime = createdAt.plus(penaltyClassDetail.getCriteria()
                .getLeeway());
        if (PenaltyUtil.isGreaterThanOrEqual(penaltyGrowthStartTime, updatedAt) || PenaltyUtil.isGreaterThanOrEqual(
                penaltyGrowthStartTime, LocalDateTime.now())) {
            return getScheduleTimeForGrowthUnit(updatedAt, penaltyClassDetail.getGrowthRate()
                    .getUnit());
        } else {
            return penaltyGrowthStartTime.atZone(ZoneId.systemDefault())
                    .toEpochSecond();
        }
    }

    private Long getScheduleTimeForGrowthUnit(LocalDateTime updatedAt,
                                              GrowthUnit growthUnit) {
        return growthUnit.accept(new Visitor<>() {
            @Override
            public Long visitDay() {
                return updatedAt.plusDays(1)
                        .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            }

            @Override
            public Long visitWeek() {
                return updatedAt.plusWeeks(1)
                        .atZone(ZoneId.systemDefault())
                        .toInstant().toEpochMilli();
            }

            @Override
            public Long visitMonth() {
                return updatedAt.plusMonths(1)
                        .atZone(ZoneId.systemDefault())
                        .toInstant().toEpochMilli();
            }

            @Override
            public Long visitYear() {
                return updatedAt.plusYears(1)
                        .atZone(ZoneId.systemDefault())
                        .toInstant().toEpochMilli();
            }
        });
    }

    private Long getPenaltyAmount(PenaltyEntity penaltyEntity,
                                  PenaltyClassDetail penaltyClassDetail) {
        TransactionContext transactionContext = TransactionContext.builder()
                .transactionAmount(penaltyEntity.getTransactionAmount())
                .transactionId(penaltyEntity.getTransactionId())
                .build();
        Long finalAmount = penaltyEntity.getFinalPenaltyAmount()==null ? 0L : penaltyEntity.getFinalPenaltyAmount();
        if (PenaltyEvaluatorUtil.evaluateTriggerCriteria(transactionContext, penaltyClassDetail.getCriteria()
                .getTrigger())) {
            Calculation calculation = penaltyClassDetail.getGrowthRate()
                    .getCalculation();
            PenaltyCalculationStrategy calculationStrategy = penaltyCalculationStrategyFactory.getStrategy(calculation);
            return calculationStrategy.calculate(penaltyClassDetail, penaltyEntity, calculation);
        }
        return finalAmount;
    }
}
