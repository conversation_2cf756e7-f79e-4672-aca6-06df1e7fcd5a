package com.phonepe.central.stratos.penalty.server.convertor;

import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassState;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCreateRequest;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassDetailEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassEntity;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class PenaltyClassConvertors {

    public static final String PENALTY_CLASS_ID_PREFIX = "PC";

    public PenaltyClassDetailEntity convert(PenaltyClassDetail source) {
        if (source != null) {
            return PenaltyClassDetailEntity.builder()
                    .criteriaConfigData(MapperUtils.serializeToString(source.getCriteria()))
                    .growthConfigData(MapperUtils.serializeToString(source.getGrowthRate()))
                    .label(source.getLabel())
                    .penaltyCap(source.getPenaltyCap())
                    .build();
        }

        return null;
    }

    public PenaltyClassEntity convert(PenaltyClassCreateRequest source) {
        if (source != null) {
            return PenaltyClassEntity.builder()
                    .penaltyClassId(IdGenerator.generate(PENALTY_CLASS_ID_PREFIX)
                            .getId())
                    .name(source.getName())
                    .description(source.getDescription())
                    .tenant_name(source.getTenant()
                            .getName())
                    .tenant_subcategory_name(source.getTenant()
                            .getSubCategory())
                    .version(1)
                    .state(PenaltyClassState.CREATED)
                    .build();
        }

        return null;
    }

    public PenaltyClassDetail convert(PenaltyClassDetailEntity source) {
        if (source != null) {
            return PenaltyClassDetail.builder()
                    .criteria(source.getCriteriaConfigData())
                    .label(source.getLabel())
                    .growthRate(source.getGrowthConfigData())
                    .penaltyCap(source.getPenaltyCap())
                    .build();
        }
        return null;
    }

    public PenaltyClass convert(PenaltyClassEntity source) {
        if (source != null) {
            return PenaltyClass.builder()
                    .id(String.valueOf(source.getPenaltyClassId()))
                    .name(source.getName())
                    .version(source.getVersion())
                    .description(source.getDescription())
                    .tenant(TenantInfo.builder()
                            .name(source.getTenant_name())
                            .subCategory(source.getTenant_subcategory_name())
                            .build())
                    .state(source.getState())
                    .build();
        }
        return null;
    }

    public PenaltyClassEntity clone(PenaltyClassEntity source) {
        if (source != null) {
            return PenaltyClassEntity.builder()
                    .name(source.getName())
                    .version(source.getVersion())
                    .description(source.getDescription())
                    .state(source.getState())
                    .tenant_subcategory_name(source.getTenant_subcategory_name())
                    .tenant_name(source.getTenant_name())
                    .penaltyClassId(source.getPenaltyClassId())
                    .build();
        }
        return null;
    }
}
