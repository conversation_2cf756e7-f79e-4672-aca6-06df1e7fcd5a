package com.phonepe.central.stratos.penalty.server.client;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs.NexusClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.HttpClientUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.http.v2.executor.HeaderPair;
import com.phonepe.platform.http.v2.executor.factory.HttpExecutorBuilderFactory;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
public class NexusClient implements PenaltyClient {

    private final HttpExecutorBuilderFactory httpExecutorBuilderFactory;
    private final OlympusIMClient olympusIMClient;

    @Inject
    public NexusClient(@NexusClientConfig final HttpConfiguration httpConfiguration,
                       final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                       final ObjectMapper mapper,
                       final MetricRegistry metricRegistry,
                       final OlympusIMClient olympusIMClient) {

        this.httpExecutorBuilderFactory = HttpClientUtils.getHttpExecutorBuilderFactory(NexusClient.class,
                httpConfiguration, serviceEndpointProviderFactory, mapper, metricRegistry);
        this.olympusIMClient = olympusIMClient;
    }

    @Override
    public Object getPenaltyTransactionResponse(String penaltyTransactionId) {
        final var url = String.format("/housekeeping/transaction-details/%s", penaltyTransactionId);
        List<HeaderPair> headers = new ArrayList<>();
        headers.add(HeaderPair.builder()
                .name(Constants.AUTHORIZATION)
                .value(olympusIMClient.getSystemAuthHeader())
                .build());
        headers.add(HeaderPair.builder()
                .name("X-ROUTING-KEY")
                .value(penaltyTransactionId)
                .build());
        return HttpClientUtils.executeGet(httpExecutorBuilderFactory, "NexusClient-getPenaltyTransactionResponse", url,
                new TypeReference<>() {
                }, headers);

    }
}


