package com.phonepe.central.stratos.penalty.server.service;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.central.stratos.notification.CommunicationRequest;
import com.phonepe.central.stratos.notification.EmailCommunicationRequest;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevel;
import com.phonepe.central.stratos.penalty.escalation.EscalationMatrix;
import com.phonepe.central.stratos.penalty.escalation.EscalationType;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.escalation.request.EscalateEntityRequest;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltyDateRangeSearchRequest;
import com.phonepe.central.stratos.penalty.response.EscalationResponse;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.EscalationEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.EscalationRepository;
import com.phonepe.central.stratos.penalty.server.queue.actors.EscalatedNotificationActor;
import com.phonepe.central.stratos.penalty.server.queue.messages.EscalatedEntityNotificationQueueMessage;
import com.phonepe.central.stratos.penalty.server.util.PenaltyConvertorUtils;

import java.util.*;
import java.util.stream.Collectors;

import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class EscalationService {

    private final EscalationRepository escalationRepository;
    private final Provider<PenaltyProbableService> penaltyProbableService;
    private final Provider<PenaltyService> penaltyService;
    private final Provider<EscalatedNotificationActor> escalationActor;


    public List<EscalationResponse> createEscalation(TenantInfo tenantInfo,
                                                     EscalationMatrix escalationMatrix,
                                                     String escalationMappingId) {
        List<EscalationResponse> escalation = getEscalations(escalationMappingId);
        if (CollectionUtils.isNotEmpty(escalation)) {
            log.info("Escalations config is already present");
            return escalation;
        }
        List<EscalationEntity> entities = PenaltyConvertorUtils.convertEscalationMatrixToEscalationEntities(tenantInfo,
                escalationMatrix, escalationMappingId);
        return entities.stream()
                .map(escalationRepository::save)
                .filter(Optional::isPresent)
                .map(entity -> EscalationResponse.builder()
                        .escalationLevel(entity.get()
                                .getEscalationLevel())
                        .id(entity.get()
                                .getId())
                        .build())
                .collect(Collectors.toList());
    }

    public List<EscalationResponse> getEscalations(String escalationMappingId) {
        List<EscalationEntity> entities = escalationRepository.getEscalationsFor(escalationMappingId);
        return entities.stream()
                .map(PenaltyConvertorUtils::convertEscalationEntityToEscalationResponse)
                .collect(Collectors.toList());

    }
    public EscalationResponse getEscalations(String escalationMappingId, EscalationLevel escalationLevel) {
        List<EscalationEntity> entities = escalationRepository.getEscalationsFor(escalationMappingId, escalationLevel);
        return entities.stream()
                .map(PenaltyConvertorUtils::convertEscalationEntityToEscalationResponse)
                .findFirst().orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.ESCALATION_NOT_FOUND,
                        Map.of(Constants.MESSAGE, ("Unable to find escalationEntity for escalationMappingId: " +
                                escalationMappingId + " and escalationLevel: " + escalationLevel))));

    }

    public EscalationMatrix getEscalationMatrix(String escalationMappingId) {
        List<EscalationResponse> responseList = this.getEscalations(escalationMappingId);
        return PenaltyConvertorUtils.convertEscalationResponseToEscalationMatrix(responseList);
    }

    @SuppressWarnings("java:S1604")
    public void escalateEntities(EscalateEntityRequest escalateEntityRequest) {
        EscalationResponse escalation = getEscalations(escalateEntityRequest.getMappingId(),
                escalateEntityRequest.getEscalationLevel());
        Set<String> emailDLList = escalation.getEmailDl();
        escalation.getEscalationType().accept(new EscalationType.EscalationTypeVisitor<>(){
            @Override
            public Void visitPenalty() {
                escalatePenaltyEntities(escalateEntityRequest, emailDLList);
                return null;
            }
        });

    }
    private void escalatePenaltyEntities(EscalateEntityRequest escalateEntityRequest, Set<String> emailDLList) {
        var dateRangeRequest = escalateEntityRequest.getDateRangeRequest();
        String mappingId = escalateEntityRequest.getMappingId();
        CommunicationRequest communicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(emailDLList)
                .build();
        escalateEntityRequest.getEscalationLevel().accept(new EscalationLevel.EscalationLevelVisitor<>() {
            @Override
            public Void visitL1() {
                var dateRange = DateRangeRequest.builder()
                        .fromDate(PenaltyUtil.addTwoDaysToDate(dateRangeRequest.getFromDate()))
                        .toDate(PenaltyUtil.addTwoDaysToDate(dateRangeRequest.getToDate()))
                        .build();

                List<PenaltyProbable> penaltyProbables = penaltyProbableService.get()
                        .getDuePenaltyProbablesFromRange(dateRange).stream()
                        .filter(p -> Objects.equals(p.getPenaltyClassId(), mappingId)).toList();

                publishEscalation(EscalatedEntityNotificationQueueMessage.<PenaltyProbable>builder()
                        .mappingId(mappingId)
                        .escalationType(EscalationType.PENALTY)
                        .escalationLevel(EscalationLevel.L1)
                        .escalatedEntities(penaltyProbables)
                        .communicationRequest(communicationRequest)
                        .dateRangeRequest(dateRange)
                        .build());

                return null;
            }

            @Override
            public Void visitL2() {
                var dateRange = DateRangeRequest.builder()
                        .fromDate(PenaltyUtil.addOneDayToDate(dateRangeRequest.getFromDate()))
                        .toDate(PenaltyUtil.addOneDayToDate(dateRangeRequest.getToDate()))
                        .build();

                List<PenaltyProbable> penaltyProbables = penaltyProbableService.get()
                        .getDuePenaltyProbablesFromRange(dateRange).stream()
                        .filter(p -> Objects.equals(p.getPenaltyClassId(), mappingId)).toList();

                publishEscalation(EscalatedEntityNotificationQueueMessage.<PenaltyProbable>builder()
                        .mappingId(mappingId)
                        .escalationType(EscalationType.PENALTY)
                        .escalationLevel(EscalationLevel.L2)
                        .escalatedEntities(penaltyProbables)
                        .communicationRequest(communicationRequest)
                        .dateRangeRequest(dateRange)
                        .build());

                return null;
            }

            @Override
            public Void visitL3() {
                List<Penalty> penalties = penaltyService.get().fetchCreatedPenalties(PenaltyDateRangeSearchRequest.builder()
                        .penaltyClassId(mappingId)
                        .dateRangeRequest(dateRangeRequest)
                        .build());

                publishEscalation(EscalatedEntityNotificationQueueMessage.<Penalty>builder()
                        .mappingId(mappingId)
                        .escalationType(EscalationType.PENALTY)
                        .escalationLevel(EscalationLevel.L3)
                        .escalatedEntities(penalties)
                        .communicationRequest(communicationRequest)
                        .dateRangeRequest(dateRangeRequest)
                        .build());

                return null;
            }

            @Override
            public Void visitL4() {
                Date toDate = PenaltyUtil.subOneDayFromDate(dateRangeRequest.getToDate());
                List<Penalty> penalties = penaltyService.get().fetchCreatedPenaltiesBeforeDate(mappingId, toDate);

                Date fromDate = penalties.stream()
                        .map(Penalty::getQualifiedAt)
                        .filter(Objects::nonNull)
                        .min(Date::compareTo)
                        .orElse(toDate);

                publishEscalation(EscalatedEntityNotificationQueueMessage.<Penalty>builder()
                        .mappingId(mappingId)
                        .escalationType(EscalationType.PENALTY)
                        .escalationLevel(EscalationLevel.L4)
                        .escalatedEntities(penalties)
                        .communicationRequest(communicationRequest)
                        .dateRangeRequest(DateRangeRequest.builder()
                                .fromDate(fromDate)
                                .toDate(toDate)
                                .build())
                        .build());

                return null;
            }
        });
    }

    private void publishEscalation(EscalatedEntityNotificationQueueMessage escalatedEntityNotificationQueueMessage) {
        try {
            if (escalatedEntityNotificationQueueMessage.getEscalatedEntities().isEmpty()) {
                log.info("No entities to escalate for mappingId {}, escalation type {} and escalation level {}",
                        escalatedEntityNotificationQueueMessage.getMappingId(),
                        escalatedEntityNotificationQueueMessage.getEscalationType(),
                        escalatedEntityNotificationQueueMessage.getEscalationLevel());
                return;
            }
            escalationActor.get().publish(escalatedEntityNotificationQueueMessage);
        } catch (Exception e) {
            log.error("Error while publishing escalation message", e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.ESCALATION_PUBLISH_ERROR,
                    Map.of(Constants.MESSAGE, "Unable to publish escalation message"));
        }
    }
}
