package com.phonepe.central.stratos.penalty.server.util;

import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassSearchRequest;
import com.phonepe.central.stratos.penalty.server.convertor.PenaltyClassConvertors;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.EscalationEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassEntity.Fields;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyDisbursementEntity;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
@UtilityClass
public class PenaltyDBUtils {

    public static final String PENALTY_PROBABLE_ID_PREFIX = "PP";

    private static final Date REF_DATE_FOR_MONTHLY_PARTITIONS = new Date(
            1451606400000L); // Friday, 1 January 2016 00:00:00
    private static final String TIME_ZONE = "Asia/Kolkata";

    private static final int TOTAL_PARTITIONS = 120;

    public DetachedCriteria getPenaltyClassDetachCriteria(PenaltyClassSearchRequest searchRequest,
                                                          List<TenantInfo> tenantInfoList) {

        if (tenantInfoList.isEmpty()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PENALTY_TENANT_INFO_LIST_IS_EMPTY);
        }

        final var detachedCriteria = DetachedCriteria.forClass(PenaltyClassEntity.class);
        if (StringUtils.isNotBlank(searchRequest.getTenantName())) {
            detachedCriteria.add(Restrictions.eq(PenaltyClassEntity.Fields.tenant_name, searchRequest.getTenantName()));
        } else {
            detachedCriteria.add(Restrictions.in(PenaltyClassEntity.Fields.tenant_name, tenantInfoList.stream()
                    .map(TenantInfo::getName)
                    .collect(Collectors.toList())));
        }
        if (searchRequest.getPenaltyClassId() != null) {
            detachedCriteria.add(
                    Restrictions.eq(PenaltyClassEntity.Fields.penaltyClassId, searchRequest.getPenaltyClassId()));
        }
        if (StringUtils.isNotBlank(searchRequest.getTenantSubCategory())) {
            detachedCriteria.add(Restrictions.eq(PenaltyClassEntity.Fields.tenant_subcategory_name,
                    searchRequest.getTenantSubCategory()));
        } else {
            detachedCriteria.add(Restrictions.in(Fields.tenant_subcategory_name, tenantInfoList.stream()
                    .map(TenantInfo::getSubCategory)
                    .collect(Collectors.toList())));
        }
        if (searchRequest.getVersion() != null) {
            detachedCriteria.add(Restrictions.eq(PenaltyClassEntity.Fields.version, searchRequest.getVersion()));
        }
        return detachedCriteria;

    }

    public DetachedCriteria getEscalationEntityCriteria(String escalationMappingId) {
        final var detachedCriteria = DetachedCriteria.forClass(EscalationEntity.class);
        detachedCriteria.add(Restrictions.eq(EscalationEntity.Fields.escalationMappingId, escalationMappingId));
        return detachedCriteria;
    }

    public DetachedCriteria getPenaltyDisbursementCriteria(String penaltyClassId) {
        final var detachedCriteria = DetachedCriteria.forClass(PenaltyDisbursementEntity.class);
        detachedCriteria.add(Restrictions.eq(PenaltyDisbursementEntity.Fields.penaltyClassId, penaltyClassId));
        return detachedCriteria;
    }

    public static int getMonthBasedPartition(final Date date) {
        final long months = ChronoUnit.MONTHS.between(REF_DATE_FOR_MONTHLY_PARTITIONS.toInstant()
                .atZone(ZoneId.of(TIME_ZONE)), date.toInstant()
                .atZone(ZoneId.of(TIME_ZONE)));
        return (int) months % TOTAL_PARTITIONS;
    }

    public static Integer createNewClassVersion(List<PenaltyClass> classConfigs) {
        if (CollectionUtils.isNotEmpty(classConfigs)) {
            return classConfigs.stream()
                    .map(PenaltyClass::getVersion)
                    .max(Integer::compareTo)
                    .orElse(1) + 1;
        }
        return 1;
    }

    public static String generateProbableId(String penaltyClassId,
                                            String transactionId) {
        return PENALTY_PROBABLE_ID_PREFIX + StringUtils.substring(penaltyClassId,
                PenaltyClassConvertors.PENALTY_CLASS_ID_PREFIX.length()) + transactionId;
    }
}
