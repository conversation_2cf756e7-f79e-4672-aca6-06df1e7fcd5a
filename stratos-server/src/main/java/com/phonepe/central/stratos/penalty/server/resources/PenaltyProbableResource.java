package com.phonepe.central.stratos.penalty.server.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.request.PenaltyProbableRequest;
import com.phonepe.central.stratos.penalty.response.PenaltyProbableResponse;
import com.phonepe.central.stratos.penalty.server.service.PenaltyAuthorizationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyProbableService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState.ApiStateString;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.PenaltyPermissionsConstants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.annotations.AccessAllowed;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */

@Slf4j
@Path("/v1/penalty/probable")
@Tag(name = "Penalty Probable Related APIs")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class PenaltyProbableResource {

    public static final String SUCCESS = "Success";
    private final PenaltyProbableService penaltyProbableService;


    private final PenaltyAuthorizationService penaltyAuthorizationService;

    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_PROBABLE_CREATE_PERMISSION)
    @Path("/register")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Operation(summary = "Register Probable for transaction")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response registerProbable(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                             @NotNull @Valid PenaltyProbableRequest penaltyProbableRequest) {
        try {
            boolean isAuthorised = penaltyAuthorizationService.isTenantAuthorizedToRegisterProbable(
                    serviceUserPrincipal.getUserAuthDetails(),penaltyProbableRequest.getPenaltyClassId());
            if (!isAuthorised) {
                return Response.status(Status.FORBIDDEN)
                        .entity("User is not authorized to register penalty probable for tenant")
                        .build();
            }

            log.info("Input requests for probable register  is  {} ", penaltyProbableRequest);
            String probableId = penaltyProbableService.registerProbable(penaltyProbableRequest);
            return Response.ok("Successfully register probable with ID : "+ probableId)
                    .build();
        } catch (DisputeException exception) {
            log.error("Exception in register penalty probable is  {} {}", penaltyProbableRequest, exception);
            throw DisputeExceptionUtil.propagate(exception);
        }

    }


    @GET
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_PROBABLE_VIEW_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/search/{penaltyClassId}")
    @Operation(summary = "Get Penalty probable configs for classId with pagination")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response getProbableBy(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                           @PathParam("penaltyClassId") final String penaltyClassId,
                                           @Parameter(description = "Number of records to fetch") @QueryParam("limit") @DefaultValue("10") @Max(100) @Min(1) int limit,
                                           @Parameter(description = "Starting point for records") @QueryParam("offset") @DefaultValue("1") @Max(100) @Min(1) int offset) {
        try {
            boolean isAuthorised = penaltyAuthorizationService.isTenantAuthorizedToViewProbable(
                    serviceUserPrincipal.getUserAuthDetails(), penaltyClassId);
            if (!isAuthorised) {
                return Response.status(Status.FORBIDDEN)
                        .entity("User is not authorized to view penalty probable  for tenant")
                        .build();
            }

            log.info("search probable requests with penaltyClassID {} ", penaltyClassId);
            List<PenaltyProbableResponse> response = penaltyProbableService.getPaginatedPenaltyProbables(penaltyClassId,
                    limit);
            return Response.ok(response)
                    .build();
        } catch (Exception exception) {
            log.error("Exception in processing the probable getting paginated request {}", penaltyClassId, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PROBABLE_GET_ERROR, exception);
        }

    }

    @POST
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_PROBABLE_RECONCILIATION_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/range/dueDate/reconcile")
    @Operation(summary = "Reconcile probables for date range of max one day")
    @ApiKillerMeta(tags = {ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response reconcileByRange(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                                 @NotNull @Valid DateRangeRequest dateRangeRequest) {
        try {
            if (dateRangeRequest.getDifferenceInTimeUnit(TimeUnit.DAYS) > 1) {
                return Response.status(Status.BAD_REQUEST)
                        .entity("More than One day range scan is not supported")
                        .build();
            }
            log.info("Range scan for probable  requests with param {}", dateRangeRequest);
            penaltyProbableService.reconcile(dateRangeRequest);
            return Response.ok(SUCCESS)
                    .build();
        } catch (Exception exception) {
            log.error("Exception in processing the probable get request {}", dateRangeRequest, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.PROBABLE_GET_ERROR, exception);
        }
    }


    @POST
    @ExceptionMetered
    @AccessAllowed
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/reconcile/callback/{penaltyProbableId}")
    @Operation(summary = "Recon callback for penalty Penalty probable configs for ID")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response reconcileByProbableId(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                                 @NotNull @Valid @PathParam("penaltyProbableId") String penaltyProbableId) {
        try {

            log.info("Input requests for probable with param  {}", penaltyProbableId);
            penaltyProbableService.reconcile(penaltyProbableId);
            return Response.ok(SUCCESS)
                    .build();
        } catch (DisputeException exception) {
            log.error("Exception in processing the probable reconcile request {}", penaltyProbableId, exception);
            throw DisputeExceptionUtil.propagate(exception);
        }
    }

    @DELETE
    @ExceptionMetered
    @RolesAllowed(PenaltyPermissionsConstants.PENALTY_PROBABLE_UPDATE_PERMISSION)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @Path("/{penaltyProbableId}")
    @Operation(summary = "Remove penalty probable from storage")
    @ApiKillerMeta(tags = {ApiStateString.WRITE_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public Response removeProbable(@Parameter(hidden = true) @Auth @NotNull ServiceUserPrincipal serviceUserPrincipal,
                                                 @NotNull @Valid @PathParam("penaltyProbableId") String penaltyProbableId) {
        try {

            log.info("Removing probable from storage with  param  {}", penaltyProbableId);
            penaltyProbableService.removeProbableFromStorage(penaltyProbableId);
            return Response.ok(SUCCESS)
                    .build();
        } catch (DisputeException exception) {
            log.error("Exception in removing the probable  request {}", penaltyProbableId, exception);
            throw DisputeExceptionUtil.propagate(exception);
        }
    }
}
