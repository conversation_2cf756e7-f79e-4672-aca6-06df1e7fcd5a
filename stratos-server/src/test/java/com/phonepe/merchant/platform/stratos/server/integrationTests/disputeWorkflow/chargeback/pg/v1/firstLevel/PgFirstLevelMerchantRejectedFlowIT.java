package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.firstLevel;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PG_PARTIAL_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PG_PARTIAL_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED;

import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.PgBaseTest;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import org.junit.jupiter.api.Test;

public class PgFirstLevelMerchantRejectedFlowIT extends PgBaseTest {

    @Test
    void testFulfillmentCompleted() {

        createAndTestTillPgRepresentment();

    }


    @Test
    void testPartialFulfillmentCompleted() {

        createEntrySetup();
        assertRefundBlock();

        assertTriggerEvent(RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
            PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);

        // do partial representment
        assertTriggerEvent(COMPLETE_PG_PARTIAL_REPRESENTMENT,
            PG_PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext(disputeWorkflow.getDisputedAmount() - 10));

        triggerPartialDebit(disputeWorkflow.getDisputedAmount() - 10);

    }

    @Test
    void testRepresentmentWithMerchantNotResponded() {

        createAndTestTillMerchantNotResponded();
        assertTriggerEvent(COMPLETE_PG_REPRESENTMENT, PG_REPRESENTMENT_COMPLETED);

    }

    @Test
    void testPartialRepresentmentWithMerchantNotResponded() {

        createAndTestTillMerchantNotResponded();

        // do partial representment
        assertTriggerEvent(COMPLETE_PG_PARTIAL_REPRESENTMENT,
            PG_PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext(disputeWorkflow.getDisputedAmount() - 10));

        triggerPartialDebit(disputeWorkflow.getDisputedAmount() - 10);

    }

}
