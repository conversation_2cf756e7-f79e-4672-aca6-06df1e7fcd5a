package com.phonepe.merchant.platform.stratos.server.core.resources;

import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.commons.DownloadReportType;
import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeData;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CheckStatusRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DownloadReportRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CheckStatusResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CreateDisputeResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.RefundEligibilityResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingComplaintStatusRequest;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirOutgoingComplaintStatusResponse;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.dispute.creation.TestDisputeCreationRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.EnumClass;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.models.response.GenericResponse;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import javax.ws.rs.core.Response;
import org.eclipse.jetty.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DisputeResourceTest extends ErrorConfiguratorBaseTest {

    @Mock
    private DisputeService disputeService;

    @InjectMocks
    private DisputeResource disputeResource;

    @Mock
    private OlympusIMClient olympusIMClient;

    @Captor
    private ArgumentCaptor<DownloadReportRequest> reportRequestArgumentCaptor;

    @BeforeEach
    public void setup() throws URISyntaxException, MalformedURLException {
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    void testGenerateSvgGraph() {

        final String svgGRaph = "SVG_GRAPH";

        Mockito.when(disputeService
            .getStateMachineSvgGraph(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowVersion.V1)).thenReturn(svgGRaph);

        final var resultSvgGraph = disputeResource
            .getStateMachineSvgGraph(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowVersion.V1);

        Assertions.assertEquals(svgGRaph, resultSvgGraph);
    }

    @Test
    void testStatusOlympus() {
        ServiceUserPrincipal serviceUserPrincipal = ServiceUserPrincipal.builder()
                .userAuthDetails(UserAuthDetails.builder()
                        .encodedTenantPermissions(new HashMap<>() {
                        }).build())
                .build();
        CheckStatusRequest checkStatusRequest = UdirOutgoingComplaintStatusRequest.builder()
                .complaintId("CID")
                .transactionId("TXID")
                .build();
        CheckStatusResponse checkStatusResponse = UdirOutgoingComplaintStatusResponse.builder()
                .complaintId("CID")
                .crn("crn")
                .build();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.anyString());
        Mockito.when(disputeService.checkStatus(checkStatusRequest)).thenReturn(checkStatusResponse);
        CheckStatusResponse response = disputeResource.status(checkStatusRequest, serviceUserPrincipal);
        Assertions.assertEquals(checkStatusResponse, response);
    }

    @Test
    void testGetAllEnumsByEnumClassName() {
        Map<Integer, String> map1 = new HashMap<>();
        Map<String,Map<Integer, String> > map = new HashMap<>();
        map1.put(0,"v1");
        map.put(EnumClass.DISPUTE_WORKFLOW_VERSION.name(), map1);
        Mockito.when(disputeService.getAllEnumsByEnumClassName(
            Stream.of(EnumClass.DISPUTE_WORKFLOW_VERSION)
                .collect(Collectors.toList()))).thenReturn(map);
        final var resultMap = disputeResource.getAllEnumsByEnumClassName(Stream.of(EnumClass.DISPUTE_WORKFLOW_VERSION)
            .collect(Collectors.toList()));

        Assertions.assertEquals(resultMap,map);
    }

    @Test
    void testDisputeDownload(){

        final var request = TestDataUtils.getDownloadReportRequest(DownloadReportType.CHARGEBACK);
        final var expectedBytes = "content".getBytes(StandardCharsets.UTF_8);

        Mockito.when(disputeService.download(reportRequestArgumentCaptor.capture())).thenReturn(expectedBytes);
        Mockito.when(olympusIMClient.verifyPermission(Mockito.any(), Mockito.anyString())).thenReturn(true);
        // Test Chargeback
        final var responseChargeback = disputeResource.downloadSummary(TestDataUtils.getOlympusUser(),request);

        Assertions.assertEquals(expectedBytes, responseChargeback.getEntity());
        Assertions.assertEquals(request, reportRequestArgumentCaptor.getValue());

        request.setReportType(DownloadReportType.TOA);

        // Test p2pm toa download
        final var p2pmToaResponse = disputeResource.downloadSummary(TestDataUtils.getOlympusUser(),request);
        Assertions.assertEquals(expectedBytes, p2pmToaResponse.getEntity());
        Assertions.assertEquals(request, reportRequestArgumentCaptor.getValue());

        request.setReportType(DownloadReportType.NPCI_CHARGEBACK);

        // Test p2pm toa download
        final var npciChargebackResponse = disputeResource.downloadSummary(TestDataUtils.getOlympusUser(),request);
        Assertions.assertEquals(expectedBytes, npciChargebackResponse.getEntity());
        Assertions.assertEquals(request, reportRequestArgumentCaptor.getValue());

    }
    @Test
    void testRefundEligibility(){
        String transactionId = "T12345671818";
        RefundEligibilityResponse expected = Mockito.mock(RefundEligibilityResponse.class);
        Mockito.when(disputeService.getRefundEligibility(transactionId)).thenReturn(expected);
        final var response = disputeResource.refundEligibility(transactionId);
        Assertions.assertEquals(response, expected);
    }

    @Test
    void testReconcile(){
        DisputeReconcileRequest request = Mockito.mock(
            SuspectedFraudChargebackDisputeReconcileRequest.class);
        Mockito.doNothing().when(disputeService).reconcile(Mockito.any());

        Response response = disputeResource.reconcile(request);

        Assertions.assertEquals(200,response.getStatus());

    }

    @Test
    void testCreateDispute(){
        CreateDisputeRequest testCreateDisputeRequest = TestDisputeCreationRequest.builder()
            .disputedAmount(12)
            .disputeData(DisputeData.builder()
                .disputeType(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.CHARGEBACK)
                .disputeCategory(DisputeCategory.SERVICE)
                .disputeStage(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL)
                .build())
            .transactionType(TransactionType.UPI)
            .transactionId("T123343")
            .build();

        Mockito.doReturn(CreateDisputeResponse.builder().disputeId("1233").build()).when(disputeService).createDispute(Mockito.any(), Mockito.any());
        GenericResponse<CreateDisputeResponse> response = disputeResource.createDispute(TransactionType.UPI,
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.CHARGEBACK,
            testCreateDisputeRequest, TestDataUtils.getOlympusUser());

        Assertions.assertEquals(String.valueOf(HttpStatus.OK_200), response.getCode());
    }

    @Test
    void testCreateDisputeWithException(){
        CreateDisputeRequest testCreateDisputeRequest = TestDisputeCreationRequest.builder()
            .disputedAmount(12)
            .disputeData(DisputeData.builder()
                .disputeType(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.CHARGEBACK)
                .disputeCategory(DisputeCategory.SERVICE)
                .disputeStage(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL)
                .build())
            .transactionType(TransactionType.UPI)
            .transactionId("T123343")
            .build();

        Mockito.doThrow(new RuntimeException()).when(disputeService).createDispute(Mockito.any(), Mockito.any());
        GenericResponse<CreateDisputeResponse> response = disputeResource.createDispute(TransactionType.UPI,
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.CHARGEBACK,
            testCreateDisputeRequest, TestDataUtils.getOlympusUser());

        Assertions.assertEquals(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR_500), response.getCode());
    }

    @Test
    void testCreateDisputeWithDisputeException(){
        CreateDisputeRequest testCreateDisputeRequest = TestDisputeCreationRequest.builder()
            .disputedAmount(12)
            .disputeData(DisputeData.builder()
                .disputeType(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.CHARGEBACK)
                .disputeCategory(DisputeCategory.SERVICE)
                .disputeStage(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.FIRST_LEVEL)
                .build())
            .transactionType(TransactionType.UPI)
            .transactionId("T123343")
            .build();

        Mockito.doThrow(DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND)).when(disputeService).createDispute(Mockito.any(), Mockito.any());
        GenericResponse<CreateDisputeResponse> response = disputeResource.createDispute(TransactionType.UPI,
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.CHARGEBACK,
            testCreateDisputeRequest, TestDataUtils.getOlympusUser());

        Assertions.assertEquals("404", response.getCode());
        Assertions.assertEquals("Dispute Not Found", response.getMessage());
    }
}