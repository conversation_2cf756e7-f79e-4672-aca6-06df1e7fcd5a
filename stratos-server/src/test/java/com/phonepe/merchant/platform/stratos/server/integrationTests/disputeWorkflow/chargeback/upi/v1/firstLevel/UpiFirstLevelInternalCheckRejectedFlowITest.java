package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v1.firstLevel;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_RGCS_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.RGCS_ACCEPTANCE_COMPLETED;

class UpiFirstLevelInternalCheckRejectedFlowITest extends UpiFirstLevelBaseTest {

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testRepresentmentCompletedThenCreditReceived(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDispute(getDisputeType(), getDisputeStage(), disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        assertFromCreateToRepresentmentRequirement(Set.of(COMPLETE_NPCI_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);
        testFromRepresentmentCompletedToCreditReceived();
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testRgcsCompleted(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDispute(getDisputeType(), getDisputeStage(), disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiMockingRgcsTransaction(dispute.getTransactionReferenceId(),
            dispute.getTransactionAmount());

        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        // Assert Status of Dispute Workflow to be RGCS Acceptance Required
        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.RGCS_ACCEPTANCE_REQUIRED,
            5L, TimeUnit.SECONDS);

        // Test Allowed Transitions from Current State
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                DisputeWorkflowState.RGCS_ACCEPTANCE_REQUIRED);
        Assertions
            .assertEquals(Set.of(COMPLETE_RGCS_ACCEPTANCE), upcomingEvents);

        // Trigger Event for Acceptance of RGCS
        assertTriggerEvent(COMPLETE_RGCS_ACCEPTANCE, RGCS_ACCEPTANCE_COMPLETED);

        // As Workflow has ended, No more Transitions should be allowed on same
        final var stratosError = Assertions.assertThrows(DisputeException.class, () ->
            disputeService
                .triggerEvent(serviceUserPrincipal.getUserAuthDetails(),
                    dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                    COMPLETE_RGCS_ACCEPTANCE,
                    Constants.EMPTY_TRANSITION_CONTEXT));

        Assertions
            .assertEquals(StratosErrorCodeKey.TRANSITION_NOT_ALLOWED, stratosError.getErrorCode());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testInternalMidCreditReceived(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDisputeWithInternalMerchant(getDisputeType(), getDisputeStage(),
            disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiFullRefundExistsMocking(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(),
            "YBL1234");

        assertFromCreateToInternalMidRepresentmentRequired(Set.of(COMPLETE_NPCI_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);
        testFromRepresentmentCompletedToCreditReceived();

    }
}