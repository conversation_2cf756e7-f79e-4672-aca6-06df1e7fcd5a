package com.phonepe.merchant.platform.stratos.server.integrationTests.row;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.RowStateDto;
import com.phonepe.merchant.platform.stratos.models.row.EdcRowDto;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowHistoryRequest;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowReplayRequest;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.RowResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class ReplyRowTest extends LoadOnlyOnClassLevelBaseTest {

    private DisputeService disputeService;
    private FileResource fileResource;
    private RowService rowService;
    private RowResource rowResource;
    private OlympusIMClient olympusIMClient;


    @BeforeEach
    void setUpGuiceInjection() {
        rowService = guiceInjector.getInstance(RowService.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        fileResource = guiceInjector.getInstance(FileResource.class);
        rowResource = guiceInjector.getInstance(RowResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        MARIA_DB_CONTAINER.executeQuery("TRUNCATE stratos_shard_1.file;\n"
            + "TRUNCATE stratos_shard_2.file;\n"
            + "TRUNCATE stratos_shard_1.file_audit;\n"
            + "TRUNCATE stratos_shard_2.file_audit;\n"
            + "TRUNCATE stratos_shard_1.row;\n"
            + "TRUNCATE stratos_shard_2.row;\n"
            + "TRUNCATE stratos_shard_1.row_audit;\n"
            + "TRUNCATE stratos_shard_2.row_audit;", true);
    }

    @Test
    void testMisRowReplay() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var pgTransactionId = IdGenerator.generate("PG").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking(
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(pgTransactionId));
        MockingUtils.setupPgTransportTransactionDetailMocking(pgTransactionId,
            transactionReferenceId);
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        MockingUtils.setupPaymentsApiNoRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100);

        final var userAuthDetails = TestDataUtils.getOlympusAuthDetails();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL,
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(pgTransactionId),
            FormDataContentDisposition.name("randomName").fileName("file- 20-12-2021.csv")
                .build(), "AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.PG_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);

        //Testing for olympus user
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        // Moving the chargeback to PG_ACCEPTANCE_COMPLETED
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_PG_ACCEPTANCE,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED);

        // Mis Row Signal Received
        rowService.processSignal(TestDataUtils.getPgMisReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK, 1.00, "mis_reports_file"));

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.DEBIT_RECEIVED,
            5L, TimeUnit.SECONDS);


    }

    @Test
    void testEdcMisRowReplay() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var merchantTransactionId = TestDataUtils.idHelper.merchantTransactionId(transactionReferenceId);
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForEdcCreateChargebackApiMocking();
        MockingUtils.setEdcTransactionDetails("MERCHANTID",
            transactionReferenceId, 100, 0, merchantTransactionId);
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(transactionReferenceId, 100, 0, merchantTransactionId);
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.setEdcUnBlockReversals(merchantTransactionId);
        MockingUtils.setEdcBlockReversals(merchantTransactionId);
        final var userDetailsOlympus = HumanUserDetails.builder()
            .userId("test-user")
            .build();
        final var userAuthDetails = UserAuthDetails.builder()
            .userDetails(userDetailsOlympus)
            .build();
        final var serviceUserPrincipal = ServiceUserPrincipal.builder()
            .userAuthDetails(userAuthDetails)
            .build();

        var response = fileResource.upload(DisputeTypeDto.EDC_CHARGEBACK,
            FileTypeDto.EDC_FIRST_LEVEL, TestDataUtils.edcChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.EDC_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        Assertions.assertTrue(disputeWorkflow instanceof FinancialDisputeWorkflow);
        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REFUND_BLOCKED,
            5L, TimeUnit.SECONDS);
        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);

        rowService.processSignal(TestDataUtils.getEdcReportRequest(transactionReferenceId,
            RowTransactionType.CHARGEBACK, 100.0, "mis_reports_file2"));

        AssertionUtils.assertRowStateEquals(RowState.FAILED, "mis_reports_file2", 15,
            TimeUnit.SECONDS);

        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_ACCEPTANCE,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.ACCEPTANCE_COMPLETED);

        var RowResponse = rowService.history(RowHistoryRequest.builder()
            .rowTypes(Set.of(RowTypeDto.EDC_MIS_ROW))
            .dateRangeFilter(DateRangeFilter.builder()
                .dateRange(DateRange.builder().startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .build())
            .build());

        rowResource.replay(
            RowReplayRequest.builder()
                .sourceId(((EdcRowDto) RowResponse.getRows().get(0)).getSourceId())
                .rowId(((EdcRowDto) RowResponse.getRows().get(0)).getRowId())
                .build());
        AssertionUtils.assertRowStateEquals(RowState.PROCESSED, "mis_reports_file2", 15,
            TimeUnit.SECONDS);
    }

    @Test
    void testEdcFileRowReplay() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var merchantTransactionId = TestDataUtils.idHelper.merchantTransactionId(transactionReferenceId);
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForEdcCreateChargebackApiMocking();
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(transactionReferenceId, 100, 100, merchantTransactionId);
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.setEdcUnBlockReversals(merchantTransactionId);
        final var userDetailsOlympus = HumanUserDetails.builder()
            .userId("test-user")
            .build();
        final var userAuthDetails = UserAuthDetails.builder()
            .userDetails(userDetailsOlympus)
            .build();
        final var serviceUserPrincipal = ServiceUserPrincipal.builder()
            .userAuthDetails(userAuthDetails)
            .build();

        var response = fileResource.upload(DisputeTypeDto.EDC_CHARGEBACK,
            FileTypeDto.EDC_FIRST_LEVEL, TestDataUtils.edcChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);
        AssertionUtils.assertRowStateEquals(RowState.FAILED, response.getId(), 15,
            TimeUnit.SECONDS);
        // Didn't mock the transaction detail API last time.
        MockingUtils.setEdcTransactionDetails("MERCHANTID",
            transactionReferenceId, 100, 100, merchantTransactionId);
        var fileRowResponse = fileResource.fileRow(response.getId());
        var fileRow = fileRowResponse.getRows().get(0);
        rowResource.replay(
            RowReplayRequest.builder().sourceId(response.getId()).rowId(fileRow.getRowId())
                .build());
        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);
    }

    @Test
    void testFileRowReplay() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking(
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId("PG1234"));
        MockingUtils.setupPgTransportTransactionDetailMocking("PG1234",
            transactionReferenceId);
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.setupPaymentsApiFullRefundExistsMockingPaidFromDebitCard(
            transactionReferenceId, 100, "PG1234");
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.any());

        final var userAuthDetails = TestDataUtils.getOlympusAuthDetails();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_FIRST_LEVEL,
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgTransactionId("PG1234"),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        //First Level File Upload has succeeded
        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackPreArbApiMocking();

        //Pre Arb File Upload
        var preArbResponse = fileResource.upload(DisputeTypeDto.PG_CHARGEBACK,
            FileTypeDto.PG_PRE_ARBITRATION,
            TestDataUtils.pgPreArbLevelChargebackCSVFileStream("HDFC Bank"),
            FormDataContentDisposition.name("randomName1").fileName("file2.csv")
                .build(), "AuthToken",serviceUserPrincipal);

        //Pre Arb File Upload Failed because of First Level is not moved to terminal state
        AssertionUtils.assertRowStateEquals(RowState.FAILED, preArbResponse.getId(), 10,
            TimeUnit.SECONDS);

        var fileRowResponse = fileResource.fileRow(preArbResponse.getId());
        Assertions.assertEquals(1, fileRowResponse.getRows().size());

        var fileRow = fileRowResponse.getRows().get(0);
        Assertions.assertEquals(RowStateDto.FAILED, fileRow.getRowState());
        Assertions.assertEquals(StratosErrorCodeKey.PREVIOUS_DISPUTE_WORKFLOW_NOT_ENDED.name(),
            fileRow.getRowContext().getCode());

        // Replay File Row failed as First level is not moved yet
        var stratosError = Assertions.assertThrows(DisputeException.class, () -> rowResource.replay(
            RowReplayRequest.builder().sourceId(preArbResponse.getId()).rowId(fileRow.getRowId())
                .build()));

        Assertions.assertEquals(StratosErrorCodeKey.PREVIOUS_DISPUTE_WORKFLOW_NOT_ENDED,
            stratosError.getErrorCode());

        AssertionUtils.assertRowStateEquals(RowState.FAILED, preArbResponse.getId(), 10,
            TimeUnit.SECONDS);

        // Moving First Level to Represented State
        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.PG_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED,
            5L, TimeUnit.SECONDS);

        // Replaying PreArb row
        rowResource.replay(
            RowReplayRequest.builder().sourceId(preArbResponse.getId()).rowId(fileRow.getRowId())
                .build());

        //PreArb file and row state processed after replay
        AssertionUtils.assertFileProcessed(preArbResponse.getId(), 20, TimeUnit.SECONDS);

        final var preArbDisputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.PG_CHARGEBACK,
                DisputeStage.PRE_ARBITRATION);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, preArbDisputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REPRESENTMENT_REQUIRED,
            5L, TimeUnit.SECONDS);

        AssertionUtils.assertRowStateEquals(RowState.PROCESSED, preArbResponse.getId(), 10,
            TimeUnit.SECONDS);

        var fileRowResponseAfterReplay = fileResource.fileRow(preArbResponse.getId());
        //Since the fileRow api only returns FAILED rows, the number of rows in response is 0
        Assertions.assertEquals(0, fileRowResponseAfterReplay.getRows().size());
    }


}
