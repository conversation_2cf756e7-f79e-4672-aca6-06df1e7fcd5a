package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeFeed;
import com.phonepe.merchant.platform.stratos.server.core.configs.TstoreClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.platform.scroll.model.v2.CreatedSchemaParams;
import com.phonepe.tstore.client.TstoreClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

@ExtendWith(MockitoExtension.class)
class TstoreServiceImplTest {

    @Mock
    private TstoreClient tstoreClient;

    @Mock
    private TstoreClientConfig tstoreClientConfig;

    @Mock
    private Map<Class<?>, CreatedSchemaParams> schemaParamsMapping;

    @InjectMocks
    private TstoreServiceImpl tstoreService;


    @Test
    void testWhenSchemaParamsMapReturnsNullThenCreateFeedThrowsStratosError() {

        Mockito.when(schemaParamsMapping.get(DisputeFeed.class)).thenReturn(null);

        final var disputeFeed = DisputeFeed.builder().build();

        final var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> tstoreService.createFeed("E123", disputeFeed));

        Assertions.assertEquals(StratosErrorCodeKey.FEED_SCHEMA_PARAMS_NOT_FOUND,
            stratosError.getErrorCode());
    }

    @Test
    void testWhenTstoreClientReturnsFalseThenCreateFeedThrowsStratosError() {

        final CreatedSchemaParams params = Mockito.mock(CreatedSchemaParams.class);
        Mockito.when(schemaParamsMapping.get(DisputeFeed.class)).thenReturn(params);

        Mockito.when(tstoreClient.putEntityAndNotify(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(false);

        final var disputeFeed = DisputeFeed.builder().build();

        final var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> tstoreService.createFeed("E123", disputeFeed));

        Assertions.assertEquals(StratosErrorCodeKey.FEED_PUBLISH_ERROR, stratosError.getErrorCode());
    }
}