package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.edc.v1.firstLevel;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import org.junit.jupiter.api.Test;

class EdcFirstLevelFirstLevelMerchantRejectedFlowITest extends EdcFirstLevelBaseTest {

    @Test
    void testFulfillmentCompleted() {

        assertFromCreateToRepresentment();

    }


    @Test
    void testPartialFulfillmentCompleted() {

        createEntrySetup();
        assertRefundBlock();

        assertTriggerEvent(RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
            PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);

        assertTriggerEvent(COMPLETE_PARTIAL_REPRESENTMENT,
            PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext(disputeWorkflow.getDisputedAmount() - 10));

        triggerPartialDebit(disputeWorkflow.getDisputedAmount() - 10);

    }

    @Test
    void testRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMechantNotResponded();
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);

    }

    @Test
    void testPartialRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMechantNotResponded();

        assertTriggerEvent(COMPLETE_PARTIAL_REPRESENTMENT,
            PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext(disputeWorkflow.getDisputedAmount() - 10));

        triggerPartialDebit(disputeWorkflow.getDisputedAmount() - 10);

    }

}
