package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.PgBaseTest;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.END;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

public class PgFirstLevelInternalCheckRejectedFlowIT extends PgBaseTest {

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testFullRefundExistsAndRepresentmentCompleted(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDispute(getDisputeType(), getDisputeStage(), disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute, DisputeWorkflowVersion.V2);

        assertFromCreateToRepresentmentRequirement(Set.of(COMPLETE_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                END,
                10L, TimeUnit.SECONDS);

    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testInternalMidToRepresentment(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDisputeWithInternalMerchant(getDisputeType(), getDisputeStage(),
            disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute,DisputeWorkflowVersion.V2);

        MockingUtils.setupPaymentsApiFullRefundExistsMocking(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(),
            "YBL1234");

        assertFromCreateToInternalMidRepresentmentRequired(Set.of(COMPLETE_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                END,
                10L, TimeUnit.SECONDS);
    }
}
