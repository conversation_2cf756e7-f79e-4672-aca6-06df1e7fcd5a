package com.phonepe.merchant.platform.stratos.server;

import com.aerospike.client.policy.BatchPolicy;
import com.aerospike.client.policy.Policy;
import com.aerospike.client.policy.WritePolicy;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.google.inject.Provider;
import com.google.inject.Stage;
import com.hystrix.configurator.core.HystrixConfigurationFactory;
import com.phonepe.data.provider.rosey.bundle.RoseyConfigProviderBundle;
import com.phonepe.error.configurator.model.ErrorConfiguratorConfig;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.growth.neuron.NeuronBundle;
import com.phonepe.growth.neuron.model.NeuronPulseHandler;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeFeed;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.guice.*;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.ToaDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.neuron.NotionalCreditPulseHandler;
import com.phonepe.merchant.platform.stratos.server.core.neuron.P2pmToaPulseHandler;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.*;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.KratosServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.integrationTests.guice.TestModule;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.NeuronTestUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.checkstatus.impls.CheckChargebackRecoveryAccountingEventStatusActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.checkstatus.impls.CheckChargebackRecoveryReversalAccountingEventStatusActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds.ChargebackRecoveryFeedPublishActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds.ChargebackRecoveryReversalFeedPublishActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.raiseevents.RaiseChargebackRecoveryAccountingEventActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.raiseevents.RaiseChargebackRecoveryReversalAccountingEventActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.queue.EdcChargebackCreationHandlingActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.statemachines.EdcChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.queue.NetBankingChargebackCreationHandlingActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.NetBankingChargebackFirstLevelStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.queue.PgChargebackCreationHandlingActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.PgFirstLevelChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.PgPreArbChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.statemachines.UdirComplaintStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.queue.actors.UpiChargebackCreationHandlingActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.UpiFirstLevelChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.UpiPreArbChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actors.ToaEntryCreationActor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actors.ToaPayRetryActor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.common.actors.ToaPaymentStatusCheckActor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.notionalcredit.statemachine.NotionalCreditStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.actors.P2pmToaPayRetryHandlingActor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.actors.P2pmToaPayStatusCheckActor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.actors.P2pmToaPostEntryHandingActor;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines.P2pmToaStateMachine;
import com.phonepe.merchant.platform.stratos.server.utils.MockedBindingModule;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.merchant.platform.stratos.server.utils.ResourceUtils;
import com.phonepe.merchants.platform.notificationbundle.NotificationModule;
import com.phonepe.merchants.platform.notificationbundle.config.NotificationClientConfig;
import com.phonepe.olympus.im.client.OlympusIMBundle;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.aerospike.bundle.AerospikeBundle;
import com.phonepe.platform.aerospike.config.AerospikeConfiguration;
import com.phonepe.platform.executors.ExecutorUtils;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.platform.schema.models.SchemaParams;
import com.phonepe.platform.schema.utils.SchemaParamUtils;
import com.phonepe.platform.scroll.model.v2.CreatedSchemaParams;
import com.phonepe.platform.scroll.model.v2.Unit;
import com.phonepe.services.warden.models.init.WardenModelsInit;
import com.phonepe.shadow.ShadowModule;
import com.phonepe.tstore.client.TstoreClient;
import com.phonepe.tstore.client.bundle.TstoreClientBundle;
import com.phonepe.verified.kaizen.guice.OncallOpsModule;
import com.phonepe.verified.kaizen.guice.StateMachineModule;
import io.appform.dropwizard.actors.RabbitmqActorBundle;
import io.appform.dropwizard.actors.TtlConfig;
import io.appform.dropwizard.actors.config.RMQConfig;
import io.appform.dropwizard.sharding.DBShardingBundle;
import io.appform.dropwizard.sharding.config.ShardedHibernateFactory;
import io.appform.functionmetrics.FunctionMetricsManager;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryBundle;
import io.appform.testcontainers.aerospike.AerospikeContainerConfiguration;
import io.appform.testcontainers.aerospike.container.AerospikeContainer;
import io.appform.testcontainers.mariadb.config.MariaDbContainerConfiguration;
import io.appform.testcontainers.mariadb.container.MariaDbContainer;
import io.appform.testcontainers.rabbitmq.config.RabbitMQContainerConfiguration;
import io.appform.testcontainers.rabbitmq.container.RabbitMQContainer;
import io.dropwizard.jackson.Jackson;
import io.dropwizard.setup.Bootstrap;
import io.dropwizard.setup.Environment;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.hibernate.SessionFactory;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import uk.org.webcompere.systemstubs.environment.EnvironmentVariables;
import uk.org.webcompere.systemstubs.jupiter.SystemStub;
import uk.org.webcompere.systemstubs.jupiter.SystemStubsExtension;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;

@Slf4j
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(SystemStubsExtension.class)
public abstract class MakerCheckerBaseTest {

    @SystemStub
    private static EnvironmentVariables testWideVariables = new EnvironmentVariables(
            "DC_ID", "nb6",
            "other", "setting");

    private static final String AEROSPIKE_DOCKER_IMAGE = "aerospike/aerospike-server:4.8.0.6";

    private static final String RABBIT_MQ_DOCKER_IMAGE = "rabbitmq:3.11.2-alpine";

    private static final String MARIA_DB_DOCKER_IMAGE = "mariadb:10.5.15-focal";

    protected static final MariaDbContainer MARIA_DB_CONTAINER;

    protected static final AerospikeContainer AEROSPIKE_CONTAINER;

    protected static final RabbitMQContainer RABBIT_MQ_CONTAINER;

    protected static DBShardingBundle<StratosConfiguration> dbShardingBundle;

    protected static NeuronBundle<StratosConfiguration> neuronBundle;

    protected static AerospikeBundle<StratosConfiguration> aerospikeBundle;

    protected static RabbitmqActorBundle<StratosConfiguration> rabbitmqActorBundle;
    protected static final ObjectMapper objectMapper = new ObjectMapper();

    protected static WireMockServer wireMockServer;
    public static KratosServiceImpl kratosService;
    private static EventIngester eventIngester;

    protected static StratosConfiguration stratosConfiguration;
    private static final String configResourceFilePath="config/test-maker-checker.yml";

    protected static Injector guiceInjector;

    static {
        try {
            final var mariaDbContainerConfiguration = new MariaDbContainerConfiguration();
            mariaDbContainerConfiguration.setDockerImage(MARIA_DB_DOCKER_IMAGE);
            mariaDbContainerConfiguration.setWaitTimeoutInSeconds(300L);
            MARIA_DB_CONTAINER = new MariaDbContainer(mariaDbContainerConfiguration);
            MARIA_DB_CONTAINER.start();
            log.info("MariaDB Container Started on Host: {} Port: {}",
                    MARIA_DB_CONTAINER.getContainerIpAddress(), MARIA_DB_CONTAINER.getJdbcPort());

            Assertions.assertTrue(
                    MARIA_DB_CONTAINER.executeQuery("set global max_connections = 10000;", true),
                    "Failed to set max connection limit in MariaDB Container");
        } catch (Exception e) {
            log.error("BaseTest mariadb STATIC failed", e);
            throw e;
        }

    }

    static {
        try {
            final var aerospikeContainerConfig = new AerospikeContainerConfiguration(
                    true,
                    AEROSPIKE_DOCKER_IMAGE,
                    "stratos",
                    "localhost",
                    3000);
            aerospikeContainerConfig.setWaitTimeoutInSeconds(300L);
            AEROSPIKE_CONTAINER = new AerospikeContainer(aerospikeContainerConfig);
            AEROSPIKE_CONTAINER.start();
            log.info("Aerospike Container Started on Port: {}",
                    AEROSPIKE_CONTAINER.getConnectionPort());
        } catch (Exception e) {
            log.error("BaseTest aerospike STATIC failed", e);
            throw e;
        }
    }

    static {
        try {
            final var rabbitMqContainerConfig = new RabbitMQContainerConfiguration();
            rabbitMqContainerConfig.setDockerImage(RABBIT_MQ_DOCKER_IMAGE);
            rabbitMqContainerConfig.setWaitTimeoutInSeconds(300L);
            RABBIT_MQ_CONTAINER = new RabbitMQContainer(rabbitMqContainerConfig);
            RABBIT_MQ_CONTAINER.withStartupCheckStrategy(
                    new IsRunningStartupCheckStrategyWithDelay());
            RABBIT_MQ_CONTAINER.start();
            log.info("RabbitMQ Container Started on Port: {}",
                    RABBIT_MQ_CONTAINER.getConnectionPort());
        } catch (Exception e) {
            log.error("BaseTest rmq STATIC failed", e);
            throw e;
        }

    }



    @BeforeAll
    @SneakyThrows
    public static void setUp() {

        wireMockServer = new WireMockServer(wireMockConfig().dynamicPort());
        wireMockServer.start();
        WireMock.configureFor("localhost", wireMockServer.port());

        final var bootstrap = new Bootstrap<StratosConfiguration>(null);
        stratosConfiguration = getStratosConfiguration();

        ErrorConfiguratorConfig errorConfiguratorConfig =new ErrorConfiguratorConfig() {
            @Override
            public String getErrorPropertiesPath() {
                return "stratos-server/src/main/resources/resourcebundle/";
            }

            @Override
            public String getBaseName() {
                return "resource";
            }
        };
        try {
            URL[] urlsTest = new URL[]{MakerCheckerBaseTest.class.getProtectionDomain().getCodeSource().getLocation().toURI().toURL()};
            log.error("File Name for code source : {}", urlsTest[0].getFile());
            String baseClassPath =urlsTest[0].getFile() + "../../../"+ errorConfiguratorConfig.getErrorPropertiesPath();
            File resourceBundlePath = new File(baseClassPath);


            URL[] urls = new URL[]{resourceBundlePath.toURI().toURL()};
            log.error("File Name with error file: {}", urls[0].getFile());
            stratosConfiguration.setErrorPropertiesPath(urls[0].getFile());
            stratosConfiguration.setBaseName(errorConfiguratorConfig.getBaseName());
            ResourceErrorService<StratosErrorCodeKey> resourceErrorService = new ResourceErrorService<>(errorConfiguratorConfig, resourceBundlePath, StratosErrorCodeKey.class);
            log.error("resourceErrorService  : {}", resourceErrorService);
            DisputeExceptionUtil.init(resourceErrorService);
        } catch (MalformedURLException | URISyntaxException e) {
            e.printStackTrace();
        }


        final var environment = new Environment(
                StratosApplication.APP_NAME,
                bootstrap.getObjectMapper(),
                bootstrap.getValidatorFactory(),
                bootstrap.getMetricRegistry(),
                bootstrap.getClassLoader(),
                bootstrap.getHealthCheckRegistry(),
                stratosConfiguration);

        HystrixConfigurationFactory.init(stratosConfiguration.getHystrixConfig());
        FunctionMetricsManager.initialize("commands", environment.metrics());
        MapperUtils.init(environment.getObjectMapper());

        dbShardingBundle = getDbShardingBundle();
        dbShardingBundle.initialize(bootstrap);
        dbShardingBundle.initBundles(bootstrap);
        dbShardingBundle.runBundles(stratosConfiguration, environment);
        dbShardingBundle.run(stratosConfiguration, environment);

        aerospikeBundle = getAerospikeBundle(bootstrap);
        aerospikeBundle.run(stratosConfiguration, environment);

        rabbitmqActorBundle = getRabbitmqActorBundle();
        rabbitmqActorBundle.run(stratosConfiguration, environment);

        final var roseyConfigProviderBundle = getRoseyConfigProviderBundle();
        roseyConfigProviderBundle.run(stratosConfiguration, environment);

        final var olympusIMBundle = getOlympusIMBundle();

        guiceInjector = getGuiceInjector(
                rabbitmqActorBundle,
                roseyConfigProviderBundle,
                dbShardingBundle, aerospikeBundle,
                environment, stratosConfiguration, bootstrap, olympusIMBundle);

        startManagedInstances();
        startStateMachineInstances();
        startRabbitMqActors();
        setUpStaticContext();
        final WardenModelsInit wardenModelsInit = new WardenModelsInit();
        wardenModelsInit.init();


        var toaProcessorActor = guiceInjector.getInstance(TOAProcessorActor.class);

        neuronBundle = getNeuroneBundle(toaProcessorActor, eventIngester);
        neuronBundle.initialize(bootstrap);

        // Setup Tstore Client return Success upon Feed Publish
        MockingUtils.setupTstoreClient(guiceInjector.getInstance(TstoreClient.class));

        // setup mocking for merchant profile
        MockingUtils.setupGetMerchantProfileApiMocking(TestDataUtils.MERCHANT_ID, "P2P_MERCHANT");
        MockingUtils.setupGetMerchantProfileApiMocking(TestDataUtils.INTERNAL_MERCHANT_ID,
                "P2P_MERCHANT");

        TestDataUtils.init(guiceInjector.getInstance(IdHelper.class));

    }

    private static NeuronBundle<StratosConfiguration> getNeuroneBundle(
            TOAProcessorActor toaProcessorActor, EventIngester eventIngester) {
        return new NeuronBundle<StratosConfiguration>() {
            @Override
            public List<NeuronPulseHandler> getRegisteredPulseHandlers() {
                return List.of(new P2pmToaPulseHandler(new Provider<TOAProcessorActor>() {
                            @Override
                            public TOAProcessorActor get() {
                                return toaProcessorActor;
                            }
                        }, stratosConfiguration.getPulseTypeCellMapping(), eventIngester),
                        new NotionalCreditPulseHandler(new Provider<TOAProcessorActor>() {
                            @Override
                            public TOAProcessorActor get() {
                                return toaProcessorActor;
                            }
                        }, stratosConfiguration.getPulseTypeCellMapping(), eventIngester, guiceInjector.getInstance(DisputeService.class)));
            }
        };
    }

    @SneakyThrows
    private static StratosConfiguration getStratosConfiguration() {
        final var configString = ResourceUtils.getResourceString(configResourceFilePath);
        final var replacedConfigString = configString
                .replaceAll("@@wiremock.port@@", Integer.toString(wireMockServer.port()))
                .replaceAll("@@mariadb.host@@", MARIA_DB_CONTAINER.getContainerIpAddress())
                .replaceAll("@@mariadb.port@@",
                        Integer.toString(MARIA_DB_CONTAINER.getJdbcPort()))
                .replaceAll("@@mariadb.root.password@@", MARIA_DB_CONTAINER.getRootPassword())
                .replaceAll("@@aerospike.host@@", AEROSPIKE_CONTAINER.getContainerIpAddress())
                .replaceAll("@@aerospike.port@@",
                        Integer.toString(AEROSPIKE_CONTAINER.getConnectionPort()))
                .replaceAll("@@rabbitmq.host@@", RABBIT_MQ_CONTAINER.getContainerIpAddress())
                .replaceAll("@@rabbitmq.port@@",
                        Integer.toString(RABBIT_MQ_CONTAINER.getConnectionPort()));
        final var mapper = Jackson.newObjectMapper(new YAMLFactory());
        return mapper.readValue(replacedConfigString, StratosConfiguration.class);
    }

    private static DBShardingBundle<StratosConfiguration> getDbShardingBundle() {

        return new DBShardingBundle<>(
                StratosApplication.APP_NAME,
                List.of("com.phonepe.merchant.platform.stratos.server.core.mariadb.entities",
                        "com.phonepe.verified.kaizen.storage.mariadb.entities",
                        "com.phonepe.stratos.kaizen.storage.mariadb.entities",
                        "com.phonepe.shadow.storage")
        ) {
            @Override
            protected ShardedHibernateFactory getConfig(
                    final StratosConfiguration stratosConfiguration) {
                return stratosConfiguration.getDatabase();
            }
        };
    }

    private static AerospikeBundle<StratosConfiguration> getAerospikeBundle(
            final Bootstrap<StratosConfiguration> bootstrap) {

        return new AerospikeBundle<>() {
            @Override
            protected AerospikeConfiguration configuration(final StratosConfiguration config) {
                return config.getAerospikeConfig().getAerospikeBundleConfig();
            }

            @Override
            protected Policy readPolicy(final StratosConfiguration config) {
                return config.getAerospikeConfig().getReadPolicy();
            }

            @Override
            protected WritePolicy writePolicy(final StratosConfiguration config) {
                return config.getAerospikeConfig().getWritePolicy();
            }

            @Override
            protected BatchPolicy batchPolicy(final StratosConfiguration config) {
                return config.getAerospikeConfig().getBatchPolicy();
            }

            @Override
            public ExecutorService threadPool(final StratosConfiguration config) {
                return ExecutorUtils.newFixedThreadPool(
                        "aerospike",
                        config.getAerospikeConfig().getAerospikeBundleConfig().getThreadPoolSize(),
                        bootstrap.getMetricRegistry());
            }
        };
    }

    private static RabbitmqActorBundle<StratosConfiguration> getRabbitmqActorBundle() {

        return new RabbitmqActorBundle<>() {
            @Override
            protected TtlConfig ttlConfig() {
                return TtlConfig.builder().build();
            }

            @Override
            protected RMQConfig getConfig(final StratosConfiguration config) {
                return config.getRmqConfig();
            }
        };
    }

    private static RoseyConfigProviderBundle<StratosConfiguration> getRoseyConfigProviderBundle() {

        return new RoseyConfigProviderBundle<>() {
            @Override
            public String getRoseyConfigPath(final StratosConfiguration userServiceConfiguration) {
                return "/test/incorrect_path.yml";
            }

            @Override
            public String getRoseyTeamId(final StratosConfiguration userServiceConfiguration) {
                return StratosApplication.ROSEY_TEAM_NAME;
            }

            @Override
            public String getRoseyConfigName(final StratosConfiguration userServiceConfiguration) {
                return StratosApplication.APP_NAME;
            }
        };
    }

    private static Injector getGuiceInjector(
            final RabbitmqActorBundle<StratosConfiguration> rabbitmqActorBundle,
            final RoseyConfigProviderBundle<StratosConfiguration> roseyConfigProviderBundle,
            final DBShardingBundle<StratosConfiguration> dbShardingBundle,
            final AerospikeBundle<StratosConfiguration> aerospikeBundle,
            final Environment environment,
            final StratosConfiguration stratosConfiguration,
            final Bootstrap<StratosConfiguration> bootstrap,
            final OlympusIMBundle<StratosConfiguration> olympusIMBundle) {

        final var serviceDiscoveryBundle = getServiceDiscoveryBundle(stratosConfiguration,environment);
        final var tstoreClientBundle = getTstoreClientBundle();
        final var httpDiscoveryBundle = getHttpDiscoveryBundle(serviceDiscoveryBundle);

        eventIngester = Mockito.mock(EventIngester.class);
        Mockito.doNothing().when(eventIngester).generateEvent(Mockito.any());
        Mockito.doNothing().when(eventIngester).generateExceptionEvent(Mockito.any());

        kratosService = Mockito.mock(KratosServiceImpl.class);

        // by default noop
        MockingUtils.mockKratos(kratosService, KratosRecommendedAction.NOOP);

        return Guice.createInjector(Stage.PRODUCTION, List.of(
                new CoreModule(roseyConfigProviderBundle, bootstrap.getObjectMapper(),
                        bootstrap.getMetricRegistry(), httpDiscoveryBundle),
                new TestModule(environment, stratosConfiguration,serviceDiscoveryBundle),
                new TstoreClientModule(tstoreClientBundle),
                new RabbitmqModule(rabbitmqActorBundle),
                new AerospikeModule(aerospikeBundle),
                new DBModule(dbShardingBundle),
                new OlympusIMModule(olympusIMBundle),
                new MockedBindingModule(kratosService, eventIngester),
                new ConfigModule(),
                new com.phonepe.verified.kaizen.guice.BindingModule(),
                new OncallOpsModule(),
                new StateMachineModule(),
                new ShadowModule<>(dbShardingBundle) {
                },
                new NotificationModule<StratosConfiguration>() {
                    @Override
                    public NotificationClientConfig provideNotificationClientConfig(
                            StratosConfiguration stratosConfiguration) {
                        return stratosConfiguration.getNotificationClientConfig();
                    }
                }
        ));
    }

    @SuppressWarnings("unchecked")
    private static TstoreClientBundle<StratosConfiguration> getTstoreClientBundle() {
        final var schemaNameAndSpace = SchemaParamUtils
                .getSchemaNameAndSpace(DisputeFeed.class);
        final var schemaParams = new SchemaParams(schemaNameAndSpace.getSchemaName(),
                schemaNameAndSpace.getSchemaNamespace(), 1);
        final var unitSchemaNameAndSpace = SchemaParamUtils
                .getSchemaNameAndSpace(Unit.class);
        final var unitSchemaParams = new SchemaParams(unitSchemaNameAndSpace.getSchemaName(),
                unitSchemaNameAndSpace.getSchemaNamespace(), 1);
        final var schemaParamsMapping = Map.of(
                DisputeFeed.class,
                CreatedSchemaParams.newInstance(schemaParams, unitSchemaParams));
        final var tstoreClient = Mockito.mock(TstoreClient.class);
        final var tstoreClientBundle = Mockito.mock(TstoreClientBundle.class);
        Mockito.when(tstoreClientBundle.getTstoreClient()).thenReturn(tstoreClient);
        Mockito.when(tstoreClientBundle.getSchemaParamsMapping()).thenReturn(schemaParamsMapping);
        return tstoreClientBundle;
    }

    @SuppressWarnings("unchecked")
    private static ServiceDiscoveryBundle<StratosConfiguration> getServiceDiscoveryBundle(StratosConfiguration stratosConfiguration,
                                                                                          Environment environment) {
        final var curatorFramework = Mockito.mock(CuratorFramework.class);
        final var serviceDiscoveryBundle = Mockito.mock(ServiceDiscoveryBundle.class);
        Mockito.when(serviceDiscoveryBundle.getCurator()).thenReturn(curatorFramework);
        return serviceDiscoveryBundle;
    }

    private static HttpDiscoveryBundle<StratosConfiguration> getHttpDiscoveryBundle(
            final ServiceDiscoveryBundle<StratosConfiguration> serviceDiscoveryBundle) {
        return new HttpDiscoveryBundle<>() {
            @Override
            protected CuratorFramework getCuratorFramework(final StratosConfiguration configuration) {
                return serviceDiscoveryBundle.getCurator();
            }

            @Override
            protected RangerHubConfiguration getHubConfiguration(final StratosConfiguration configuration) {
                return configuration.getRangerHubConfiguration();
            }
        };
    }

    private static OlympusIMBundle<StratosConfiguration> getOlympusIMBundle() {
        final var olympusIMBundle = Mockito.mock(OlympusIMBundle.class);
        final var olympusImClient = Mockito.mock(OlympusIMClient.class);
        Mockito.when(olympusImClient.getSystemAuthHeader()).thenReturn("AUTH_TOKEN");
        Mockito.when(olympusIMBundle.getOlympusIMClient()).thenReturn(olympusImClient);

        return olympusIMBundle;
    }

    @SneakyThrows
    private static void startManagedInstances() {

        final var eventIngester = guiceInjector.getInstance(EventIngester.class);
        eventIngester.start();
    }

    @SneakyThrows
    private static void startStateMachineInstances() {

        final var upiFirstLevelChargebackStateMachine = guiceInjector
                .getInstance(UpiFirstLevelChargebackStateMachine.class);
        final var upiPreArbChargebackStateMachine = guiceInjector
                .getInstance(UpiPreArbChargebackStateMachine.class);
        final var pgFirstLevelChargebackStateMachine = guiceInjector.getInstance(
                PgFirstLevelChargebackStateMachine.class);
        final var pgPreArbChargebackStateMachine = guiceInjector.getInstance(
                PgPreArbChargebackStateMachine.class);
        final var udirComplaintStateMachine = guiceInjector.getInstance(
                UdirComplaintStateMachine.class);

        final var p2pmToaStateMachine = guiceInjector.getInstance(
                P2pmToaStateMachine.class);
        final var edcChargebackStateMachine = guiceInjector.getInstance(
                EdcChargebackStateMachine.class);
        final var nbFirstLevelChargebackStateMachine = guiceInjector.getInstance(
                NetBankingChargebackFirstLevelStateMachine.class
        );
        final var notionalCreditStateMachine= guiceInjector.getInstance(
                NotionalCreditStateMachine.class);

        upiFirstLevelChargebackStateMachine.start();
        upiPreArbChargebackStateMachine.start();
        pgFirstLevelChargebackStateMachine.start();
        pgPreArbChargebackStateMachine.start();
        udirComplaintStateMachine.start();
        p2pmToaStateMachine.start();
        edcChargebackStateMachine.start();
        nbFirstLevelChargebackStateMachine.start();
        notionalCreditStateMachine.start();
    }

    @SneakyThrows
    private static void startRabbitMqActors() {

        final var chargebackRecoveryFeedPublishActor = guiceInjector
                .getInstance(ChargebackRecoveryFeedPublishActor.class);
        chargebackRecoveryFeedPublishActor.start();

        final var chargebackRecoveryReversalFeedPublishActor = guiceInjector
                .getInstance(ChargebackRecoveryReversalFeedPublishActor.class);
        chargebackRecoveryReversalFeedPublishActor.start();

        final var checkChargebackRecoveryAccountingEventStatusActor = guiceInjector
                .getInstance(CheckChargebackRecoveryAccountingEventStatusActor.class);
        checkChargebackRecoveryAccountingEventStatusActor.start();

        final var checkChargebackRecoveryReversalAccountingEventStatusActor = guiceInjector
                .getInstance(CheckChargebackRecoveryReversalAccountingEventStatusActor.class);
        checkChargebackRecoveryReversalAccountingEventStatusActor.start();

        final var raiseChargebackRecoveryAccountingEventActor = guiceInjector
                .getInstance(RaiseChargebackRecoveryAccountingEventActor.class);
        raiseChargebackRecoveryAccountingEventActor.start();

        final var raiseChargebackRecoveryReversalAccountingEventActor = guiceInjector
                .getInstance(RaiseChargebackRecoveryReversalAccountingEventActor.class);
        raiseChargebackRecoveryReversalAccountingEventActor.start();

        final var upiFirstLevelChargebackCreationHandlingActor = guiceInjector
                .getInstance(UpiChargebackCreationHandlingActor.class);
        upiFirstLevelChargebackCreationHandlingActor.start();

        final var fileProcessorActor = guiceInjector.getInstance(FileProcessorActor.class);
        fileProcessorActor.start();

        final var fileRowProcessorActor = guiceInjector.getInstance(FileRowProcessorActor.class);
        fileRowProcessorActor.start();

        final var pgChargebackCreationHandlingActor = guiceInjector.getInstance(
                PgChargebackCreationHandlingActor.class);
        pgChargebackCreationHandlingActor.start();

        final var pgMisRowProcessorActor = guiceInjector.getInstance(PgMisRowProcessorActor.class);
        pgMisRowProcessorActor.start();

        final var p2pmToaPostEntryHandingActor = guiceInjector.getInstance(
                P2pmToaPostEntryHandingActor.class);
        p2pmToaPostEntryHandingActor.start();

        final var p2pmToaPayStatusCheckActor = guiceInjector.getInstance(
                P2pmToaPayStatusCheckActor.class);
        p2pmToaPayStatusCheckActor.start();

        final var toaProcessorActor = guiceInjector.getInstance(
                TOAProcessorActor.class);
        toaProcessorActor.start();

        final var p2pmToaPayRetryHandlingActor = guiceInjector.getInstance(
                P2pmToaPayRetryHandlingActor.class);
        p2pmToaPayRetryHandlingActor.start();

        final var edcChargebackCreationHandlingActor = guiceInjector.getInstance(
                EdcChargebackCreationHandlingActor.class);
        edcChargebackCreationHandlingActor.start();
        final var edcRowProcessorActor = guiceInjector.getInstance(EdcRowProcessorActor.class);
        edcRowProcessorActor.start();

        final var kratosProcessorActor = guiceInjector.getInstance(KratosProcessorActor.class);
        kratosProcessorActor.start();

        final var nbChargebackCreationHandlingActor = guiceInjector.getInstance(
                NetBankingChargebackCreationHandlingActor.class
        );
        nbChargebackCreationHandlingActor.start();

        final var stateChangeHandlingActor = guiceInjector.getInstance(
                StateChangeHandlerActor.class
        );
        stateChangeHandlingActor.start();

        final var createToaHandlingActor = guiceInjector.getInstance(
                ToaEntryCreationActor.class
        );
        createToaHandlingActor.start();

        final var toaPayStatusCheckHandlingActor = guiceInjector.getInstance(
                ToaPaymentStatusCheckActor.class
        );
        toaPayStatusCheckHandlingActor.start();

        final var toaPayRetryHandlingActor = guiceInjector.getInstance(
                ToaPayRetryActor.class
        );
        toaPayRetryHandlingActor.start();
        final var autoApprovalHandlingActor = guiceInjector.getInstance(
                AutoApprovalActor.class
        );
        autoApprovalHandlingActor.start();
        final var callbackActor = guiceInjector.getInstance(
                CallbackActor.class
        );
        callbackActor.start();

    }

    public static void setUpStaticContext() {
        final var disputeService = guiceInjector.getInstance(DisputeService.class);
        final var fileRepository = guiceInjector.getInstance(FileRepository.class);
        final var rowRepository = guiceInjector.getInstance(RowRepository.class);
        final var disputeMetadataRepository = guiceInjector.getInstance(
                DisputeMetadataRepository.class);
        final var toaDisputeMetadataRepository = guiceInjector.getInstance(
                ToaDisputeMetadataRepository.class);
        final var p2pmToaPulseHandler = guiceInjector.getInstance(P2pmToaPulseHandler.class);
        final var notionalToaPulseHandler = guiceInjector.getInstance(NotionalCreditPulseHandler.class);

        AssertionUtils.init(disputeService, fileRepository, rowRepository,
                disputeMetadataRepository, toaDisputeMetadataRepository);
        NeuronTestUtils.init(p2pmToaPulseHandler, notionalToaPulseHandler);
    }

    protected static void truncateDb() {
        List<String> tablesList = List.of(
                "dispute", "dispute_audit",
                "dispute_workflow", "dispute_workflow_audit",
                "file", "file_audit",
                "row", "row_audit",
                "dispute_metadata", "dispute_metadata_audit"
        );
        StringBuilder query = new StringBuilder();
        tablesList.forEach((value) -> {
            query.append(String.format("TRUNCATE stratos_shard_1.%s;\n", value));
            query.append(String.format("TRUNCATE stratos_shard_2.%s;\n", value));
        });
        MARIA_DB_CONTAINER.executeQuery(query.toString(), true);
    }

    @AfterAll
    @SneakyThrows
    public static void  tearDown() {

        if (Objects.nonNull(dbShardingBundle.getSessionFactories())) {
            dbShardingBundle.getSessionFactories().forEach(SessionFactory::close);
        }

        if (Objects.nonNull(aerospikeBundle.getAerospikeClient())) {
            aerospikeBundle.getAerospikeClient().close();
        }

        if (Objects.nonNull(rabbitmqActorBundle.getConnectionRegistry())) {
            rabbitmqActorBundle.getConnectionRegistry().stop();
        }

        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        RABBIT_MQ_CONTAINER.execInContainer("rabbitmqctl", "purge_queue",
                "test.stratos.PG_MIS_ROW_PROCESSOR");
        RABBIT_MQ_CONTAINER.execInContainer("rabbitmqctl", "purge_queue",
                "test.stratos.PG_MIS_ROW_PROCESSOR_SIDELINE");
    }
}
