package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.edc.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.HOLD;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

class EdcFirstLevelFirstLevelMerchantRejectedFlowITest extends EdcFirstLevelBaseTest {

    @Test
    void testFulfillmentCompleted() {

        assertRepresentmentCompleted();
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                HOLD,
                10L, TimeUnit.SECONDS);
        triggerIndefiniteHoldReconSignal();

    }
    @Test
    void testRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotRespondedWOPartialRep();
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);

    }
}
