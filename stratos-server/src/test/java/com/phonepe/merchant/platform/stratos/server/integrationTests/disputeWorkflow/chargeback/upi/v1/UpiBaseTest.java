package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v1;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCase;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import org.junit.jupiter.api.Assertions;

import java.util.Set;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.*;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED;

public abstract class UpiBaseTest extends ChargebackBaseTestCase {

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.UPI_CHARGEBACK;
    }


    protected void assertFromCreateToMerchantNotResponded() {

        final var permissibleEventsMerchantNotResponding = Set
            .of(COMPLETE_NPCI_REPRESENTMENT, COMPLETE_NPCI_PARTIAL_REPRESENTMENT,
                MERCHANT_ACCEPT_CHARGEBACK);

        assertFromCreateToMerchantNotResponded(permissibleEventsMerchantNotResponding);
    }

    protected void testFromRepresentmentCompletedToCreditReceived() {

        // Test scenario where refund amount is lesser than expected amount
        // Then workflow breaks with error and refund row moves to error row
        final var lessAmountRefundCreditTransitionContext = TestDataUtils
            .getRefundCreditTransitionContext(disputeWorkflow.getDisputedAmount() - 50L);

        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusIMClient);

        final var stratosError = Assertions.assertThrows(DisputeException.class, () ->
            disputeService.triggerEvent(serviceUserPrincipal.getUserAuthDetails(),
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                RECEIVE_CREDIT, lessAmountRefundCreditTransitionContext));
        Assertions.assertEquals(StratosErrorCodeKey.INVALID_CREDIT_AMOUNT,
            stratosError.getErrorCode());

        triggerCreditSignal();
    }

    protected void assertFromCreateToNpciAcceptanceCompleted(KratosRecommendedAction recommendedAction) {
        createEntrySetup(recommendedAction);
        assertRefundBlock();

        //merchant accepted chargeback
        assertTriggerEvent(MERCHANT_ACCEPT_CHARGEBACK, MERCHANT_ACCEPTED_CHARGEBACK);

        // complete NPCI acceptance
        assertTriggerEvent(COMPLETE_NPCI_ACCEPTANCE, NPCI_ACCEPTANCE_COMPLETED);
    }
}
