package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v1.preArb;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED;

import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import org.junit.jupiter.api.Test;

class UpiPreArbResetChargebackFlowITest extends UpiPreArbBaseTest {

    @Test
    void testAcceptedReset() {

        assertFromCreateToNpciAcceptanceCompleted(KratosRecommendedAction.ALLOW);
        testResetChargeback();

    }

    @Test
    void testRepresentmentCompletedReset() {

        createEntrySetup(KratosRecommendedAction.ALLOW);
        assertRefundBlock();
        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);
        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);
        testResetChargeback();

    }
}