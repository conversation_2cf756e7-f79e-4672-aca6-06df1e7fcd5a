package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.PgBaseTest;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.FRAUD_REJECT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.FRAUD_REJECTED_TO_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.SUSPECTED_FRAUD_TO_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ACCEPTANCE_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.END;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FRAUD_REJECTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.SUSPECTED_FRAUD;

public class PgFirstLevelMerchantAcceptedFlowIT extends PgBaseTest {

    @ParameterizedTest
    @EnumSource( names = {"NOOP"})
    void testSuspectedFraudThenNotFraudFlow(KratosRecommendedAction actionAfterSuspected){

        assertFromCreateToSuspectedFraud();

        MockingUtils.mockKratos(kratosService, actionAfterSuspected);

        disputeResource.reconcile(new SuspectedFraudChargebackDisputeReconcileRequest());

        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                MERCHANT_ACCEPTED_CHARGEBACK, 5L, TimeUnit.SECONDS);

        assertTriggerEvent(COMPLETE_ACCEPTANCE, ACCEPTANCE_COMPLETED);
        triggerDebitSignal();
    }

    @Test
    void testFraOpsSuspectedFraudActionToAcceptance(){

        assertFromCreateToSuspectedFraud();

        serviceUserPrincipal = MockingUtils.getAndMockFraOpsUser(olympusIMClient);

        // check upcoming actions that fra ops can take
        final var upcomingEvents = disputeResource
                .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                        disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                        SUSPECTED_FRAUD);

        final var permissibleEvents = Set.of(SUSPECTED_FRAUD_TO_ACCEPTANCE, FRAUD_REJECT);
        Assertions.assertEquals(permissibleEvents, upcomingEvents);

        assertTriggerEvent(SUSPECTED_FRAUD_TO_ACCEPTANCE, MERCHANT_ACCEPTED_CHARGEBACK);

    }

    @Test
    void testFraOpsSuspectedFraudActionToRejected(){

        assertFromCreateToSuspectedFraud();

        serviceUserPrincipal = MockingUtils.getAndMockFraOpsUser(olympusIMClient);

        // check upcoming actions that fra ops can take
        final var upcomingEvents = disputeResource
                .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                        disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                        SUSPECTED_FRAUD);

        final var permissibleEvents = Set.of(SUSPECTED_FRAUD_TO_ACCEPTANCE, FRAUD_REJECT);
        Assertions.assertEquals(permissibleEvents, upcomingEvents);

        assertTriggerEvent(FRAUD_REJECT, FRAUD_REJECTED);

    }

    @ParameterizedTest(name = "{0} {1}")
    @MethodSource("provideArgumentForFraNoActionAndPenaltyAmount")
    void testRecoveryWithoutFraud(KratosRecommendedAction recommendedAction, final long penaltyAmount) {
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute, penaltyAmount, DisputeWorkflowVersion.V2);
        assertAcceptanceToDebitSignal(recommendedAction);
    }

}
