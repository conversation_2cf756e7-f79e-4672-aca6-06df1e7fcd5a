package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v1.firstLevel;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_PARTIAL_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.FRAUD_REJECTED_TO_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FRAUD_REJECTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_PARTIAL_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED;

import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

class UpiFirstLevelMerchantRejectedFlowITest extends UpiFirstLevelBaseTest {

    @ParameterizedTest
    @EnumSource( names = {"NOOP", "ALLOW", "SUSPECT"})
    void testFulfillmentCompletedForNotFraudCases(KratosRecommendedAction recommendedAction) {

        createEntrySetup(recommendedAction);
        assertRefundBlock();
        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);
        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);
        testFromRepresentmentCompletedToCreditReceived();
    }

    @Test
    void testFulfillmentCompletedForFraudRejectedCase(){

        createEntrySetup(KratosRecommendedAction.BLOCK);

        // Fetch the latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            FRAUD_REJECTED,
            20L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        assertTriggerEvent(FRAUD_REJECTED_TO_REPRESENTMENT,NPCI_REPRESENTMENT_COMPLETED );

        testFromRepresentmentCompletedToCreditReceived();
    }

    @ParameterizedTest
    @EnumSource( names = {"NOOP", "ALLOW", "SUSPECT"})
    void testPartialFulfillmentCompletedForNotFraudCases(KratosRecommendedAction recommendedAction){

        createEntrySetup(recommendedAction);
        assertRefundBlock();

        assertTriggerEvent(RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS, PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);

        // do partial representment
        assertTriggerEvent(COMPLETE_NPCI_PARTIAL_REPRESENTMENT,
            NPCI_PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext(disputeWorkflow.getDisputedAmount() - 10));

        triggerPartialCredit(10);


    }

    @Test
    void testRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotResponded();
        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);

    }


    @Test
    void testPartialRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotResponded();

        // do partial representment
        assertTriggerEvent(COMPLETE_NPCI_PARTIAL_REPRESENTMENT,
            NPCI_PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext(disputeWorkflow.getDisputedAmount() - 10));

        triggerPartialCredit(10);

    }

}