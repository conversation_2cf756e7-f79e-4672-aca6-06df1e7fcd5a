package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.edc.v1.firstLevel;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ACCEPTANCE_COMPLETED;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;

public class EdcFirstLevelFirstLevelInternalCheckRejectedFlowITest extends EdcFirstLevelBaseTest {

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testFullRefundExistsAndRepresentmentCompleted(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDispute(getDisputeType(), getDisputeStage(), disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        createAndTestTillRepresentmentRequirement();
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);

    }

    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testSuspectedFraudThenNotFraudFlow(KratosRecommendedAction actionAfterSuspected) {

        assertFromCreateToSuspectedFraud();

        MockingUtils.mockKratos(kratosService, actionAfterSuspected);

//        reconcile
        disputeResource.reconcile(new SuspectedFraudChargebackDisputeReconcileRequest());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            MERCHANT_ACCEPTED_CHARGEBACK,
            5L, TimeUnit.SECONDS);

        // complete NPCI acceptance
        assertTriggerEvent(COMPLETE_ACCEPTANCE,
            ACCEPTANCE_COMPLETED);
        
    }
}
