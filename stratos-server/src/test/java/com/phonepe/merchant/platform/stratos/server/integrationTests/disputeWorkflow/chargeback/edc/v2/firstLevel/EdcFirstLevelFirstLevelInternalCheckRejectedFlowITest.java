package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.edc.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ACCEPTANCE_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

public class EdcFirstLevelFirstLevelInternalCheckRejectedFlowITest extends EdcFirstLevelBaseTest {

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testFullRefundExistsAndRepresentmentCompleted(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDispute(getDisputeType(), getDisputeStage(), disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute, DisputeWorkflowVersion.V2);

        // mock full reversal
        MockingUtils.setEdcTransactionDetails(dispute.getMerchantId(),
                dispute.getTransactionReferenceId(), dispute.getTransactionAmount(),
                dispute.getTransactionAmount(), dispute.getMerchantTransactionId());

        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(),
                dispute.getTransactionAmount(), dispute.getMerchantTransactionId());

        MockingUtils.setEdcUnBlockReversals(dispute.getMerchantTransactionId());

        assertFromCreateToRepresentmentRequirement(Set.of(COMPLETE_REPRESENTMENT));
    }

    @ParameterizedTest
    @EnumSource(names = {"NOOP", "ALLOW"})
    void testSuspectedFraudThenNotFraudFlow(KratosRecommendedAction actionAfterSuspected) {

        assertFromCreateToSuspectedFraud();

        MockingUtils.mockKratos(kratosService, actionAfterSuspected);

//        reconcile
        disputeResource.reconcile(new SuspectedFraudChargebackDisputeReconcileRequest());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            MERCHANT_ACCEPTED_CHARGEBACK,
            5L, TimeUnit.SECONDS);

        // complete NPCI acceptance
        assertTriggerEvent(COMPLETE_ACCEPTANCE,
            ACCEPTANCE_COMPLETED);
        
    }
}
