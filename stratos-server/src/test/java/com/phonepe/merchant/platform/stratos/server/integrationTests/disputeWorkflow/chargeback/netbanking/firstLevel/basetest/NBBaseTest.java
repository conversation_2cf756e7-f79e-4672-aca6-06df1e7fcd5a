package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.netbanking.firstLevel.basetest;

import com.phonepe.merchant.platform.stratos.models.commons.contexts.PartialAcceptanceTransitionContext;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.CallBackResource;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.services.NetBankingChargebackService;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponse;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponseVersion;
import lombok.SneakyThrows;
import com.phonepe.services.refund.orchestrator.models.v1.payer.RefundResponseV2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.provider.Arguments;
import org.opentest4j.AssertionFailedError;

import java.io.ByteArrayInputStream;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.*;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED;

public abstract class NBBaseTest extends ChargebackBaseTestCase {

    protected NetBankingChargebackService netBankingChargebackService;
    protected CallBackResource callBackResource;
    @Override
    protected void baseTestSetup() {

        netBankingChargebackService = guiceInjector.getInstance(NetBankingChargebackService.class);
        callBackResource = guiceInjector.getInstance(CallBackResource.class);
    }

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.NB_CHARGEBACK;
    }

    static Stream<Arguments> provideArgumentWithChargebackCategories() {
        return Stream.of(
            Arguments.of(DisputeCategory.SERVICE_CHARGEBACK),
            Arguments.of(DisputeCategory.FRAUD_CHARGEBACK)
        );
    }

    protected void testTillRepresentmentRequirement() {

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setUpPaymentsApiFullRefundExistsMockingPaidFromNB(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount(),
            "NP1234");
        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        // Assert Status of Dispute Workflow to be Representment Required
        AssertionUtils.assertDisputeWorkflowStateAndCategoryEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REPRESENTMENT_REQUIRED, dispute.getDisputeCategory(),
            5L, TimeUnit.SECONDS);

        // Test Allowed Transitions from Current State
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                DisputeWorkflowState.REPRESENTMENT_REQUIRED);
        Assertions
            .assertEquals(Set.of(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT), upcomingEvents);
    }

    protected void createAndTestTillMerchantAcceptedChargeback(KratosRecommendedAction kratosRecommendedAction){

        createEntrySetup(kratosRecommendedAction);

        assertRefundBlock();
        //merchant accepted chargeback
        assertTriggerEvent(MERCHANT_ACCEPT_CHARGEBACK, MERCHANT_ACCEPTED_CHARGEBACK);

    }

    protected void testTillRefundInitiated(RefundStatus refundStatus) {

        //Setup Refund Initiation API
        MockingUtils.setUpRefundResponseMocking(refundStatus.name(),
            disputeWorkflow.getDisputedAmount());

        assertTriggerEventWithDelay(DisputeWorkflowEvent.CREATE_CHARGEBACK_REFUND,
            DisputeWorkflowState.CB_REFUND_INITIATED, Constants.EMPTY_TRANSITION_CONTEXT, 30L);
    }

    protected void testTillRefundFailure(KratosRecommendedAction kratosRecommendedAction) {

        createAndTestTillMerchantAcceptedChargeback(kratosRecommendedAction);

        MockingUtils.setUpRefundResponseMocking(RefundStatus.FAILED.name(),
            disputeWorkflow.getDisputedAmount());

        testRefundCreatedState();
    }

    protected void testRefundCreatedState() {

        disputeResource.triggerEvent(serviceUserPrincipal,
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.CREATE_CHARGEBACK_REFUND,
            Constants.EMPTY_TRANSITION_CONTEXT);

        Assertions.assertThrows(
            AssertionFailedError.class,
            ()->AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.CB_REFUND_INITIATED));

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_CREATED);

    }

    @SneakyThrows
    protected void triggerCallBackAndAssertStatus(int count) {

        String merchantTxnId = String.format("CB%s-%d",disputeWorkflow.getTransactionReferenceId(),count);

        RefundResponseV2 refundStatus = RefundResponseV2.builder()
            .status(RefundStatus.ACCEPTED)
            .paymentTxnId(disputeWorkflow.getTransactionReferenceId())
            .reversalTxnId(merchantTxnId)
            .version(RefundResponseVersion.V2)
            .build();
        String status = objectMapper.writeValueAsString(refundStatus);
        var olympusClient = guiceInjector.getInstance(OlympusIMClient.class);
        serviceUserPrincipal = MockingUtils.getAndMockCallbackComponent(olympusClient);
        callBackResource.processCallback(serviceUserPrincipal,new ByteArrayInputStream(status.getBytes()), "refund-status");

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.CB_REFUND_ACCEPTED,
            10L, TimeUnit.SECONDS);
        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusClient);
    }

    protected void testTillPartialRepresentmentCompleted() {

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount(),
            "NP1234");
        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        assertRefundBlock();

        assertTriggerEvent(RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
            DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);

        long acceptedAmount = disputeWorkflow.getDisputedAmount() - 50L;

        final var partialAcceptanceTransitionContext = PartialAcceptanceTransitionContext.builder()
            .acceptedAmount(acceptedAmount)
            .build();

        assertTriggerEvent(COMPLETE_PARTIAL_REPRESENTMENT,
            PARTIAL_REPRESENTMENT_COMPLETED, partialAcceptanceTransitionContext);
    }

    protected void testTillPartialRefundFailure() {

        MockingUtils.setUpRefundResponseMocking(RefundStatus.FAILED.name(),
            disputeWorkflow.getDisputedAmount() - 50L);

        testTillPartialRepresentmentCompleted();

        testRefundCreatedState();

    }

}
