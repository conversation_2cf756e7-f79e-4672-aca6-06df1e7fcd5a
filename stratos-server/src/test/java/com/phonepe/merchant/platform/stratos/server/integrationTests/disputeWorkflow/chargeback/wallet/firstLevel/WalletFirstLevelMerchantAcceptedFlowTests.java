package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.wallet.firstLevel;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundStatusReconRequest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.WalletDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.WalletDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import java.time.LocalDateTime;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

class WalletFirstLevelMerchantAcceptedFlowTests extends WalletFirstLevelBaseTest {

    public WalletDisputeMetadataRepository walletDisputeMetadataRepository;
    public DisputeMetadataHelper disputeMetadataHelper;

    @BeforeEach
    void initEach(){
        truncateDb();
        createEntrySetupForAPI("WALLET", String.valueOf(LocalDateTime.now()),
            dispute.getTransactionReferenceId());
        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(disputeWorkflow);
        MockingUtils.setUpRefundResponseMocking(RefundStatus.ACCEPTED.name(),
            financialDisputeWorkflow.getAcceptedAmount());
        MockingUtils.setUpRefundStatusMocking(RefundStatus.ACCEPTED.name(),
            financialDisputeWorkflow.getAcceptedAmount(), dispute.getMerchantId(),
            String.format("CB%s-%s", disputeWorkflow.getTransactionReferenceId(), "F"));
        disputeWorkflow.setDisputeWorkflowVersion(DisputeWorkflowVersion.V1);
        walletDisputeMetadataRepository = guiceInjector.getInstance(WalletDisputeMetadataRepository.class);
        disputeMetadataHelper = guiceInjector.getInstance(DisputeMetadataHelper.class);
        MockingUtils.setupEventIngestionApiMocking();
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void testNpciAckToAcceptedChargeback(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory) {
        disputeService.persistDisputeAndDisputeWorkflow(dispute, disputeWorkflow);
        walletDisputeMetadataRepository.save(disputeMetadataHelper.toWalletDisputeMetadata(
            disputeWorkflow, "123"));

        assertFromCreateToNpciAck(Set.of(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK));
        assertFromNpciAckToCBRefundInitiationComplete();
        WalletDisputeMetadata walletDisputeMetadata = walletDisputeMetadataRepository.selectAllDisputeMetadata(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId()).get(0);

        assertFromCBRefundInitiationCompleteToAcceptedChargeback(RefundStatus.ACCEPTED, DisputeWorkflowState.ACCEPTED_CHARGEBACK, walletDisputeMetadata.getDisbursementTransactionId());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void testNpciAckToAcceptedChargebackWithReconcileAPI(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory) {
        disputeService.persistDisputeAndDisputeWorkflow(dispute, disputeWorkflow);
        walletDisputeMetadataRepository.save(disputeMetadataHelper.toWalletDisputeMetadata(
            disputeWorkflow, "123"));

        assertFromCreateToNpciAck(Set.of(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK));
        assertFromNpciAckToCBRefundInitiationComplete();

        disputeService.reconcile(RefundStatusReconRequest.builder()
            .build());

        assertFromCBRefundInitiationCompleteToAcceptedChargebackWithReconcileAPI(DisputeWorkflowState.ACCEPTED_CHARGEBACK);
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void testNpciAckToMerchantPartialAccepted() {
        disputeService.persistDisputeAndDisputeWorkflow(dispute, disputeWorkflow);
        walletDisputeMetadataRepository.save(disputeMetadataHelper.toWalletDisputeMetadata(
            disputeWorkflow, "123"));
        assertFromCreateToNpciAck(Set.of(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK));

        assertFromCreateToPartiallyAcceptance();
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(API_SERVICE_CATEGORY)
    void testNpciAckToAcceptedChargebackForRefundFail(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory) {
        disputeService.persistDisputeAndDisputeWorkflow(dispute, disputeWorkflow);
        walletDisputeMetadataRepository.save(disputeMetadataHelper.toWalletDisputeMetadata(
            disputeWorkflow, "123"));

        assertFromCreateToNpciAck(Set.of(DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
            DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK));
        assertFromNpciAckToCBRefundInitiationComplete();
        WalletDisputeMetadata walletDisputeMetadata = walletDisputeMetadataRepository.selectAllDisputeMetadata(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId()).get(0);

        assertFromCBRefundInitiationCompleteToAcceptedChargeback(RefundStatus.FAILED, DisputeWorkflowState.CB_REFUND_FAILED, walletDisputeMetadata.getDisbursementTransactionId());
        assertFromRefundFailureToAcceptedFromExternalSource();
    }
}

