package com.phonepe.merchant.platform.stratos.server.integrationTests.makerChecker;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.server.MakerCheckerBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.resources.CallBackResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.services.warden.models.callback.ReviewCallbackRequest;
import com.phonepe.warden.workflow.models.enums.WorkflowInstanceState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.util.concurrent.TimeUnit;

public class FileUploadWardenIntegrationTest extends MakerCheckerBaseTest {

    private FileResource fileResource;
    private CallBackResource callBackResource;

    @BeforeEach
    void setUpGuiceInjection() {
        fileResource = guiceInjector.getInstance(FileResource.class);
        callBackResource = guiceInjector.getInstance(CallBackResource.class);
        truncateDb();
    }
    @Test
    public void testFileUpload() throws Exception {
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupWardenWorkflowCreationSuccess();

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
                TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
                FormDataContentDisposition.name("randomName").fileName("file.csv")
                        .build(), "AuthToken",serviceUserPrincipal);
        AssertionUtils.assertFileAccepted(response.getId());
    }

    @Test
    public void testFileUploadWhenWardenFails() throws Exception {
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupWardenWorkflowCreationFailed();

        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        final var thrown = Assertions.assertThrows(DisputeException.class,
                ()-> fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
                TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
                FormDataContentDisposition.name("randomName").fileName("file.csv")
                        .build(), "AuthToken",serviceUserPrincipal));

        Assertions.assertEquals("Internal Communication Error", thrown.getMessage());
    }
    @Test
    public void testCallback() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("P").getId();

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();
        MockingUtils.setupPaymentsApiFullRefundExistsMocking(transactionReferenceId, 100, "YBL123");
        MockingUtils.setupEventIngestionApiMocking();

        MockingUtils.setupWardenWorkflowCreationSuccess();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(), "AuthToken",serviceUserPrincipal);
        MockingUtils.setupWardenDetails(TestDataUtils.WARDEN_WF_ID, WorkflowInstanceState.APPROVED, response.getId());

        ReviewCallbackRequest callbackRequest = ReviewCallbackRequest.builder()
                .workflowId(TestDataUtils.WARDEN_WF_ID)
                .build();
        String status = objectMapper.writeValueAsString(callbackRequest);
        var olympusClient = guiceInjector.getInstance(OlympusIMClient.class);
        ServiceUserPrincipal principal = MockingUtils.getAndMockCallbackComponent(olympusClient);
        callBackResource.processCallback(principal, new ByteArrayInputStream(status.getBytes()), "warden-callback");
        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);
    }

    @Test
    public void testCallbackWhenRequestRejected() throws Exception {

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();

        MockingUtils.setupWardenWorkflowCreationSuccess();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
                TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
                FormDataContentDisposition.name("randomName").fileName("file.csv")
                        .build(), "AuthToken",serviceUserPrincipal);
        MockingUtils.setupWardenDetails(TestDataUtils.WARDEN_WF_ID, WorkflowInstanceState.REJECTED, response.getId());

        ReviewCallbackRequest callbackRequest = ReviewCallbackRequest.builder()
                .workflowId(TestDataUtils.WARDEN_WF_ID)
                .build();
        String status = objectMapper.writeValueAsString(callbackRequest);
        var olympusClient = guiceInjector.getInstance(OlympusIMClient.class);
        ServiceUserPrincipal principal = MockingUtils.getAndMockCallbackComponent(olympusClient);
        callBackResource.processCallback(principal, new ByteArrayInputStream(status.getBytes()), "warden-callback");
        AssertionUtils.assertFileRejected(response.getId());
    }

    @Test
    public void testCallbackWhenRequestFailed() throws Exception {

        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForCreateChargebackApiMocking();

        MockingUtils.setupWardenWorkflowCreationSuccess();
        final var serviceUserPrincipal = TestDataUtils.getOlympusUser();

        var response = fileResource.upload(DisputeTypeDto.UPI_CHARGEBACK, FileTypeDto.YES,
                TestDataUtils.UPIFirstLevelChargebackCSVFileStream(),
                FormDataContentDisposition.name("randomName").fileName("file.csv")
                        .build(), "AuthToken",serviceUserPrincipal);
        MockingUtils.setupWardenDetails(TestDataUtils.WARDEN_WF_ID, WorkflowInstanceState.FAILED, response.getId());

        ReviewCallbackRequest callbackRequest = ReviewCallbackRequest.builder()
                .workflowId(TestDataUtils.WARDEN_WF_ID)
                .build();
        String status = objectMapper.writeValueAsString(callbackRequest);
        var olympusClient = guiceInjector.getInstance(OlympusIMClient.class);
        ServiceUserPrincipal principal = MockingUtils.getAndMockCallbackComponent(olympusClient);

        callBackResource.processCallback(principal, new ByteArrayInputStream(status.getBytes()), "warden-callback");
        AssertionUtils.assertFileFailed(response.getId());
    }

}
