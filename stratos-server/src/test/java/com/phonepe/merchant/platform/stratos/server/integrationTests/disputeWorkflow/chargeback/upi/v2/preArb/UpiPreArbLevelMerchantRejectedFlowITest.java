package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v2.preArb;

import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.FRAUD_REJECTED_TO_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FRAUD_REJECTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.HOLD;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

class UpiPreArbLevelMerchantRejectedFlowITest extends UpiPreArbBaseTest {

    @ParameterizedTest
    @EnumSource( names = {"ALLOW", "NOOP", "SUSPECT"})
    void testFulfillmentCompletedForNotFraudCases(KratosRecommendedAction recommendedAction) throws InterruptedException {
        createEntrySetup(recommendedAction);
        assertRefundBlock();
        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                HOLD,
            20L, TimeUnit.SECONDS);
    }

    @Test
    void testFulfillmentCompletedForFraudRejectedCase(){

        createEntrySetup(KratosRecommendedAction.BLOCK);

        // Fetch the latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            FRAUD_REJECTED,
            20L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        assertTriggerEvent(FRAUD_REJECTED_TO_REPRESENTMENT,REPRESENTMENT_COMPLETED );

        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                HOLD,
                20L, TimeUnit.SECONDS);
    }

    @ParameterizedTest
    @EnumSource( names = {"NOOP"})
            //, "ALLOW", "SUSPECT"})
    void testPartialFulfillmentCompletedForNotFraudCases(KratosRecommendedAction recommendedAction){

        createEntrySetup(recommendedAction);
        assertRefundBlock();

        assertTriggerEvent(RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS, PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);

        // do partial representment
        assertTriggerEvent(COMPLETE_PARTIAL_REPRESENTMENT,
            PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext());

        triggerPartialDebit(TestDataUtils.partialAcceptanceAmount);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                HOLD,
                20L, TimeUnit.SECONDS);
    }

    @Test
    void testRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotRespondedWithPartialRep();
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);

    }


    @Test
    void testPartialRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotRespondedWithPartialRep();

        // do partial representment
        assertTriggerEvent(COMPLETE_PARTIAL_REPRESENTMENT,
            PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext());
    }
    @ParameterizedTest
    @EnumSource( names = {"ALLOW"})
    void testIndefiniteHoldToEnd(KratosRecommendedAction recommendedAction) throws InterruptedException {
        createEntrySetup(recommendedAction);
        assertRefundBlock();
        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                HOLD,
                20L, TimeUnit.SECONDS);
        triggerIndefiniteHoldReconSignal();
    }
    @Test
    void testUpiPreArbRaiseWithPrevOpenITest(){
        dispute.setCurrentDisputeStage(DisputeStage.FIRST_LEVEL);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute, DisputeWorkflowVersion.V2);
        createEntrySetup(KratosRecommendedAction.ALLOW);
        assertRefundBlock();
        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
        testFromRepresentmentCompletedToCreditReceived();
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                HOLD,
                20L, TimeUnit.SECONDS);
        String disputeWorkflowIdFL = disputeWorkflow.getDisputeWorkflowId();
/*
        dispute.setCurrentDisputeStage(DisputeStage.PRE_ARBITRATION);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute, DisputeWorkflowVersion.V2);
        createEntrySetup(KratosRecommendedAction.ALLOW);
        assertRefundBlock();

        disputeService.getDisputeWorkflow(disputeWorkflowIdFL);
*/
    }


}