package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.preArb;


import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.firstLevel.PgFirstLevelMerchantAcceptedFlowIT;

/*
PG Pre arb is same as first level hence same test cases
*/
public class PgPreArbMerchantAcceptedFlowIT extends PgFirstLevelMerchantAcceptedFlowIT {

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.PRE_ARBITRATION;
    }
}
