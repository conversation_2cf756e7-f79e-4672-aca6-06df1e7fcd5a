package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCase;
import java.util.Set;

public abstract class PgBaseTest extends ChargebackBaseTestCase {

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.FIRST_LEVEL;
    }

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.PG_CHARGEBACK;
    }

    protected void createAndTestTillPgAcceptance() {
        createEntrySetup();
        assertRefundBlock();

        //merchant accepted chargeback
        assertTriggerEvent(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);

        // complete PG acceptance
        assertTriggerEvent(DisputeWorkflowEvent.COMPLETE_PG_ACCEPTANCE,
            DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED);
    }

    protected void createAndTestTillPgRepresentment() {
        createEntrySetup();
        assertRefundBlock();

        // receiving Fulfillment Documents from Merchant
        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);

        // Completing Representment on pg
        assertTriggerEvent(COMPLETE_PG_REPRESENTMENT, PG_REPRESENTMENT_COMPLETED);
    }

    protected void createAndTestTillMerchantNotResponded() {

        final var permissibleEventsMerchantNotResponding = Set
            .of(DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT,
                DisputeWorkflowEvent.COMPLETE_PG_PARTIAL_REPRESENTMENT,
                DisputeWorkflowEvent.COMPLETE_PG_ACCEPTANCE);

        assertFromCreateToMerchantNotResponded(permissibleEventsMerchantNotResponding);

    }
}
