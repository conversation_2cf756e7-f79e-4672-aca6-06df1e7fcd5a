package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback;

import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.CommentContext;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DisputeFilterParams;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTestV2;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.TransitionLockKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeResource;
import com.phonepe.merchant.platform.stratos.server.core.services.AccountingEventService;
import com.phonepe.merchant.platform.stratos.server.core.services.AmountHoldService;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AccountingEventUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.resources.ChargebackResource;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.provider.Arguments;
import org.mockito.Mockito;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.APPROVE_ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_HOLD;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_CREDIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_DEBIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_CREDIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REJECT_ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REJECT_RECOVER_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RESET_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.REVERSE_ABSORB_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ACCEPTANCE_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CHARGEBACK_ABSORBED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CREDIT_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.DEBIT_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.END;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACTION_REQUESTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.RECOVER_HOLD_EVENT_ACCEPTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_REQUIRED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.SUSPECTED_FRAUD;

public abstract class ChargebackBaseTestCaseV2 extends LoadOnlyOnClassLevelBaseTestV2 {


    protected DisputeService disputeService;
    protected AmountHoldService amountHoldService;
    protected DisputeResource disputeResource;
    protected AccountingEventService accountingEventService;
    protected Dispute dispute;
    protected DisputeWorkflow disputeWorkflow;
    protected OlympusIMClient olympusIMClient;
    protected ServiceUserPrincipal serviceUserPrincipal;
    protected ChargebackResource chargebackResource;

    public static final String CATEGORY_ARGS = "provideArgumentWithChargebackCategories";
    public static final String PENALTY_AMOUNT_ARGS = "provideArgumentWithPenaltyAmount";

    static Stream<Arguments> provideArgumentWithChargebackCategories() {
        return Stream.of(
            Arguments.of(DisputeCategory.SERVICE_CHARGEBACK),
            Arguments.of(DisputeCategory.FRAUD_CHARGEBACK),
            Arguments.of(DisputeCategory.DIFFERED_CHARGEBACK),
            Arguments.of(DisputeCategory.DUPLICATE_CHARGEBACK)
        );
    }

    static Stream<Arguments> provideArgumentForFraNoActionAndPenaltyAmount() {
        return Stream.of(
            Arguments.of(KratosRecommendedAction.NOOP, 0L),
            Arguments.of(KratosRecommendedAction.NOOP, 50L),
            Arguments.of(KratosRecommendedAction.ALLOW, 0L),
            Arguments.of(KratosRecommendedAction.ALLOW, 50L)
        );
    }


    static Stream<Arguments> provideArgumentWithPenaltyAmount() {
        return Stream.of(
            Arguments.of(0L),
            Arguments.of(50L)
        );
    }

    @BeforeEach
    void initBeforeEach() {
        truncateDb();
        //purgeQueues();

        disputeService = guiceInjector.getInstance(DisputeService.class);
        amountHoldService = guiceInjector.getInstance(AmountHoldService.class);
        accountingEventService = guiceInjector.getInstance(AccountingEventService.class);
        disputeResource = guiceInjector.getInstance(DisputeResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        chargebackResource = guiceInjector.getInstance(ChargebackResource.class);
        final var olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);

        // default is set to merchant ops
        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusIMClient);

        dispute = TestDataUtils
            .getServiceChargeBackDispute(getDisputeType(), getDisputeStage());
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute, DisputeWorkflowVersion.V2);

        baseTestSetup();
    }

    protected void clearAerospike() {
        final TransitionLockCommand transitionLockCommand = guiceInjector.getInstance(TransitionLockCommand.class);
        transitionLockCommand.delete(TransitionLockKey.builder()
            .transitionKey(disputeWorkflow.getDisputeWorkflowId())
            .build());
    }

    protected void baseTestSetup() {

    }

    protected abstract DisputeStage getDisputeStage();

    protected abstract DisputeType getDisputeType();

    protected void assertRefundBlock() {
        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            MERCHANT_ACTION_REQUESTED,
            20L, TimeUnit.SECONDS);
        //check if amount on hold is present in aerospike
        assertAmountHold(dispute.getTransactionReferenceId(),disputeWorkflow.getDisputedAmount());

        //check if accounting event exist
        String holdEventId = AccountingEventUtils.getHoldEventId(
                disputeWorkflow.getTransactionReferenceId(),
                EventGenerationType.HOLD_RECOVERY,
                disputeWorkflow.getDisputedAmount());
        List<AccountingEvent> accountingEventList = accountingEventService.getAccountingEvents(disputeWorkflow.getDisputeWorkflowId());
        boolean eventExist = accountingEventList.stream().anyMatch(event -> event.getHeader().getTransactionId().equals(holdEventId));
        Assertions.assertTrue(eventExist);

        final var permissibleEvents = Set
            .of(RECEIVE_FULFILMENT_DOCUMENTS,
                RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
                MERCHANT_ACCEPT_CHARGEBACK,
                NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL);
        assertRefundBlock(permissibleEvents);
    }
    protected void assertRefundBlock(Set<DisputeWorkflowEvent> permissibleEvents){
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                MERCHANT_ACTION_REQUESTED,
                10L, TimeUnit.SECONDS);
        // Test Allowed Transitions from Current State
        final var upcomingEvents = disputeResource
                .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                        disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                MERCHANT_ACTION_REQUESTED);
        Assertions.assertEquals(permissibleEvents, upcomingEvents);
    }


    protected void assertTriggerEvent(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state) {
        assertTriggerEvent(event, state, Constants.EMPTY_TRANSITION_CONTEXT);
    }

    protected void assertTriggerEvent(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state, final TransitionContext transitionContext) {

        // Trigger Event
        disputeResource.triggerEvent(
            serviceUserPrincipal,
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            event, transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflow(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            state, dispute.getDisputeCategory(), disputeWorkflow.getDisputeType());
    }

    protected void assertTriggerEventWithDelay(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state) {
        assertTriggerEventWithDelay(event, state, Constants.EMPTY_TRANSITION_CONTEXT);
    }

    protected void assertTriggerEventWithDelay(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state, final TransitionContext transitionContext) {

        // Trigger Event
        disputeResource.triggerEvent(
            serviceUserPrincipal,
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            event, transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            state, 10L, TimeUnit.SECONDS);
    }

    protected void assertTriggerEventWithDelay(final DisputeWorkflowEvent event,
        final DisputeWorkflowState state, final TransitionContext transitionContext, final long delay) {

        // Trigger Event
        disputeResource.triggerEvent(serviceUserPrincipal,
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            event,
            transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            state, delay, TimeUnit.SECONDS);

    }
    protected void createEntrySetup() {
        createEntrySetup(KratosRecommendedAction.ALLOW);
    }

    protected void createEntrySetup(KratosRecommendedAction recommendedAction) {
        // This is needed as money movement is done right after dispute is registered
        MockingUtils.setupPlutusEventIngestionApiMocking(disputeWorkflow);
        MockingUtils.setupPlutusStatusCheckApiMocking(disputeWorkflow);

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiMockingSubmitFulfillmentDocument(
            dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount());
        MockingUtils.mockKratos(kratosService, recommendedAction);

        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);


    }


    protected void testAbsorbtionFlow() {

        CommentContext commentContext = CommentContext.builder()
            .comment("Chargeback absorb required")
            .build();

        // Merchant Ops requests for Absorb Chargeback with Comment
        assertTriggerEvent(REQUEST_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REQUESTED,
            commentContext);

        // Reject absorb
        assertTriggerEvent(REJECT_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REJECTED,
            commentContext);

        //request absorb
        assertTriggerEvent(REQUEST_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REQUESTED,
            commentContext);

        // finance manager approve absorb request
        serviceUserPrincipal = MockingUtils.getAndMockFinanceManagerUser(olympusIMClient);
        assertTriggerEvent(APPROVE_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_APPROVED,
            commentContext);

        // finance ops  marks Chargeback as Absorbed
        serviceUserPrincipal = MockingUtils.getAndMockFinanceOpsUser(olympusIMClient);
        assertTriggerEvent(ABSORB_CHARGEBACK, CHARGEBACK_ABSORBED,
            commentContext);

        // finance ops  marks Chargeback Absorbed reversed
        serviceUserPrincipal = MockingUtils.getAndMockFinanceOpsUser(olympusIMClient);
        assertTriggerEvent(REVERSE_ABSORB_CHARGEBACK, CHARGEBACK_ABSORB_REVERSED,
            commentContext);

    }

    protected void testRecoveryFlow() {

        // Setup Plutus Ingestor and Status Check API to respond 204
        MockingUtils.setupPlutusEventIngestionApiMocking(disputeWorkflow);
        MockingUtils.setupPlutusStatusCheckApiMocking(disputeWorkflow);

        CommentContext commentContext = CommentContext.builder()
            .comment("Chargeback absorb required")
            .build();

        // Merchant Ops requests for recovery
        assertTriggerEvent(REQUEST_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REQUESTED,
            commentContext);

        // Reject recovery
        assertTriggerEvent(REJECT_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REJECTED,
            commentContext);

        // request absorb
        assertTriggerEvent(REQUEST_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REQUESTED,
            commentContext);

        // Reject absorb
        assertTriggerEvent(REJECT_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REJECTED,
            commentContext);

        //request recovery
        assertTriggerEvent(REQUEST_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REQUESTED,
            commentContext);

        // Finance Manager approves Recovery of Chargeback
        serviceUserPrincipal = MockingUtils.getAndMockFinanceManagerUser(olympusIMClient);
        assertTriggerEventWithDelay(APPROVE_RECOVER_CHARGEBACK,
            RECOVER_CHARGEBACK_EVENT_ACCEPTED, commentContext);

        serviceUserPrincipal = MockingUtils.getAndMockFinanceOpsUser(olympusIMClient);
        assertTriggerEventWithDelay(REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK,
            REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED, commentContext);

        assertTriggerEventWithDelay(REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK,
            RECOVER_CHARGEBACK_EVENT_ACCEPTED, commentContext);

        assertTriggerEventWithDelay(REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK,
            REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED, commentContext);

        serviceUserPrincipal = MockingUtils.getAndMockFinanceManagerUser(olympusIMClient);
        assertTriggerEventWithDelay(APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK,
            REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED, commentContext);
    }

    protected void testResetChargeback() {

        serviceUserPrincipal = MockingUtils.getAndMockMerchantManagerUser(olympusIMClient);

        // reset chargeback
        assertTriggerEvent(RESET_CHARGEBACK, MERCHANT_ACTION_REQUESTED, CommentContext.builder()
                .comment("Wrong entry")
                .build()
        );

        final var dw = disputeService
            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                getDisputeType(), getDisputeStage());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
            MERCHANT_ACTION_REQUESTED, 5L, TimeUnit.SECONDS);
        Assertions.assertEquals(0L,
            ((FinancialDisputeWorkflow) dw).getAcceptedAmount());
        Assertions.assertEquals(disputeWorkflow.getDisputedAmount(),
            dw.getDisputedAmount());
    }

    protected void triggerPartialDebit(final long debitAmount) {
        // partial debit
        MockingUtils.mockOlympusPermission("chargeback/transition-receive_partial_debit",
            olympusIMClient);
        assertTriggerEvent(RECEIVE_PARTIAL_DEBIT,
            PARTIAL_DEBIT_RECEIVED, TestDataUtils.getDebitTransitionContext(debitAmount));
    }

    protected void triggerPartialCredit(final long creditAmount) {

        MockingUtils.mockOlympusPermission("chargeback/transition-receive_partial_credit",
            olympusIMClient);
        assertTriggerEvent(RECEIVE_PARTIAL_CREDIT,
            PARTIAL_CREDIT_RECEIVED, TestDataUtils.getRefundCreditTransitionContext(creditAmount));
    }

    protected void triggerDebitSignal() {

        final var debitTransitionContext = TestDataUtils
            .getDebitTransitionContext(dispute.getTransactionAmount());

        MockingUtils.mockOlympusPermission("chargeback/transition-receive_debit", olympusIMClient);
        assertTriggerEventWithDelay(RECEIVE_DEBIT, DEBIT_RECEIVED, debitTransitionContext);

    }
    protected void triggerIndefiniteHoldReconSignal() {
        MockingUtils.mockOlympusPermission("chargeback/transition-approve_reversal_of_recovered_hold", olympusIMClient);
        assertTriggerEventWithDelay(APPROVE_REVERSAL_OF_RECOVERED_HOLD, END, Constants.EMPTY_TRANSITION_CONTEXT);

    }

    protected void triggerCreditSignal() {

        final var refundCreditTransitionContext = TestDataUtils
                .getRefundCreditTransitionContext(dispute.getTransactionAmount());

        // credit received
        Mockito.when(olympusIMClient.verifyPermission(Mockito.any(),
                Mockito.eq("chargeback/transition-receive_credit"))).thenReturn(true);
        assertTriggerEvent(RECEIVE_CREDIT, CREDIT_RECEIVED, refundCreditTransitionContext);
    }
    protected void assertFromCreateToMerchantNotRespondedWithPartialRep() {
        final var permissibleEventsMerchantNotResponding = Set
            .of(COMPLETE_REPRESENTMENT, COMPLETE_PARTIAL_REPRESENTMENT,
                    MERCHANT_ACCEPT_CHARGEBACK);
        assertFromCreateToMerchantNotResponded(permissibleEventsMerchantNotResponding);
    }

    protected void assertFromCreateToMerchantNotRespondedWOPartialRep() {
        final var permissibleEventsMerchantNotResponding = Set
                .of(COMPLETE_REPRESENTMENT, MERCHANT_ACCEPT_CHARGEBACK);
        assertFromCreateToMerchantNotResponded(permissibleEventsMerchantNotResponding);
    }

    protected void assertFromCreateToMerchantNotResponded(
        final Set<DisputeWorkflowEvent> permissibleEventsMerchantNotResponding) {

        createEntrySetup(KratosRecommendedAction.ALLOW);
        assertRefundBlock();

        assertTriggerEventWithDelay(NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL, MERCHANT_NOT_RESPONDED_WITHIN_TTL);

        final var upcomingEventsMerchantNotResponding = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                MERCHANT_NOT_RESPONDED_WITHIN_TTL);
        Assertions.assertEquals(permissibleEventsMerchantNotResponding,
            upcomingEventsMerchantNotResponding);
    }

    protected void assertFromCreateToRepresentmentRequirement(
        final Set<DisputeWorkflowEvent> expectedUpcomingEvents) {

        MockingUtils.setupPaymentsApiFullRefundExistsMocking(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount(), "YBL1234");

        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        // Assert Status of Dispute Workflow to be Representment Required
        AssertionUtils.assertDisputeWorkflowStateAndCategoryEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            REPRESENTMENT_REQUIRED, dispute.getDisputeCategory(), 5L, TimeUnit.SECONDS);

        // Test Allowed Transitions from Current State
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                REPRESENTMENT_REQUIRED);
        Assertions
            .assertEquals(expectedUpcomingEvents, upcomingEvents);
    }

    protected void assertFromCreateToInternalMidRepresentmentRequired(
        Set<DisputeWorkflowEvent> expectedUpcomingEvents) {
        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        // Assert Status of Dispute Workflow to be Internal Merchant Representment Required
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            INTERNAL_MID_REPRESENTMENT_REQUIRED, 5L, TimeUnit.SECONDS);

        // Test Allowed Transitions from Current State
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                INTERNAL_MID_REPRESENTMENT_REQUIRED);

        // Assert Status of Dispute Workflow to be Complete NPCI Representment
        Assertions.assertEquals(expectedUpcomingEvents, upcomingEvents);
    }
    protected void assertFromCreateToSuspectedFraud(){
        createEntrySetup(KratosRecommendedAction.SUSPECT);
        assertRefundBlock();
        //merchant accepted chargeback but was marked suspected
        assertTriggerEventWithDelay(MERCHANT_ACCEPT_CHARGEBACK, SUSPECTED_FRAUD);
    }

    protected void testAutoApprovalFlow() {

        // Setup Plutus Ingestor and Status Check API to respond 204
        MockingUtils.setupPlutusEventIngestionApiMocking(disputeWorkflow);
        MockingUtils.setupPlutusStatusCheckApiMocking(disputeWorkflow);

        CommentContext commentContext = CommentContext.builder()
                .comment("Chargeback recovery required")
                .build();

        assertTriggerEvent(REQUEST_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REQUESTED,
                commentContext);


        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                RECOVER_CHARGEBACK_EVENT_ACCEPTED, 10L, TimeUnit.SECONDS);

    }
    protected void assertAcceptanceCompleted(){
        assertAcceptanceCompleted(KratosRecommendedAction.ALLOW);
    }

    protected void assertAcceptanceCompleted(KratosRecommendedAction recommendedAction){
        createEntrySetup(recommendedAction);
        assertRefundBlock();

        //merchant accepted chargeback
        assertTriggerEvent(MERCHANT_ACCEPT_CHARGEBACK, MERCHANT_ACCEPTED_CHARGEBACK);

        // complete NPCI acceptance
        assertTriggerEvent(COMPLETE_ACCEPTANCE, ACCEPTANCE_COMPLETED);

    }

    protected void assertAcceptanceToDebitSignal(KratosRecommendedAction recommendedAction) {
        assertAcceptanceCompleted(recommendedAction);
        triggerDebitSignal();
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                END,
                10L, TimeUnit.SECONDS);

    }

    protected void assertRepresentmentCompleted() {
        assertRepresentmentCompleted(KratosRecommendedAction.ALLOW);
    }

    protected void assertRepresentmentCompleted(KratosRecommendedAction action) {
        createEntrySetup(action);
        assertRefundBlock();

        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
    }
    protected void assertAmountHold(String transactionId, long amount){
        Assertions.assertEquals(amountHoldService.getHoldAmount(transactionId),amount);
    }
    protected void assertNoAmountHold(String transactionId) {
        Assertions.assertFalse(amountHoldService.holdExist(transactionId));
    }

    protected void assertNoAccountingEvent(String disputeWorkflowId) {
        Assertions.assertEquals(accountingEventService.getAccountingEvents(disputeWorkflowId).size(),0);
    }

}