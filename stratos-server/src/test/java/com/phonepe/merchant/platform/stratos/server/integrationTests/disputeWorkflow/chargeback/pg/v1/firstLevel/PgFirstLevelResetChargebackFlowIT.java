package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.firstLevel;

import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.PgBaseTest;
import org.junit.jupiter.api.Test;

public class PgFirstLevelResetChargebackFlowIT extends PgBaseTest {

    @Test
    void testAcceptedReset() {

        createAndTestTillPgAcceptance();
        testResetChargeback();
    }

    @Test
    void testRepresentmentCompletedReset() {

        createAndTestTillPgRepresentment();
        testResetChargeback();

    }
}
