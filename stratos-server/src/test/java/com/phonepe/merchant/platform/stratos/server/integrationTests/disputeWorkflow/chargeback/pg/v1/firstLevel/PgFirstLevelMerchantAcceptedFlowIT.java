package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.firstLevel;

import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.PgBaseTest;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

public class PgFirstLevelMerchantAcceptedFlowIT extends PgBaseTest {


    @Test
    void testCreateAndAbsorbtionFlow() {

        createAndTestTillPgAcceptance();
        triggerDebitSignal();
        testAbsorbtionFlow();

    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(PENALTY_AMOUNT_ARGS)
    void testCreateAndRecovery(final long penaltyAmount) {

        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute, penaltyAmount);

        createAndTestTillPgAcceptance();
        triggerDebitSignal();
        testRecoveryFlow();
    }

}
