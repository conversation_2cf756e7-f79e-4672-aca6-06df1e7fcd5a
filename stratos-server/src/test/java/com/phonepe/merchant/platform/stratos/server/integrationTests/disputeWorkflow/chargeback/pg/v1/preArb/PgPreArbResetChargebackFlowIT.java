package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.preArb;


import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.firstLevel.PgFirstLevelResetChargebackFlowIT;

/*
PG Pre arb is same as first level hence same test cases
*/
public class PgPreArbResetChargebackFlowIT extends PgFirstLevelResetChargebackFlowIT {

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.PRE_ARBITRATION;
    }
}
