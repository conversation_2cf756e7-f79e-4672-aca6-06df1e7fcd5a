package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v1.preArb;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_PARTIAL_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_PARTIAL_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

public class UpiPreArbInternalCheckRejectedFlowITest extends UpiPreArbBaseTest {

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testRepresentmentCompletedThenCreditReceived(DisputeCategory disputeCategory) {
        dispute = TestDataUtils.getDispute(getDisputeType(), getDisputeStage(), disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        assertFromCreateToRepresentmentRequirement(Set.of(COMPLETE_NPCI_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);

    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testInternalMidToRepresentmentCompleted(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDisputeWithInternalMerchant(getDisputeType(), getDisputeStage(),
            disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiFullRefundExistsMocking(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(),
            "YBL1234");

        assertFromCreateToInternalMidRepresentmentRequired(Set.of(COMPLETE_NPCI_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);

    }

    @Test
    void testRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotResponded();
        assertTriggerEvent(COMPLETE_NPCI_REPRESENTMENT, NPCI_REPRESENTMENT_COMPLETED);

    }


    @Test
    void testPartialRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotResponded();

        // do partial representment
        assertTriggerEvent(COMPLETE_NPCI_PARTIAL_REPRESENTMENT,
            NPCI_PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext(disputeWorkflow.getDisputedAmount() - 10));

        triggerPartialDebit(disputeWorkflow.getDisputedAmount() - 10);

    }

}