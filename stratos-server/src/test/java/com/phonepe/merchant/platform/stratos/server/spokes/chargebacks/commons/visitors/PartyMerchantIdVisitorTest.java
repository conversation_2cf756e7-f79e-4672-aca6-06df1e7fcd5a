package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.models.payments.common.party.impl.CardNetworkAcquirerParty;
import com.phonepe.models.payments.common.party.impl.ExternalUserParty;
import com.phonepe.models.payments.common.party.impl.InternalUserParty;
import com.phonepe.models.payments.common.party.impl.MerchantUserParty;
import com.phonepe.models.payments.common.party.impl.UnknownParty;
import com.phonepe.models.payments.pay.ReceivedPayment;
import com.phonepe.models.payments.pay.context.impl.PeerToPeerPaymentContext;
import com.phonepe.models.payments.pay.context.impl.WalletAppTopupContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class PartyMerchantIdVisitorTest {

    @Test
    void testInternalUserException() {

        final var thrown = Assertions.assertThrows(DisputeException.class,
            () -> InternalUserParty.builder()
                .build().accept(new PartyMerchantIdVisitor(ReceivedPayment.builder()
                    .transactionId("TXN1234")
                    .context(PeerToPeerPaymentContext.builder()
                        .build())
                    .build())));

        Assertions.assertEquals(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION, thrown.getErrorCode());
        Assertions.assertEquals("TXN1234", thrown.getContext().get(Constants.TRANSACTION_ID));
    }

    @Test
    void testExternalUserException() {

        final var thrown = Assertions.assertThrows(DisputeException.class,
            () -> ExternalUserParty.builder()
                .build().accept(new PartyMerchantIdVisitor(ReceivedPayment.builder()
                    .transactionId("TXN1234")
                    .build())));

        Assertions.assertEquals(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION, thrown.getErrorCode());
        Assertions.assertEquals("TXN1234", thrown.getContext().get(Constants.TRANSACTION_ID));
    }

    @Test
    void testWalletCase() {

        var merchantId = InternalUserParty.builder()
            .build().accept(new PartyMerchantIdVisitor(ReceivedPayment.builder()
                .transactionId("TXN1234")
                .context(WalletAppTopupContext.builder().build())
                .build()));

        Assertions.assertEquals("PHONEPEWALLETTOPUP", merchantId);
    }
    @Test
    void testCardNetworkAcquirerException() {

        final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> CardNetworkAcquirerParty.builder()
                        .build().accept(new PartyMerchantIdVisitor(ReceivedPayment.builder()
                                .transactionId("TXN1234")
                                .build())));

        Assertions.assertEquals(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION, thrown.getErrorCode());
        Assertions.assertEquals("TXN1234", thrown.getContext().get(Constants.TRANSACTION_ID));
    }
    @Test
    void testUnkownPartyException() {

        final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> UnknownParty.builder()
                        .build().accept(new PartyMerchantIdVisitor(ReceivedPayment.builder()
                                .transactionId("TXN1234")
                                .build())));

        Assertions.assertEquals(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION, thrown.getErrorCode());
        Assertions.assertEquals("TXN1234", thrown.getContext().get(Constants.TRANSACTION_ID));
    }
    @Test
    void testMerchantUserException() {

        final var thrown = Assertions.assertThrows(DisputeException.class,
                () -> MerchantUserParty.builder()
                        .build().accept(new PartyMerchantIdVisitor(ReceivedPayment.builder()
                                .transactionId("TXN1234")
                                .build())));

        Assertions.assertEquals(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION, thrown.getErrorCode());
        Assertions.assertEquals("TXN1234", thrown.getContext().get(Constants.TRANSACTION_ID));
    }


}