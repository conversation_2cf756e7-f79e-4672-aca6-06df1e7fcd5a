package com.phonepe.merchant.platform.stratos.server.integrationTests.utils;

import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.phonepe.merchant.platform.stratos.models.disputes.toa.responses.ToaSummary;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.FraudActionDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.ToaDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.ToaDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.WalletTransactionService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.junit.jupiter.api.Assertions;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static org.awaitility.Awaitility.await;

@UtilityClass
@Slf4j
public class AssertionUtils {

    private DisputeService disputeService;
    private FileRepository fileRepository;
    private RowRepository rowRepository;
    private DisputeMetadataRepository disputeMetadataRepository;

    private ToaDisputeMetadataRepository toaDisputeMetadataRepository;

    public void init(final DisputeService disputeService, final FileRepository fileRepository,
        final RowRepository rowRepository, final DisputeMetadataRepository disputeMetadataRepository,
        final ToaDisputeMetadataRepository toaDisputeMetadataRepository) {
        AssertionUtils.disputeService = disputeService;
        AssertionUtils.fileRepository = fileRepository;
        AssertionUtils.rowRepository = rowRepository;
        AssertionUtils.disputeMetadataRepository = disputeMetadataRepository;
        AssertionUtils.toaDisputeMetadataRepository = toaDisputeMetadataRepository;
    }

    public void assertDisputeWorkflowStateAndCategoryEquals(final String transactionReferenceId,
        final String disputeWorkflowId,
        final DisputeWorkflowState expectedState,
        final DisputeCategory expectedCategory,
        final long duration,
        final TimeUnit timeUnit){
        assertDisputeWorkEquals(
            transactionReferenceId,
            disputeWorkflowId,
            disputeWorkflow -> disputeWorkflow.getCurrentState() != expectedState || disputeWorkflow.getDispute().getDisputeCategory() != expectedCategory,
            retryException -> {
                Assertions.fail(String.format(
                    "After retry for 5 seconds dispute workflow state didn't changed to expected state %s. Dispute Category expected = %s",
                    expectedState,expectedCategory));
            },
            duration,
            timeUnit );
    }

    public void assertDisputeWorkflowStateEquals(
        final String transactionReferenceId,
        final String disputeWorkflowId,
        final DisputeWorkflowState expectedState,
        final long duration,
        final TimeUnit timeUnit){
        assertDisputeWorkEquals(
            transactionReferenceId,
            disputeWorkflowId,
            disputeWorkflow -> disputeWorkflow.getCurrentState() != expectedState,
            retryException -> {
                Assertions.fail(String.format(
                    "After retry for %s seconds dispute workflow state didn't changed to expected state %s. Current State : %s", duration,
                    expectedState,
                        disputeService
                        .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId).getCurrentState() ));
            },
            duration,
            timeUnit );
    }

    public void assertDisputeWorkflow(
        final String transactionReferenceId,
        final String disputeWorkflowId,
        final DisputeWorkflowState expectedState,
        final DisputeCategory disputeCategory,
        final DisputeType disputeType) {

        final var disputeWorkflow = disputeService
            .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);

        Assertions.assertEquals(expectedState, disputeWorkflow.getCurrentState());
        Assertions.assertEquals(disputeCategory, disputeWorkflow.getDispute().getDisputeCategory());
        Assertions.assertEquals(disputeType, disputeWorkflow.getDispute().getDisputeType());
    }

    private void assertDisputeWorkEquals(
        final String transactionReferenceId,
        final String disputeWorkflowId,
        final com.google.common.base.Predicate<DisputeWorkflow> retryIfResultPredicate,
        final Consumer<RetryException> onRetryFail,
        final long duration,
        final TimeUnit timeUnit) {

        final Callable<DisputeWorkflow> callable = () -> disputeService
            .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);

        final var retryer = RetryerBuilder.<DisputeWorkflow>newBuilder()
            .retryIfException()
            .retryIfResult(retryIfResultPredicate)
            .withWaitStrategy(WaitStrategies.fixedWait(1L, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterDelay(duration, timeUnit))
            .build();

        try {
            retryer.call(callable);
        } catch (final ExecutionException e) {
            Assertions.fail("Exception occurred while asserting dispute workflow state");
        } catch (final RetryException e) {
            onRetryFail.accept(e);
        }
    }

    public void assertDisputeWorkflowStateEquals(
        final String transactionReferenceId,
        final String disputeWorkflowId,
        final DisputeWorkflowState expectedState) {

        final var disputeWorkflow = disputeService
            .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);

        Assertions.assertEquals(expectedState, disputeWorkflow.getCurrentState());
    }
    public void assertDisputeWorkflowStateEqualsAndCategory(
        final String transactionReferenceId,
        final String disputeWorkflowId,
        final DisputeWorkflowState expectedState,
        final DisputeCategory disputeCategory) {

        final var disputeWorkflow = disputeService
            .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);

        Assertions.assertEquals(expectedState, disputeWorkflow.getCurrentState());
        Assertions.assertEquals(disputeCategory, disputeWorkflow.getDispute().getDisputeCategory());
    }

    public void assertFileProcessed(final String fileId, final long duration,
        final TimeUnit timeUnit) {
        final Callable<File> callable = () -> fileRepository
            .getFile(fileId);

        final var retryer = RetryerBuilder.<File>newBuilder()
            .retryIfException()
            .retryIfResult(file -> file.getFileState() != FileState.PROCESSED)
            .withWaitStrategy(WaitStrategies.fixedWait(1L, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterDelay(duration, timeUnit))
            .build();

        try {
            retryer.call(callable);
        } catch (final ExecutionException ex) {
            Assertions.fail("Exception occurred while asserting dispute workflow state");
        } catch (final RetryException ex) {
            Assertions.fail(
                "After retry for 30 seconds file state didn't changed to PROCESSED");
        }
    }

    public void assertFirstLevelDisputeNotFound(String fileId, final long duration, final TimeUnit timeUnit){
        final Callable<Row> callable = () -> rowRepository.getRow(fileId);
        final var retryer = RetryerBuilder.<Row>newBuilder()
            .retryIfException()
            .retryIfResult(row -> !StratosErrorCodeKey.FIRST_LEVEL_DISPUTE_NOT_FOUND.toString().equals(row.getRowContext().getCode()))
            .withWaitStrategy(WaitStrategies.fixedWait(1L, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterDelay(duration, timeUnit))
            .build();
        try {
            retryer.call(callable);
        } catch (final ExecutionException ex) {
            Assertions.fail("Exception occurred while asserting dispute workflow state");
        } catch (final RetryException ex) {
            Assertions.fail(
                "After retry for 30 seconds row code didn't changed to FIRST_LEVEL_DISPUTE_NOT_FOUND");
        }
    }

    public void assertRowStateEquals(RowState rowState, String fileId, final long duration,
        final TimeUnit timeUnit) {
        final Callable<Row> callable = () -> rowRepository.scatterGather(
                DetachedCriteria.forClass(Row.class).add(Restrictions.eq(Fields.sourceId, fileId)))
            .stream().findFirst()
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_FILE, Map.of(
                Constants.MESSAGE, "Unable to find find file",
                File.Fields.fileId, fileId
            )));

        final var retryer = RetryerBuilder.<Row>newBuilder()
            .retryIfException()
            .retryIfResult(row -> row.getRowState() != rowState)
            .withWaitStrategy(WaitStrategies.noWait())
            .withStopStrategy(StopStrategies.stopAfterDelay(duration, timeUnit))
            .build();

        try {
            retryer.call(callable);
        } catch (final ExecutionException ex) {
            Assertions.fail("Exception occurred while asserting dispute workflow state");
        } catch (final RetryException ex) {
            Assertions.fail(
                "After retry for 30 seconds row state didn't changed to PROCESSED");
        }
    }

    public void assertAndWaitUntilDisputeWorkflowCreation(final String transactionReferenceId, final DisputeType disputeType, final DisputeStage disputeStage ){
        await()
            .ignoreException(DisputeException.class)
            .until(()-> Objects.equals(
                disputeService.getDisputeWorkflow(transactionReferenceId,
                        disputeType, disputeStage)
                    .getTransactionReferenceId(), transactionReferenceId));
    }

    public void assertAndWaitUntilDisputeWorkflowMetadataCreation(final String transactionReferenceId, final String disputeWorkflowId){
        await()
            .ignoreException(DisputeException.class)
            .until(()->
                disputeMetadataRepository.select(transactionReferenceId,
                        disputeWorkflowId)
                    .isPresent());
    }

    public DisputeWorkflow assertAndGetDisputeWorkflowCreation(final String transactionReferenceId, final DisputeType disputeType, final DisputeStage disputeStage ){
        // wait till the entry for workflow is created
        AssertionUtils.assertAndWaitUntilDisputeWorkflowCreation(transactionReferenceId, disputeType, disputeStage);

        return  disputeService.getDisputeWorkflow(transactionReferenceId,
            disputeType, disputeStage);
    }

    public List<ToaDisputeMetadata> getToaDisputeMetadata(final String transactionReferenceId, final String disputeWorkflowId){
        // wait till the metadata for workflow is created
        AssertionUtils.assertAndWaitUntilDisputeWorkflowMetadataCreation(transactionReferenceId, disputeWorkflowId);
        return toaDisputeMetadataRepository.selectAllToaDisputeMetadata(
            transactionReferenceId, disputeWorkflowId);
    }

    public List<FraudActionDisputeMetadata> getFraudActionDisputeMetadata(final String transactionReferenceId, final String disputeWorkflowId){
        // wait till the metadata for workflow is created
        AssertionUtils.assertAndWaitUntilDisputeWorkflowMetadataCreation(transactionReferenceId, disputeWorkflowId);
        return disputeMetadataRepository.select(
            disputeWorkflowId, Set.of(DisputeMetadataType.FRA_ACTION_METADATA))
            .stream().map(e -> (FraudActionDisputeMetadata)e).toList();
    }

    public void assertToaSummary(ToaSummary toaSummary, DisputeWorkflow disputeWorkflow, DisputeWorkflowState currentState) {
        Assertions.assertEquals(disputeWorkflow.getTransactionReferenceId(),
            toaSummary.getTransactionReferenceId());
        Assertions.assertEquals(disputeWorkflow.getDisputeWorkflowId(),
            toaSummary.getDisputeWorkflowId());
        Assertions.assertEquals(disputeWorkflow.getDisputeStage().name(),
            toaSummary.getDisputeStage());
        Assertions.assertEquals(currentState.name(), toaSummary.getCurrentState());
    }

    public void assertErrorRowUpdated(final String fileId) {
        final Callable<List<Row>> callable =
            () -> rowRepository.select(fileId, DetachedCriteria.forClass(Row.class));

        final var retryer = RetryerBuilder.<List<Row>>newBuilder()
            .retryIfException()
            .retryIfResult(rowList -> {
                if (rowList.size() != 1) {
                    return true;
                }
                return rowList.get(0).getRowState() != RowState.FAILED;
            })
            .withWaitStrategy(WaitStrategies.noWait())
            .withStopStrategy(StopStrategies.stopAfterDelay(18000, TimeUnit.SECONDS))
            .build();

        try {
            retryer.call(callable);
        } catch (final ExecutionException ex) {
            Assertions.fail("Exception occurred while asserting dispute workflow state");
        } catch (final RetryException ex) {
            Assertions.fail(
                "After retry for 30 seconds file state didn't changed to PROCESSED");
        }
    }

    public boolean assertFileProcessing(final String fileId) {
        return fileRepository.getFile(fileId).getFileState() == FileState.PROCESSING;
    }
    public void assertFileAccepted(final String fileId) {
        Assertions.assertSame(fileRepository.getFile(fileId).getFileState(), FileState.ACCEPTED);
    }
    public void assertFileRejected(final String fileId) {
        Assertions.assertSame(fileRepository.getFile(fileId).getFileState(), FileState.REJECTED);
    }

    public void assertFileFailed(final String fileId) {
        Assertions.assertSame(fileRepository.getFile(fileId).getFileState(), FileState.FAILED);
    }

    public void assertAndWaitUntilDisputeWorkflowStateIsUpdated(String transactionReferenceId,
        DisputeType disputeType, DisputeStage disputeStage, DisputeWorkflowState disputeWorkflowState) {
        await()
            .ignoreException(DisputeException.class)
            .until(()-> Objects.equals(
                disputeService.getDisputeWorkflow(transactionReferenceId,
                        disputeType, disputeStage)
                    .getCurrentState(),disputeWorkflowState ));
    }
}
