package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v1.firstLevel;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.FRAUD_REJECT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.SUSPECTED_FRAUD_TO_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.SUSPECTED_FRAUD;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeMetadataDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata.DisputeFraActionMetadataResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata.DisputeMetadataBaseResponse;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.SidelineSkippedEvent;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.FraudActionDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;

class UpiFirstLevelMerchantAcceptedFlowITest extends UpiFirstLevelBaseTest {


    private EventIngester eventIngester = guiceInjector.getInstance(EventIngester.class);

    @ParameterizedTest
    @EnumSource( names = {"NOOP", "ALLOW"})
    void testAbsorbtionWithoutFraud(KratosRecommendedAction recommendedAction) {

        assertFromCreateToNpciAcceptanceCompleted(recommendedAction);
        testAbsorbtionFlow();

    }

    @ParameterizedTest
    @EnumSource( names = {"NOOP", "ALLOW"})
    void testSuspectedFraudThenNotFraudFlow(KratosRecommendedAction actionAfterSuspected){

        assertFromCreateToSuspectedFraud();

        MockingUtils.mockKratos(kratosService, actionAfterSuspected);

        //reconcile
        disputeResource.reconcile(new SuspectedFraudChargebackDisputeReconcileRequest());

        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeWorkflowId(),
            MERCHANT_ACCEPTED_CHARGEBACK,
            5L, TimeUnit.SECONDS);

        // complete NPCI acceptance
        assertTriggerEvent(COMPLETE_NPCI_ACCEPTANCE,
            NPCI_ACCEPTANCE_COMPLETED);

        testAbsorbtionFlow();
    }


    @Test
    void testFraOpsSuspectedFraudActionToAcceptance(){

        assertFromCreateToSuspectedFraud();

        serviceUserPrincipal = MockingUtils.getAndMockFraOpsUser(olympusIMClient);

        // check upcoming actions that fra ops can take
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                SUSPECTED_FRAUD);

        final var permissibleEvents = Set
            .of(SUSPECTED_FRAUD_TO_ACCEPTANCE,
                FRAUD_REJECT);

        Assertions.assertEquals(permissibleEvents, upcomingEvents);

        //merchant accepted chargeback but was marked suspected
        assertTriggerEvent(SUSPECTED_FRAUD_TO_ACCEPTANCE,
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);

    }

    @Test
    void testFraOpsSuspectedFraudActionToRejected(){

        assertFromCreateToSuspectedFraud();

        List<FraudActionDisputeMetadata> metadataList = AssertionUtils.getFraudActionDisputeMetadata(dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId());
        Assertions.assertEquals(1, metadataList.size(), "metadataList must have one fraud action");
        Assertions.assertEquals("SUSPECT", metadataList.get(0).getActionType());
        Assertions.assertEquals("PC_002", metadataList.get(0).getReasonCode());

        // Test getDisputeMetadata api
        var response = disputeResource.getDisputeMetadata(disputeWorkflow.getDisputeWorkflowId(), List.of(
            DisputeMetadataDto.FRA_ACTION_METADATA));

        Assertions.assertNotNull(response, "Dispute metadata response is null");
        Assertions.assertEquals(1, response.getDisputeMetadata().size(),
            "metadata response must have one fraud action");
        Assertions.assertTrue(response.getDisputeMetadata().containsKey(DisputeMetadataDto.FRA_ACTION_METADATA),
            "metadata response must have FRA_ACTION_METADATA action");

        List<DisputeMetadataBaseResponse> metadataBaseResponses = response.getDisputeMetadata().get(DisputeMetadataDto.FRA_ACTION_METADATA);
        Assertions.assertEquals(1, metadataBaseResponses.size(), "metadataBaseResponses must have one fraud action");
        Assertions.assertEquals("SUSPECT", ((DisputeFraActionMetadataResponse)metadataBaseResponses.get(0)).getActionType());
        Assertions.assertEquals("PC_002", ((DisputeFraActionMetadataResponse)metadataBaseResponses.get(0)).getReasonCode());
        Assertions.assertEquals(disputeWorkflow.getDisputeWorkflowId(), metadataBaseResponses.get(0)
            .getDisputeWorkflowId());
        Assertions.assertEquals(disputeWorkflow.getTransactionReferenceId(), metadataBaseResponses.get(0)
            .getTransactionId());




        serviceUserPrincipal = MockingUtils.getAndMockFraOpsUser(olympusIMClient);

        // check upcoming actions that fra ops can take
        final var upcomingEvents = disputeResource
            .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                DisputeWorkflowState.SUSPECTED_FRAUD);

        final var permissibleEvents = Set
            .of(SUSPECTED_FRAUD_TO_ACCEPTANCE,
                FRAUD_REJECT);

        Assertions.assertEquals(permissibleEvents, upcomingEvents);

        //merchant accepted chargeback but was marked suspected
        assertTriggerEvent(FRAUD_REJECT,
            DisputeWorkflowState.FRAUD_REJECTED);

    }

    @ParameterizedTest(name = "{0} {1}")
    @MethodSource("provideArgumentForFraNoActionAndPenaltyAmount")
    void testRecoveryWithoutFraud(KratosRecommendedAction recommendedAction, final long penaltyAmount) {

        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute,penaltyAmount);

        assertFromCreateToNpciAcceptanceCompleted(recommendedAction);
        testAutoApprovalFlow();
    }

    @Test
    void testAcceptanceChargebackWithMerchantNotResponded() {

        assertFromCreateToMerchantNotResponded();
        assertTriggerEvent(MERCHANT_ACCEPT_CHARGEBACK, MERCHANT_ACCEPTED_CHARGEBACK);

    }

    @Test
    void testKratosActorExceptionForBuildingKratosRequestException(){

        createEntrySetupForDisputeCategoryNull();
        assertRefundBlock();
        Mockito.verify(eventIngester,Mockito.times(1)).generateEvent(Mockito.any(
            SidelineSkippedEvent.class));
    }
}