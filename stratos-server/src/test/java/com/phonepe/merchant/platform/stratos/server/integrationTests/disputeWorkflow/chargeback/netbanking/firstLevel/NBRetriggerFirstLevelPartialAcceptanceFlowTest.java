package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.netbanking.firstLevel;

import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.CommentContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.PartialAcceptanceTransitionContext;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.TransitionLockKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.CallBackResource;
import com.phonepe.merchant.platform.stratos.server.core.resources.DisputeResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.resources.ChargebackResource;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.services.NetBankingChargebackService;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponse;
import lombok.SneakyThrows;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponseV1;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponseVersion;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.opentest4j.AssertionFailedError;

import java.io.ByteArrayInputStream;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.*;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.*;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED;

/**
 * <AUTHOR>
 */
public class NBRetriggerFirstLevelPartialAcceptanceFlowTest extends BaseTest {

    protected DisputeService disputeService;
    protected DisputeResource disputeResource;
    protected Dispute dispute;
    protected DisputeWorkflow disputeWorkflow;
    protected OlympusIMClient olympusIMClient;
    protected ServiceUserPrincipal serviceUserPrincipal;
    protected ChargebackResource chargebackResource;

    protected NetBankingChargebackService netBankingChargebackService;
    protected CallBackResource callBackResource;


    @BeforeEach
    void initBeforeEach() {
        truncateDb();
        disputeService = guiceInjector.getInstance(DisputeService.class);
        disputeResource = guiceInjector.getInstance(DisputeResource.class);
        olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);
        chargebackResource = guiceInjector.getInstance(ChargebackResource.class);
        final var olympusIMClient = guiceInjector.getInstance(OlympusIMClient.class);

        // default is set to merchant ops
        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusIMClient);
        dispute = TestDataUtils
                .getServiceChargeBackDispute(getDisputeType(), getDisputeStage());
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);
        baseTestSetup();
    }
    protected void baseTestSetup() {
        netBankingChargebackService = guiceInjector.getInstance(NetBankingChargebackService.class);
        callBackResource = guiceInjector.getInstance(CallBackResource.class);
    }

    protected DisputeStage getDisputeStage() {
        return DisputeStage.FIRST_LEVEL;
    }

    protected DisputeType getDisputeType() {
        return DisputeType.NB_CHARGEBACK;
    }


    @Test
    void testReTriggerPartialRefundSuccess() {

        testTillPartialRefundFailure();

        MockingUtils.setUpRefundResponseMocking(RefundStatus.ACCEPTED.name(),
                disputeWorkflow.getDisputedAmount() - 50L);

        clearAerospike();
        assertTriggerEventWithDelay(
                DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND,
                DisputeWorkflowState.CB_REFUND_INITIATED,
                Constants.EMPTY_TRANSITION_CONTEXT, 30L);

        triggerCallBackAndAssertStatus(0);

        testRecoveryFlow();

    }

    protected void clearAerospike() {
        final TransitionLockCommand transitionLockCommand = guiceInjector.getInstance(TransitionLockCommand.class);
        transitionLockCommand.delete(TransitionLockKey.builder()
                .transitionKey(disputeWorkflow.getDisputeWorkflowId())
                .build());
    }


    protected void assertTriggerEventWithDelay(final DisputeWorkflowEvent event,
                                               final DisputeWorkflowState state, final TransitionContext transitionContext) {

        // Trigger Event
        disputeResource.triggerEvent(
                serviceUserPrincipal,
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                event, transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                state, 10L, TimeUnit.SECONDS);
    }

    protected void testRecoveryFlow() {

        // Setup Plutus Ingestor and Status Check API to respond 204
        MockingUtils.setupPlutusEventIngestionApiMocking(disputeWorkflow);
        MockingUtils.setupPlutusStatusCheckApiMocking(disputeWorkflow);

        CommentContext commentContext = CommentContext.builder()
                .comment("Chargeback absorb required")
                .build();

        // Merchant Ops requests for recovery
        assertTriggerEvent(REQUEST_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REQUESTED,
                commentContext);

        // Reject recovery
        assertTriggerEvent(REJECT_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REJECTED,
                commentContext);

        // request absorb
        assertTriggerEvent(REQUEST_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REQUESTED,
                commentContext);

        // Reject absorb
        assertTriggerEvent(REJECT_ABSORB_CHARGEBACK, ABSORB_CHARGEBACK_REJECTED,
                commentContext);

        //request recovery
        assertTriggerEvent(REQUEST_RECOVER_CHARGEBACK, RECOVER_CHARGEBACK_REQUESTED,
                commentContext);

        // Finance Manager approves Recovery of Chargeback
        serviceUserPrincipal = MockingUtils.getAndMockFinanceManagerUser(olympusIMClient);
        assertTriggerEventWithDelay(APPROVE_RECOVER_CHARGEBACK,
                RECOVER_CHARGEBACK_EVENT_ACCEPTED, commentContext);

        serviceUserPrincipal = MockingUtils.getAndMockFinanceOpsUser(olympusIMClient);
        assertTriggerEventWithDelay(REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK,
                REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED, commentContext);

        assertTriggerEventWithDelay(REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK,
                RECOVER_CHARGEBACK_EVENT_ACCEPTED, commentContext);

        assertTriggerEventWithDelay(REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK,
                REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED, commentContext);

        serviceUserPrincipal = MockingUtils.getAndMockFinanceManagerUser(olympusIMClient);
        assertTriggerEventWithDelay(APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK,
                REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED, commentContext);
    }

    protected void assertTriggerEvent(final DisputeWorkflowEvent event,
                                      final DisputeWorkflowState state) {
        assertTriggerEvent(event, state, Constants.EMPTY_TRANSITION_CONTEXT);
    }

    protected void assertTriggerEvent(final DisputeWorkflowEvent event,
                                      final DisputeWorkflowState state, final TransitionContext transitionContext) {

        // Trigger Event
        disputeResource.triggerEvent(
                serviceUserPrincipal,
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                event, transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflow(
                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                state, dispute.getDisputeCategory(), disputeWorkflow.getDisputeType());
    }

    @SneakyThrows
    protected void triggerCallBackAndAssertStatus(int count) {

        String merchantTxnId = String.format("CB%s-%d",disputeWorkflow.getTransactionReferenceId(),count);

        RefundResponse refundStatus = RefundResponseV1.builder()
                .version(RefundResponseVersion.V1)
                .status(RefundStatus.ACCEPTED)
                .paymentTxnId(disputeWorkflow.getTransactionReferenceId())
                .merchantTxnId(merchantTxnId)
                .build();
        String status = objectMapper.writeValueAsString(refundStatus);

        var olympusClient = guiceInjector.getInstance(OlympusIMClient.class);
        serviceUserPrincipal = MockingUtils.getAndMockCallbackComponent(olympusClient);
        callBackResource.processCallback(serviceUserPrincipal,new ByteArrayInputStream(status.getBytes()), "refund-status");

        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.CB_REFUND_ACCEPTED,
                10L, TimeUnit.SECONDS);
        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusClient);
    }


    public void assertTriggerEventWithDelay(final DisputeWorkflowEvent event,
                                               final DisputeWorkflowState state, final TransitionContext transitionContext, final long delay) {

        // Trigger Event
        disputeResource.triggerEvent(serviceUserPrincipal,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                event,
                transitionContext);

        // Fetch The latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                state, delay, TimeUnit.SECONDS);

    }

    protected void assertRefundBlock() {
        // Assert Status of Dispute Workflow to be Representment Required
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                REFUND_BLOCKED,
                20L, TimeUnit.SECONDS);

        // Test Allowed Transitions from Current State
        final var permissibleEvents = Set
                .of(RECEIVE_FULFILMENT_DOCUMENTS,
                        RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
                        MERCHANT_ACCEPT_CHARGEBACK,
                        NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL);

        final var upcomingEvents = disputeResource
                .getUpcomingEvents(serviceUserPrincipal, disputeWorkflow.getDisputeType(),
                        disputeWorkflow.getDisputeStage(), disputeWorkflow.getDisputeWorkflowVersion(),
                        REFUND_BLOCKED);
        Assertions.assertEquals(permissibleEvents, upcomingEvents);
    }
    protected void testTillPartialRepresentmentCompleted() {

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiNoRefundExistsMocking(dispute.getTransactionReferenceId(),
                disputeWorkflow.getDisputedAmount(),
                "NP1234");
        // Create Dispute Entry in System
        disputeService.createEntry(dispute, disputeWorkflow);

        assertRefundBlock();

        assertTriggerEvent(RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
                DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);

        long acceptedAmount = disputeWorkflow.getDisputedAmount() - 50L;

        final var partialAcceptanceTransitionContext = PartialAcceptanceTransitionContext.builder()
                .acceptedAmount(acceptedAmount)
                .build();

        assertTriggerEvent(COMPLETE_PARTIAL_REPRESENTMENT,
                PARTIAL_REPRESENTMENT_COMPLETED, partialAcceptanceTransitionContext);
    }

    protected void testRefundCreatedState() {

        disputeResource.triggerEvent(serviceUserPrincipal,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowEvent.CREATE_CHARGEBACK_REFUND,
                Constants.EMPTY_TRANSITION_CONTEXT);

        Assertions.assertThrows(
                AssertionFailedError.class,
                ()->AssertionUtils.assertDisputeWorkflowStateEquals(
                        disputeWorkflow.getTransactionReferenceId(),
                        disputeWorkflow.getDisputeWorkflowId(),
                        DisputeWorkflowState.CB_REFUND_INITIATED));

        AssertionUtils.assertDisputeWorkflowStateEquals(
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowState.CB_REFUND_CREATED);

    }
    protected void testTillPartialRefundFailure() {

        MockingUtils.setUpRefundResponseMocking(RefundStatus.FAILED.name(),
                disputeWorkflow.getDisputedAmount() - 50L);

        testTillPartialRepresentmentCompleted();

        testRefundCreatedState();

    }
}
