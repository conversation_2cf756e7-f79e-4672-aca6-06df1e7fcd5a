package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.resources;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.NpciChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummary;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services.ChargebackService;
import java.util.EnumSet;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ChargebackResourceTest {

    @Mock
    private ChargebackService chargebackService;

    @InjectMocks
    private ChargebackResource chargebackResource;

    @Test
    void testGetAllStates() {

        final var expectedAllStates = EnumSet.allOf(DisputeWorkflowState.class);
        Mockito.when(chargebackService.getAllStates()).thenReturn(expectedAllStates);

        final var allStates = chargebackResource.getAllStates();

        Assertions.assertEquals(expectedAllStates, allStates);
    }

    @Test
    void testGetChargebackSummary() {

        final var expectedChargebacks = List.of(ChargebackSummary.builder().build());
        final var filter = Mockito.mock(DisputeFilter.class);
        Mockito.when(chargebackService.filter(filter)).thenReturn(expectedChargebacks);

        final var chargebackSummary = chargebackResource.getChargebackSummary(filter);

        Assertions.assertEquals(expectedChargebacks, chargebackSummary);
    }

    @Test
    void testGetNpciChargebackSummary() {

        final var expectedChargebacks = List.of(NpciChargebackSummary.builder().build());
        final var filter = Mockito.mock(DisputeFilter.class);
        Mockito.when(chargebackService.npciChargebackFilter(filter)).thenReturn(expectedChargebacks);

        final var chargebackSummary = chargebackResource.getNpciChargebackSummary(filter);

        Assertions.assertEquals(expectedChargebacks, chargebackSummary);
    }
}