package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.utils.AccountingEventUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.FRAUD_REJECTED_TO_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FRAUD_REJECTED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.HOLD;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

class UpiFirstLevelMerchantRejectedFlowITest extends UpiFirstLevelBaseTest {

    @ParameterizedTest
    @EnumSource( names = {"ALLOW", "NOOP", "SUSPECT"})
    void testFulfillmentCompletedForNotFraudCases(KratosRecommendedAction recommendedAction) throws InterruptedException {
        assertRepresentmentCompleted(recommendedAction);
        testFromRepresentmentCompletedToCreditReceived();
        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                HOLD,
            20L, TimeUnit.SECONDS);
    }

    @Test
    void testFulfillmentCompletedForFraudRejectedCase(){

        createEntrySetup(KratosRecommendedAction.BLOCK);

        // Fetch the latest Dispute Workflow from DB and Assert it's Current Status
        AssertionUtils.assertDisputeWorkflowStateEquals(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            FRAUD_REJECTED,
            20L, TimeUnit.SECONDS);

        // Trigger Event for Completing Representment on NPCI
        assertTriggerEvent(FRAUD_REJECTED_TO_REPRESENTMENT,REPRESENTMENT_COMPLETED );

        testFromRepresentmentCompletedToCreditReceived();
    }

    @ParameterizedTest
    @EnumSource( names = {"NOOP", "ALLOW", "SUSPECT"})
    void testPartialFulfillmentCompletedForNotFraudCases(KratosRecommendedAction recommendedAction){

        createEntrySetup(recommendedAction);
        assertRefundBlock();

        assertTriggerEvent(RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS, PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);

        // do partial representment
        assertTriggerEvent(COMPLETE_PARTIAL_REPRESENTMENT,
            PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext());

        triggerPartialCredit(TestDataUtils.transactionAmount-TestDataUtils.partialAcceptanceAmount);
        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            HOLD,
            20L, TimeUnit.SECONDS);
        assertAmountHold(disputeWorkflow.getTransactionReferenceId(),
            TestDataUtils.transactionAmount-TestDataUtils.partialAcceptanceAmount);

        // verify all accounting events raised
        List<AccountingEvent> accountingEventList = accountingEventService.getAccountingEvents(
                disputeWorkflow.getDisputeWorkflowId());

        Set<String> expectedIds = new HashSet<>();
        expectedIds.add(
            AccountingEventUtils.getHoldEventId(
                disputeWorkflow.getTransactionReferenceId(),
                EventGenerationType.HOLD_RECOVERY,
                disputeWorkflow.getDisputedAmount()));
        expectedIds.add(
            AccountingEventUtils.getHoldEventId(
                disputeWorkflow.getTransactionReferenceId(),
                EventGenerationType.HOLD_RECOVERY_REVERSAL,
                disputeWorkflow.getDisputedAmount()));
        expectedIds.add(
            AccountingEventUtils.toAccountingEventId(
                disputeWorkflow.getDisputeWorkflowId(),
                EventGenerationType.CHARGEBACK_RECOVERY));
        expectedIds.add(
            AccountingEventUtils.getHoldEventId(
                disputeWorkflow.getTransactionReferenceId(),
                EventGenerationType.HOLD_RECOVERY,
            TestDataUtils.transactionAmount-TestDataUtils.partialAcceptanceAmount));

        Set<String> accountingEventIds =
            accountingEventList
                .stream()
                .map(event -> event.getHeader().getTransactionId())
                .collect(Collectors.toSet());
        Assertions.assertEquals(expectedIds, accountingEventIds);

    }

    @Test
    void testRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotRespondedWithPartialRep();
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);

    }


    @Test
    void testPartialRepresentmentWithMerchantNotResponded() {

        assertFromCreateToMerchantNotRespondedWithPartialRep();

        // do partial representment
        assertTriggerEvent(COMPLETE_PARTIAL_REPRESENTMENT,
            PARTIAL_REPRESENTMENT_COMPLETED, TestDataUtils
                .getPartialAcceptanceTransitionContext());

        triggerPartialCredit(TestDataUtils.transactionAmount-TestDataUtils.partialAcceptanceAmount);

    }
    @ParameterizedTest
    @EnumSource( names = {"ALLOW"})
    void testIndefiniteHoldToEnd(KratosRecommendedAction recommendedAction) {
        assertRepresentmentCompleted(recommendedAction);
        testFromRepresentmentCompletedToCreditReceived();
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                HOLD,
                20L, TimeUnit.SECONDS);
        triggerIndefiniteHoldReconSignal();
    }


}