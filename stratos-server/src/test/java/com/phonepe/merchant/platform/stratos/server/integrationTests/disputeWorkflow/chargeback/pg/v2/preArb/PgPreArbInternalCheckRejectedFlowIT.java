package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.preArb;


import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.firstLevel.PgFirstLevelInternalCheckRejectedFlowIT;

/*
PG Pre arb is same as first level hence same test cases
*/
public class PgPreArbInternalCheckRejectedFlowIT extends PgFirstLevelInternalCheckRejectedFlowIT {

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.PRE_ARBITRATION;
    }
}
