package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v2.preArb;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.END;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

class UpiPreArbInternalCheckRejectedFlowITest extends UpiPreArbBaseTest {

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testRepresentmentCompletedThenCreditReceived(DisputeCategory disputeCategory) {
        assertFromCreateToRepresentmentRequirement(Set.of(COMPLETE_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            END,
            20L, TimeUnit.SECONDS);
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testInternalMidCreditReceived(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDisputeWithInternalMerchant(getDisputeType(), getDisputeStage(),
            disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute, DisputeWorkflowVersion.V2);

        // Setup Payment API to respond with Full Refund & RGCS Transaction
        MockingUtils.setupPaymentsApiFullRefundExistsMocking(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(),
            "YBL1234");

        assertFromCreateToInternalMidRepresentmentRequired(Set.of(COMPLETE_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                END,
                20L, TimeUnit.SECONDS);
    }
}