package com.phonepe.merchant.platform.stratos.server.integrationTests.dispute;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.RefundAction;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.services.impls.DisputeServiceImpl;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

@ExtendWith(MockitoExtension.class)
class RefundEligibilityTest {


    @InjectMocks
    private DisputeServiceImpl disputeService;
    @Mock
    private DisputeWorkflowRepository disputeWorkflowRepository;

    static Stream<Arguments> provideArgumentParametersForSingleDisputePresent() {
        return Stream.of(
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.REFUND_BLOCKED, DisputeWorkflowEvent.BLOCK_REFUND,
                DisputeWorkflowVersion.V1, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.REPRESENTMENT_REQUIRED,
                DisputeWorkflowEvent.REQUEST_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK,
                DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RGCS_ACCEPTANCE_REQUIRED,
                DisputeWorkflowEvent.REQUEST_RGCS_ACCEPTANCE,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.CREDIT_RECEIVED,
                DisputeWorkflowEvent.RECEIVE_CREDIT,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.NPCI_PARTIAL_REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_NPCI_PARTIAL_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED,
                DisputeWorkflowEvent.RECEIVE_PARTIAL_CREDIT,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RGCS_ACCEPTANCE_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_RGCS_ACCEPTANCE,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED,
                DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS,
                DisputeWorkflowVersion.V1, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_NPCI_ACCEPTANCE,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED,
                DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED,
                DisputeWorkflowEvent.INTERNAL_MID_REQUEST_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 20L
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED,
                DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED,
                DisputeWorkflowEvent.REJECT_ABSORB_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.CHARGEBACK_ABSORBED,
                DisputeWorkflowEvent.ABSORB_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL,
                DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL,
                DisputeWorkflowVersion.V1, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 20L
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED,
                DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 20L
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, RefundAction.HOLD, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.REFUND_BLOCKED, DisputeWorkflowEvent.BLOCK_REFUND,
                DisputeWorkflowVersion.V1, RefundAction.HOLD, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.REPRESENTMENT_REQUIRED,
                DisputeWorkflowEvent.REQUEST_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_PG_ACCEPTANCE,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.PG_PARTIAL_REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED,
                DisputeWorkflowEvent.REVERSE_ABSORB_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED,
                DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED,
                DisputeWorkflowEvent.REJECT_RECOVER_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED,
                DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED,
                DisputeWorkflowEvent.ACCEPT_RECOVER_CHARGEBACK_EVENT,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, RefundAction.HOLD, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED,
                DisputeWorkflowEvent.APPROVE_ABSORB_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED,
                DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.PARTIAL_REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.ACCEPTANCE_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_ACCEPTANCE,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED,
                DisputeWorkflowEvent.REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED,
                DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED,
                DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED,
                DisputeWorkflowEvent.ACCEPT_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.CHARGEBACK_CANCELLED,
                DisputeWorkflowEvent.RESET_CHARGEBACK,
                DisputeWorkflowVersion.V1, RefundAction.HOLD, 0
            ),
            Arguments.of(DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 0
            ),
            Arguments.of(DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.FAILURE, DisputeWorkflowEvent.PAYMENT_CALL_FAILED,
                DisputeWorkflowVersion.V1, RefundAction.PROCESS, 0
            ),
            Arguments.of(DisputeType.P2PM_TOA, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS, DisputeWorkflowEvent.KS_ENABLED,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.P2PM_TOA, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.P2PM_TOA_COMPLETED,
                DisputeWorkflowEvent.INITIATED_TO_COMPLETED,
                DisputeWorkflowVersion.V1, RefundAction.FAILED, 0
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED, DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK,
                DisputeWorkflowVersion.V2, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED, DisputeWorkflowEvent.ACCEPT_RECOVER_CHARGEBACK_EVENT,
                DisputeWorkflowVersion.V2, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED, DisputeWorkflowEvent.REJECT_RECOVER_CHARGEBACK,
                DisputeWorkflowVersion.V2, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED, DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT,
                DisputeWorkflowVersion.V2, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED, DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK,
                DisputeWorkflowVersion.V2, RefundAction.HOLD, 20L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED, DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
                DisputeWorkflowVersion.V2, RefundAction.PROCESS, 0L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED, DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
                    DisputeWorkflowVersion.V2, RefundAction.FAILED, 10L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED, DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
                    DisputeWorkflowVersion.V2, RefundAction.PROCESS, 0L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED, DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
                    DisputeWorkflowVersion.V2, RefundAction.FAILED, 10L
            )
            ,
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED, DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
                    DisputeWorkflowVersion.V2, RefundAction.PROCESS, 0L
            )
            ,
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED, DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
                    DisputeWorkflowVersion.V2, RefundAction.PROCESS, 0L
            )
            ,
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.HOLD, DisputeWorkflowEvent.HOLD,
                    DisputeWorkflowVersion.V2, RefundAction.PROCESS, 0L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.HOLD, DisputeWorkflowEvent.HOLD,
                    DisputeWorkflowVersion.V2, RefundAction.FAILED, 10L
            )
            ,
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.END, DisputeWorkflowEvent.END_WORKFLOW,
                    DisputeWorkflowVersion.V2, RefundAction.PROCESS, 0L
            ),
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                    DisputeWorkflowState.END, DisputeWorkflowEvent.END_WORKFLOW,
                    DisputeWorkflowVersion.V2, RefundAction.FAILED, 10L
            )
        );
    }

    static Stream<Arguments> provideArgumentParametersForErrorThrow() {
        return Stream.of(
            Arguments.of(DisputeType.UDIR_OUTGOING_COMPLAINT, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE
            ),
            Arguments.of(DisputeType.UDIR_INCOMING_COMPLAINT, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, StratosErrorCodeKey.INVALID_DISPUTE_TYPE
            ),
            Arguments.of(DisputeType.UDIR_INCOMING_COMPLAINT, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, StratosErrorCodeKey.INVALID_DISPUTE_TYPE
            ),
            Arguments.of(DisputeType.P2PM_TOA, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE
            ),
            Arguments.of(DisputeType.EDC_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                DisputeWorkflowVersion.V1, StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE
            ));
    }

    static Stream<Arguments> provideArgumentParametersForMultipleDisputeWorkflow() {
        return Stream.of(
            Arguments.of(DisputeType.UPI_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT,
                DisputeWorkflowVersion.V1, DisputeType.UPI_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_NPCI_ACCEPTANCE, DisputeWorkflowVersion.V1,
                RefundAction.FAILED
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT,
                DisputeWorkflowVersion.V1, DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.DEBIT_RECEIVED,
                DisputeWorkflowEvent.RECEIVE_DEBIT, DisputeWorkflowVersion.V1,
                RefundAction.FAILED
            ),
            Arguments.of(DisputeType.PG_CHARGEBACK, DisputeStage.FIRST_LEVEL,
                DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED,
                DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT,
                DisputeWorkflowVersion.V1, DisputeType.PG_CHARGEBACK, DisputeStage.PRE_ARBITRATION,
                DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED,
                DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT, DisputeWorkflowVersion.V1,
                RefundAction.FAILED
            ));
    }

    @ParameterizedTest(name = "{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10}")
    @MethodSource("provideArgumentParametersForMultipleDisputeWorkflow")
    void testGetRefundEligibilityForMultipleDisputeWork(DisputeType disputeType1,
        DisputeStage disputeStage1, DisputeWorkflowState disputeWorkflowState1,
        DisputeWorkflowEvent disputeWorkflowEvent1, DisputeWorkflowVersion disputeWorkflowVersion1,
        DisputeType disputeType2,
        DisputeStage disputeStage2, DisputeWorkflowState disputeWorkflowState2,
        DisputeWorkflowEvent disputeWorkflowEvent2, DisputeWorkflowVersion disputeWorkflowVersion2,
        RefundAction refundAction) {
        List<DisputeWorkflow> mockList = new ArrayList<>();
        DisputeWorkflow disputeWorkflow1 = TestDataUtils.getFinancialDisputeWorkflow(disputeType1,
            disputeStage1, disputeWorkflowState1, disputeWorkflowEvent1, disputeWorkflowVersion1,
            LocalDateTime.now(),0);
        DisputeWorkflow disputeWorkflow2 = TestDataUtils.getFinancialDisputeWorkflow(disputeType2,
            disputeStage2, disputeWorkflowState2, disputeWorkflowEvent2, disputeWorkflowVersion2,
            LocalDateTime.now().plusMinutes(5),0);
        mockList.add(disputeWorkflow2);
        mockList.add(disputeWorkflow1);
        String transactionId = disputeWorkflow1.getTransactionReferenceId();
        Mockito.when(disputeWorkflowRepository.select(transactionId))
            .thenReturn(mockList);
        final var response = disputeService.getRefundEligibility(transactionId);
        Assertions.assertEquals(response.getRefundAction(), refundAction);
    }

    @ParameterizedTest(name = "{0},{1},{2},{3},{4},{5}")
    @MethodSource("provideArgumentParametersForErrorThrow")
    void testGetRefundEligibilityForErrorThrow(DisputeType disputeType,
        DisputeStage disputeStage, DisputeWorkflowState disputeWorkflowState,
        DisputeWorkflowEvent disputeWorkflowEvent, DisputeWorkflowVersion disputeWorkflowVersion,
        StratosErrorCodeKey stratosErrorCode) {
        List<DisputeWorkflow> mockList = new ArrayList<>();
        DisputeWorkflow disputeWorkflow = TestDataUtils.getFinancialDisputeWorkflow(disputeType,
            disputeStage, disputeWorkflowState, disputeWorkflowEvent, disputeWorkflowVersion,
            LocalDateTime.now(),0);
        mockList.add(disputeWorkflow);
        String transactionId = disputeWorkflow.getTransactionReferenceId();
        Mockito.when(disputeWorkflowRepository.select(transactionId))
            .thenReturn(mockList);
        final var stratosError = Assertions.assertThrows(DisputeException.class,
            () -> disputeService.getRefundEligibility(transactionId));
        Assertions.assertEquals(stratosErrorCode,
            stratosError.getErrorCode());
    }

    @ParameterizedTest(name = "{0},{1},{2},{3},{4},{5},{6}")
    @MethodSource("provideArgumentParametersForSingleDisputePresent")
    void testGetRefundEligibilityIfSingleDisputePresent(DisputeType disputeType,
        DisputeStage disputeStage, DisputeWorkflowState disputeWorkflowState,
        DisputeWorkflowEvent disputeWorkflowEvent, DisputeWorkflowVersion disputeWorkflowVersion,
        RefundAction refundAction, long acceptedAmount) {

        List<DisputeWorkflow> mockList = new ArrayList<>();
        DisputeWorkflow disputeWorkflow = TestDataUtils.getFinancialDisputeWorkflow(disputeType,
            disputeStage, disputeWorkflowState, disputeWorkflowEvent, disputeWorkflowVersion,
            LocalDateTime.now(),acceptedAmount);
        mockList.add(disputeWorkflow);
        String transactionId = disputeWorkflow.getTransactionReferenceId();
        Mockito.when(disputeWorkflowRepository.select(transactionId))
            .thenReturn(mockList);
        final var response = disputeService.getRefundEligibility(transactionId);
        Assertions.assertEquals(response.getRefundAction(), refundAction);
    }


    @Test
    void testGetRefundEligibilityIfDisputeAbsent() {
        List<DisputeWorkflow> mockList = new ArrayList<>();
        final String transactionId = "T12345678910";
        Mockito.when(disputeWorkflowRepository.select(transactionId)).thenReturn(mockList);
        final var response = disputeService.getRefundEligibility(transactionId);
        Assertions.assertEquals(RefundAction.PROCESS, response.getRefundAction());
    }
}
