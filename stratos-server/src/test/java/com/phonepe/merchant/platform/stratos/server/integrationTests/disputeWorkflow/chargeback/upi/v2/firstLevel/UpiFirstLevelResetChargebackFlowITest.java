package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v2.firstLevel.UpiFirstLevelBaseTest;
import org.junit.jupiter.api.Test;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_NPCI_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED;

class UpiFirstLevelResetChargebackFlowITest extends UpiFirstLevelBaseTest {

    @Test
    void testAcceptedReset() {
        createEntrySetup(KratosRecommendedAction.ALLOW);
        assertRefundBlock();
        //merchant accepted chargeback
        assertTriggerEvent(MERCHANT_ACCEPT_CHARGEBACK, MERCHANT_ACCEPTED_CHARGEBACK);
        testResetChargeback();
    }

    @Test
    void testRepresentmentCompletedReset() {

        createEntrySetup(KratosRecommendedAction.ALLOW);
        assertRefundBlock();

        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);
        testResetChargeback();

    }
}
