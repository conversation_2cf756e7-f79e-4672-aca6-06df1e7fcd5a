package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v2.UpiBaseTest;

public abstract class UpiFirstLevelBaseTest extends UpiBaseTest {

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.FIRST_LEVEL;
    }
}
