package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.edc.v1.firstLevel;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCase;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import java.util.Set;

public abstract class EdcFirstLevelBaseTest extends ChargebackBaseTestCase {

    @Override
    protected void baseTestSetup() {
        MockingUtils.setEdcTransactionDetails(dispute.getMerchantId(),
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(), 0, dispute.getMerchantTransactionId());
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount(), 0, dispute.getMerchantTransactionId());
        MockingUtils.setEdcBlockReversals(dispute.getMerchantTransactionId());
        MockingUtils.setEdcUnBlockReversals(dispute.getMerchantTransactionId());
    }

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.FIRST_LEVEL;
    }

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.EDC_CHARGEBACK;
    }

    protected void createAndTestTillRepresentmentRequirement() {

        // mock full reversal
        MockingUtils.setEdcTransactionDetails(dispute.getMerchantId(),
            dispute.getTransactionReferenceId(), dispute.getTransactionAmount(),
            dispute.getTransactionAmount(), dispute.getMerchantTransactionId());

        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(),
            dispute.getTransactionAmount(), dispute.getMerchantTransactionId());

        MockingUtils.setEdcUnBlockReversals(dispute.getMerchantTransactionId());

        assertFromCreateToRepresentmentRequirement(Set.of(COMPLETE_REPRESENTMENT));
    }

    protected void createAndTestTillAcceptance() {
        createEntrySetup();
        assertRefundBlock();

        //merchant accepted chargeback
        assertTriggerEvent(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
            DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK);

        // complete acceptance
        assertTriggerEvent(DisputeWorkflowEvent.COMPLETE_ACCEPTANCE,
            DisputeWorkflowState.ACCEPTANCE_COMPLETED);
    }

    protected void assertFromCreateToRepresentment() {
        createEntrySetup();
        assertRefundBlock();

        // Trigger Event for receiving Fulfillment Documents from Merchant
        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);

        // Trigger Event for Completing Representment
        assertTriggerEvent(COMPLETE_REPRESENTMENT, REPRESENTMENT_COMPLETED);
    }

    protected void assertFromCreateToMechantNotResponded() {

        final var permissibleEventsMerchantNotResponding = Set
            .of(DisputeWorkflowEvent.COMPLETE_ACCEPTANCE,
                DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT,
                DisputeWorkflowEvent.COMPLETE_REPRESENTMENT);

        assertFromCreateToMerchantNotResponded(permissibleEventsMerchantNotResponding);

    }
}
