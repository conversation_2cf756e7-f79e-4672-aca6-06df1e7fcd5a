package com.phonepe.merchant.platform.stratos.server.core.resources;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import com.phonepe.merchant.platform.stratos.models.row.requests.PgMisRowSignalContext;
import com.phonepe.merchant.platform.stratos.models.row.requests.RowHistoryRequest;
import com.phonepe.merchant.platform.stratos.models.row.requests.StratosRowSignalRequest;
import com.phonepe.merchant.platform.stratos.models.row.response.RowHistoryResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.DestinationRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.FileRowStatus;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import javax.ws.rs.core.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Set;

@ExtendWith(MockitoExtension.class)
public class RowResourceTest {
    @Mock
    private RowService rowService;

    @InjectMocks
    private RowResource rowResource;

    @Mock
    private OlympusIMClient olympusIMClient;

    private ServiceUserPrincipal getServiceUserPrincipal() {
        return ServiceUserPrincipal.builder()
                .userAuthDetails(UserAuthDetails.builder()
                        .encodedTenantPermissions(new HashMap<>() {
                        }).build())
                .build();
    }

    @Test
    void testProcessSignal() {
        ServiceUserPrincipal serviceUserPrincipal = getServiceUserPrincipal();
        StratosRowSignalRequest stratosRowSignalRequest = StratosRowSignalRequest.builder()
                .rowSignalContext(PgMisRowSignalContext.builder().build()).build();
        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.anyString());
        Mockito.doNothing().when(rowService).processSignal(Mockito.any());
        rowResource.processSignal(serviceUserPrincipal, stratosRowSignalRequest);
        Mockito.verify(rowService).processSignal(Mockito.any());
    }

    @Test
    void testHistory() {
        ServiceUserPrincipal serviceUserPrincipal = getServiceUserPrincipal();
        RowHistoryRequest request = RowHistoryRequest.builder()
                .dateRangeFilter(DateRangeFilter.builder().build())
                .rowTypes(Set.of(RowTypeDto.PG_MIS_ROW))
                .build();
        RowHistoryResponse rowHistoryResponse = RowHistoryResponse.builder().build();

        Mockito.doReturn(true).when(olympusIMClient).verifyPermission(Mockito.any(), Mockito.anyString());
        Mockito.doReturn(rowHistoryResponse).when(rowService).history(request);
        RowHistoryResponse response = rowResource.history(request, serviceUserPrincipal);
        Assertions.assertEquals(rowHistoryResponse, response);
    }

}
