package com.phonepe.merchant.platform.stratos.server.integrationTests.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEvent;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.commons.DownloadReportType;
import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.CommentContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalCreditTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalDebitTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.PartialAcceptanceTransitionContext;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeData;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.WalletTransactionDetails;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.WalletDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.requests.EdcRowSignalContext;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DownloadReportRequest;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.models.row.requests.PgMisRowSignalContext;
import com.phonepe.merchant.platform.stratos.models.row.requests.StratosRowSignalRequest;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.AllowFraudAction;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.BlockFraudAction;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.NonFinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.PrimaryKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.AccountingEventDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.CommentDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.CommentType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeIssuer;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.FileState;
import com.phonepe.merchant.platform.stratos.server.core.models.FileType;
import com.phonepe.merchant.platform.stratos.server.core.models.SourceType;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.file.edc.beans.EdcChargebackBean;
import com.phonepe.merchant.platform.stratos.server.integrationTests.file.nb.beans.NetBankingChargebackBean;
import com.phonepe.merchant.platform.stratos.server.integrationTests.file.pg.beans.PgFirstLevelChargebackBean;
import com.phonepe.merchant.platform.stratos.server.integrationTests.file.pg.beans.PgPreArbChargebackBean;
import com.phonepe.merchant.platform.stratos.server.integrationTests.file.upi.beans.UPIChargebackBean;
import com.phonepe.merchant.platform.stratos.server.integrationTests.file.wallet.beans.WalletChargebackBean;
import com.phonepe.merchant.platform.stratos.server.utils.ResourceUtils;
import com.phonepe.models.merchants.Address;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.MerchantType;
import com.phonepe.models.merchants.store.OnboardingType;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.ruleengine.model.integration.FraudAction;
import com.phonepe.ruleengine.model.integration.FraudActionHolder;
import com.phonepe.warden.workflow.models.enums.WorkflowInstanceState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import lombok.experimental.UtilityClass;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

@UtilityClass
public class TestDataUtils {

    public static final String MERCHANT_ID = "*********";
    public static final String WARDEN_WF_ID = "mockWardenWorkflowId";
    public static final String INTERNAL_MERCHANT_ID = "PHONEPECARDPAYMENT";
    public static final String EDC_TENANT_ID = "BijliPay";
    public static long transactionAmount = 1000L;
    public static long partialAcceptanceAmount = 400L;

    private final Random RANDOM = new Random();

    public static IdHelper idHelper;

    public static void init(final IdHelper idHelper){
        TestDataUtils.idHelper = idHelper;
    }

    public Dispute getServiceChargeBackDispute(
        final DisputeType disputeType,
        final DisputeStage disputeStage) {

        return getDispute(disputeType, disputeStage, DisputeCategory.SERVICE_CHARGEBACK);
    }

    public Dispute getDispute(
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final DisputeCategory disputeCategory){
        return getDispute(disputeType, disputeStage, disputeCategory, MERCHANT_ID);
    }

    private Dispute getDispute(
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final DisputeCategory disputeCategory,
        final String merchantId
    ) {
        String transactionId = IdGenerator.generate("T").getId();

        return Dispute.builder()
            .key(PrimaryKey.builder()
                .partitionId(202101)
                .build())
            .disputeId(idHelper.disputeId(transactionId))
            .transactionReferenceId(transactionId)
            .disputeType(disputeType)
            .currentDisputeStage(disputeStage)
            .merchantId(merchantId)
            .merchantTransactionId(idHelper.merchantTransactionId(transactionId))
            .instrumentTransactionId(IdGenerator.generate("YBL").getId())
            .disputeReferenceId(IdGenerator.generate("D").getId())
            .rrn(IdGenerator.generate("R").getId())
            .transactionAmount(transactionAmount)
            .disputeIssuer(DisputeIssuer.HDFC_PG)
            .disputeCategory(disputeCategory)
            .build();
    }

    public Dispute getDisputeWithInternalMerchant(final DisputeType disputeType,
        final DisputeStage disputeStage, final DisputeCategory disputeCategory) {
        return getDispute(disputeType, disputeStage, disputeCategory,INTERNAL_MERCHANT_ID );
    }
    public DisputeWorkflow getDisputeWorkflow(final Dispute dispute) {
        return getDisputeWorkflow(
            dispute.getDisputeType(),
            dispute.getCurrentDisputeStage(),
            dispute.getDisputeId(),
            dispute.getTransactionReferenceId(),
            dispute.getTransactionAmount(),
            0L);
    }

    public DisputeWorkflow getDisputeWorkflow(final Dispute dispute,
    final DisputeWorkflowVersion version) {
        return getDisputeWorkflow(
            dispute.getDisputeType(),
            dispute.getCurrentDisputeStage(),
            dispute.getDisputeId(),
            dispute.getTransactionReferenceId(),
            dispute.getTransactionAmount(),
            0L, version);
    }

    public DisputeWorkflow getDisputeWorkflow(
        final Dispute dispute,
        final long penaltyAmount) {
        return getDisputeWorkflow(
            dispute.getDisputeType(),
            dispute.getCurrentDisputeStage(),
            dispute.getDisputeId(),
            dispute.getTransactionReferenceId(),
            dispute.getTransactionAmount(),
            penaltyAmount);
    }
    public DisputeWorkflow getDisputeWorkflow(
        final Dispute dispute,
        final long penaltyAmount,
        final DisputeWorkflowVersion version) {
        return getDisputeWorkflow(
                dispute.getDisputeType(),
                dispute.getCurrentDisputeStage(),
                dispute.getDisputeId(),
                dispute.getTransactionReferenceId(),
                dispute.getTransactionAmount(),
                penaltyAmount, version);
    }
    public DisputeWorkflow getDisputeWorkflow(
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final String disputeId,
        final String tid,
        final long amount,
        final long penaltyAmount, DisputeWorkflowVersion version) {
    return getDisputeWorkflow(
            disputeType, disputeStage, disputeId, tid,
            DisputeWorkflowEvent.CREATE_ENTRY,
            DisputeWorkflowState.RECEIVED,
            amount, penaltyAmount,
            LocalDateTime.now(),
            LocalDateTime.now().plusDays(10), version);
    }

    public DisputeWorkflow getDisputeWorkflow(
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final String disputeId,
        final String tid,
        final long amount,
        final long penaltyAmount) {
        return getDisputeWorkflow(
            disputeType, disputeStage, disputeId, tid,
            DisputeWorkflowEvent.CREATE_ENTRY,
            DisputeWorkflowState.RECEIVED,
            amount, penaltyAmount,
            LocalDateTime.now(),
            LocalDateTime.now().plusDays(10), DisputeWorkflowVersion.V1);
    }

    public DisputeWorkflow getDisputeWorkflow(
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final String disputeId,
        final String tid,
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final DisputeWorkflowState disputeWorkflowState,
        final LocalDateTime raisedAt,
        final LocalDateTime respondBy) {
        return getDisputeWorkflow(disputeType, disputeStage,
            disputeId,
            tid,
            disputeWorkflowEvent, disputeWorkflowState,
            1000L, 0L,
            raisedAt, respondBy, DisputeWorkflowVersion.V1);
    }

    public DisputeWorkflow getDisputeWorkflow(
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final String disputeId,
        final String tid,
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final DisputeWorkflowState disputeWorkflowState,
        final long amount,
        final long penaltyAmount,
        final LocalDateTime raisedAt,
        final LocalDateTime respondBy,
        final DisputeWorkflowVersion version) {

        if (disputeType == DisputeType.UDIR_OUTGOING_COMPLAINT) {
            return NonFinancialDisputeWorkflow.builder()
                .key(PrimaryKey.builder()
                    .partitionId(202101)
                    .build())
                .disputeWorkflowId(idHelper.disputeWorkflowId(tid))
                .disputeWorkflowVersion(version)
                .disputeSourceType(SourceType.FILE)
                .disputeSourceId(IdGenerator.generate("F").getId())
                .transactionReferenceId(tid)
                .disputeType(disputeType)
                .disputeStage(disputeStage)
                .currentEvent(disputeWorkflowEvent)
                .currentState(disputeWorkflowState)
                .disputedAmount(amount)
                .raisedAt(raisedAt)
                .respondBy(respondBy)
                .gandalfUserId(IdGenerator.generate("G").getId())
                .userType(UserType.SYSTEM)
                .disputeId(disputeId)
                .build();
        } else {
            return FinancialDisputeWorkflow.builder()
                .key(PrimaryKey.builder()
                    .partitionId(202101)
                    .build())
                .disputeWorkflowId(idHelper.disputeWorkflowId(tid))
                .disputeWorkflowVersion(version)
                .disputeSourceType(SourceType.FILE)
                .disputeSourceId(IdGenerator.generate("F").getId())
                .transactionReferenceId(tid)
                .disputeType(disputeType)
                .disputeStage(disputeStage)
                .currentEvent(disputeWorkflowEvent)
                .currentState(disputeWorkflowState)
                .disputedAmount(amount)
                .penaltyAmount(penaltyAmount)
                .raisedAt(raisedAt)
                .respondBy(respondBy)
                .gandalfUserId(IdGenerator.generate("G").getId())
                .userType(UserType.SYSTEM)
                .disputeId(disputeId)
                .build();
        }


    }
    // for v2 cases where we want to keep partial acceptance amount fixed
    public PartialAcceptanceTransitionContext getPartialAcceptanceTransitionContext() {
        return PartialAcceptanceTransitionContext.builder()
            .acceptedAmount(partialAcceptanceAmount)
            .build();
    }

    public PartialAcceptanceTransitionContext getPartialAcceptanceTransitionContext(
        final long amount) {
        return PartialAcceptanceTransitionContext.builder()
            .acceptedAmount(amount)
            .build();
    }

    public InstitutionalCreditTransitionContext getRefundCreditTransitionContext(
        final long amount) {
        return InstitutionalCreditTransitionContext.builder()
            .creditSourceType(
                com.phonepe.merchant.platform.stratos.models.commons.SourceType.FILE)
            .creditSourceId(IdGenerator.generate("F").getId())
            .creditAmount(amount)
            .build();
    }

    public InstitutionalDebitTransitionContext getDebitTransitionContext(final long amount) {
        return InstitutionalDebitTransitionContext.builder()
            .debitSourceType(
                com.phonepe.merchant.platform.stratos.models.commons.SourceType.FILE)
            .debitSourceId(IdGenerator.generate("F").getId())
            .debitAmount(amount)
            .build();
    }

    public AccountingEventDisputeMetadata getAccountingEventDisputeMetadata(
        final String transactionReferenceId,
        final String disputeWorkflowId,
        final String accountingEventId) {

        final var accountingEvent = ResourceUtils
            .getResource("fixtures/accountingEvents/chargebackRecoveryAccountingEvent.json",
                AccountingEvent.class);
        accountingEvent.getHeader().setTransactionId(accountingEventId);

        return AccountingEventDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(202101)
                .build())
            .disputeMetadataId(idHelper.disputeMetaDataId(transactionReferenceId))
            .transactionReferenceId(transactionReferenceId)
            .disputeWorkflowId(disputeWorkflowId)
            .accountingEventId(accountingEventId)
            .accountingEvent(accountingEvent)
            .createdAt(LocalDateTime.now())
            .build();
    }


    public String getTransactionDetailWithFullReversal(final long amount, final String txnId) {

        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/upi/firstLevel/fullyReversedTransactionDetails.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getDeemFailureTransactionDetailWithFullReversal(final long amount,
        final String txnId) {

        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/upi/firstLevel/deemedFailureTransactionDetails.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }
    public String getFailureTransactionDetail(final long amount,final String txnId) {

        return ResourceUtils.getResourceString(
                        "fixtures/disputeWorkflow/chargeback/upi/firstLevel/failureTransactionDetails.json")
                .replaceAll("@@amount@@", Long.toString(amount))
                .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getMerchantTransactionStatus(final String merchantOrderId,
        final String paymentState, final long amount) {

        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/toa/sampleResponses/paymentStatusCheckAPIResponse.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@merchantOrderId@@", merchantOrderId)
            .replaceAll("@@paymentState@@", paymentState);
    }
    public String getWardenCallback(final String merchantOrderId,
                       final String paymentState, final long amount) {

        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/toa/sampleResponses/paymentStatusCheckAPIResponse.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@merchantOrderId@@", merchantOrderId)
            .replaceAll("@@paymentState@@", paymentState);
    }

    public String getTransactionDetailWithFullReversalAndRedeemMandateContext(final long amount,
        final String txnId) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/upi/firstLevel/fullyReversedTransactionDetailsWithRedeemMandateContext.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getTransactionDetailWithFullReversalAndPayMandateContext(final long amount,
        final String txnId) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/upi/firstLevel/fullyReversedTransactionDetailsWithPayMandateContext.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getTransactionDetailWithFullReversalPaidFromDebitCard(final long amount,
        final String txnId) {

        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/pg/fullyReversedTransactionDetailsPaidFromDebitCard.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getTransactionDetailWithFullReversalPaidFromNB(final long amount,
        final String txnId) {

        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/netbanking/fullyReversedTransactionDetails.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getTransactionDetailWithoutReversalPaidFromDebitCard(final long amount,
        final String txnId) {

        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/pg/transactionDetailPaidFromDebitCardNoReversal.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getPaymentTransactionsDetailsForWallet(final String globalPaymentId
    ,final long amount, String type, String date) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/wallet/externalRailWalletTransactionDetails.json")
            .replaceAll("@@paymentsTxnId@@", globalPaymentId)
            .replaceAll("@@walletPaymentsTxnId@@", globalPaymentId)
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@type@@", type)
            .replaceAll("@@processingRail@@", "UPI")
            .replaceAll("@@date@@", date);
    }

    public String getPaymentTransactionsDetailsForWalletForExternalUser(final String globalPaymentId
        ,final long amount, String type, String date) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/wallet/externalRailWalletForP2PTransactionDetails.json")
            .replaceAll("@@paymentsTxnId@@", globalPaymentId)
            .replaceAll("@@walletPaymentsTxnId@@", globalPaymentId)
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@type@@", type)
            .replaceAll("@@processingRail@@", "UPI")
            .replaceAll("@@date@@", date);
    }

    public String getPaymentTransactionsDetailsForWalletWithFileName(final String globalPaymentId
        ,final long amount, String type, String date, String fileName) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/wallet/" + fileName)
            .replaceAll("@@paymentsTxnId@@", globalPaymentId)
            .replaceAll("@@walletPaymentsTxnId@@", globalPaymentId)
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@type@@", type)
            .replaceAll("@@processingRail@@", "UPI")
            .replaceAll("@@date@@", date);
    }

    public String getPaymentTransactionsDetailsForInternalRailWallet(final String globalPaymentId
        ,final long amount, String type, String date) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/wallet/internalRailWalletTransactionDetails.json")
            .replaceAll("@@paymentsTxnId@@", globalPaymentId)
            .replaceAll("@@walletPaymentsTxnId@@", globalPaymentId)
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@type@@", type)
            .replaceAll("@@processingRail@@", "WALLET")
            .replaceAll("@@date@@", date);
    }

    public String getTransactionsDetailsForUpiIdFromWalletService(final String globalPaymentId
        ,final long amount, String type, String UWTransactionId) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/wallet/externalRailWalletTransactionDetails.json")
            .replaceAll("@@paymentsTxnId@@", globalPaymentId)
            .replaceAll("@@walletPaymentsTxnId@@", UWTransactionId)
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@type@@", type)
            .replaceAll("@@processingRail@@", "PPI_WALLET")
            .replaceAll("@@date@@", String.valueOf(LocalDateTime.now()));
    }

    public String getTransactionsDetailsForUpiIdFromWalletServiceForExternalUSer(final String globalPaymentId
        ,final long amount, String type, String UWTransactionId) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/wallet/externalRailWalletForP2PTransactionDetails.json")
            .replaceAll("@@paymentsTxnId@@", globalPaymentId)
            .replaceAll("@@walletPaymentsTxnId@@", UWTransactionId)
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@type@@", type)
            .replaceAll("@@processingRail@@", "PPI_WALLET")
            .replaceAll("@@date@@", String.valueOf(LocalDateTime.now()));
    }

    public WalletTransactionDetails getWalletTransactionDetails(final String globalPaymentId
        ,final long amount, String type, String UWTransactionId, String rrn, Date date){

        return WalletTransactionDetails.builder()
            .globalTransactionId(globalPaymentId)
            .upiTransactionId("PHPf7c3503b55b74c1383723d30cb45fa43")
            .transactionAmount(amount)
            .rrn(rrn)
            .sentTime(date)
            .transactionId(UWTransactionId)
            .build();
    }

    public String getTransactionDetailWithoutReversal(final long amount, final String txnId) {

        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/upi/firstLevel/nonReversedTransactionDetails.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getMiddleManMerchantDetails(final String merchantId, String merchantMandateType) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/upi/firstLevel/middleManChargebackDetails.json")
            .replaceAll("@@merchantMandateType@@", merchantMandateType)
            .replaceAll("@@merchantId@@", merchantId);
    }

    public String getEdcTransactionDetails(final String merchantId, final String terminalId,
        final String rrn, String transactionId, final long amount, final long reversalAmounts,
        final String merchantTransactionId) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/edc/edcTransactionDetails.json")
            .replaceAll("@@merchantId@@", merchantId)
            .replaceAll("@@terminalId@@", terminalId)
            .replaceAll("@@rrn@@", rrn)
            .replaceAll("@@transactionId@@", transactionId)
            .replaceAll("@@amount@@", String.valueOf(amount))
            .replaceAll("@@reversalAmounts@@", String.valueOf(reversalAmounts))
            .replaceAll("@@merchantTransactionId@@", merchantTransactionId);
    }

    public String getPaymentTransactionsDetailsForEdc(final String globalPaymentId) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/upi/firstLevel/transactionDetailForEdc.json")
            .replaceAll("@@globalPaymentId@@", globalPaymentId);
    }

    public String getMiddleManMerchantDetailsForFailure() {
        return ResourceUtils.getResourceString(
            "fixtures/disputeWorkflow/chargeback/upi/firstLevel/middleManChargebackDetailsForFailure.json");
    }

    public String getMiddleManMerchantDetailsForNull() {
        return ResourceUtils.getResourceString(
            "fixtures/disputeWorkflow/chargeback/upi/firstLevel/middleManChargebackDetailsForNull.json");
    }

    public String getTransactionDetailWithMultiReversal(final String txnId, final String fileName) {
        final String path = "fixtures/disputeWorkflow/chargeback/upi/firstLevel/" + fileName;
        return ResourceUtils.getResourceString(path)
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getValidRgcsTransactionResponse() {

        return ResourceUtils.getResourceString(
            "fixtures/disputeWorkflow/chargeback/upi/firstLevel/validRgcsTransactionResponse.json");
    }

    public String getNonRgcsTransactionResponse() {

        return ResourceUtils.getResourceString(
            "fixtures/disputeWorkflow/chargeback/upi/firstLevel/nonRgcsTransactionResponse.json");
    }

    public String getSuccessBlockUnblockResponse() {

        return ResourceUtils.getResourceString(
            "fixtures/disputeWorkflow/chargeback/upi/firstLevel/successRefundBlockedUnblocked.json");
    }

    public String getMerchantProfileWithOnlyMcc() {

        return ResourceUtils.getResourceString(
            "fixtures/disputeWorkflow/chargeback/upi/firstLevel/merchantProfileWithOnlyMcc.json");
    }

    public ServiceUserPrincipal getOlympusUser() {
        final var userAuthDetails = getOlympusAuthDetails();
        userAuthDetails.setEncodedTenantPermissions(Map.of());
        return ServiceUserPrincipal.builder()
            .userAuthDetails(userAuthDetails)
            .build();
    }

    public UserAuthDetails getOlympusAuthDetails() {
        return UserAuthDetails.builder()
            .encodedTenantPermissions(new LinkedHashMap<>())
            .userDetails(HumanUserDetails.builder()
                .userId("UID")
                .build()
            )
            .build();
    }

    public DownloadReportRequest getDownloadReportRequest(DownloadReportType reportType) {
        return DownloadReportRequest.builder()
            .fileFormat(FileFormat.CSV)
            .reportType(reportType)
            .filter(DateRangeFilter.builder()
                .disputeTypes(Set.of(DisputeTypeDto.UPI_CHARGEBACK, DisputeTypeDto.PG_CHARGEBACK))
                .dateRange(DateRange.builder()
                    .startDate(LocalDate.MIN)
                    .endDate(LocalDate.MIN)
                    .build())
                .build())
            .build();
    }

    public FinancialDisputeWorkflow getFinancialDisputeWorkflow(DisputeType disputeType,
        DisputeStage disputeStage, DisputeWorkflowState disputeWorkflowState,
        DisputeWorkflowEvent disputeWorkflowEvent, DisputeWorkflowVersion disputeWorkflowVersion,
        LocalDateTime localDateTime, long acceptedAmount) {
        return FinancialDisputeWorkflow.builder()
            .disputeType(disputeType)
            .acceptedAmount(acceptedAmount)
            .disputeStage(disputeStage)
            .currentState(disputeWorkflowState)
            .currentEvent(disputeWorkflowEvent)
            .disputeSourceType(SourceType.API)
            .disputeId("D12345678910")
            .disputeWorkflowId("DW12345678910")
            .disputeWorkflowVersion(disputeWorkflowVersion)
            .transactionReferenceId("T12345678198123123")
            .createdAt(localDateTime)
            .disputedAmount(100)
            .build();
    }

    public String getPgTransactionDetailsResponse(final String paymentsTransactionId) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/chargeback/pg/pgTransactionDetailsResponse.json")
            .replaceAll("@@paymentsTxnId@@", paymentsTransactionId);
    }

    public String getDeemedTransactionDetail(final long amount, final String txnId,
        final String paymentState) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/udir/deemedTransactionDetailResponse.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId)
            .replaceAll("@@paymentState@@", paymentState);
    }


    public String getDrcTransactionDetail(final long amount, final String txnId) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/udir/drcTransactionDetailResponse.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getP2MTransactionDetail(final long amount, final String txnId,
        final String receiverType, final String mcc) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/udir/paymentTransactionDetailResponse.json")
            .replaceAll("@@amount@@", Long.toString(amount))
            .replaceAll("@@paymentsTxnId@@", txnId)
            .replaceAll("@@receiverType@@", receiverType)
            .replaceAll("@@mcc@@", mcc);
    }

    public String getWardenDetails(final String wfId, final WorkflowInstanceState state, String fileId) {
        return ResourceUtils.getResourceString(
                        "fixtures/disputeWorkflow/warden/sampleResponse/callbackResponse.json")
                .replaceAll("@@wfId@@", wfId)
                .replaceAll("@@state@@", state.name().toUpperCase(Locale.ROOT))
                .replaceAll("@@fileId@@", fileId);
    }

    public StratosRowSignalRequest getPgMisReportRequest(String instrumentTxnId,
        RowTransactionType transactionType,
        Double amount, String fileId) {
        return StratosRowSignalRequest.builder()
            .sourceId(fileId)
            .rowSignalContext(PgMisRowSignalContext.builder()
                .autoRefund(false)
                .bankCode("tmp bank code")
                .bankTransactionId("tmp txn id")
                .cgst(new BigDecimal(0))
                .charges(new BigDecimal(0))
                .igst(new BigDecimal(0))
                .mid("mid 1234")
                .instrumentType("abcd")
                .netAmount(new BigDecimal(amount))
                .paymentDate(new Date(1))
                .pgId("pg")
                .pgTransactionId("pmtmp")
                .sgst(new BigDecimal(0))
                .transactionAmount(new BigDecimal(amount))
                .transactionId(instrumentTxnId)
                .transactionDate(new Date(1))
                .transactionType(transactionType)
                .interchange("HDFC")
                .build())
            .build();
    }

    public StratosRowSignalRequest getEdcReportRequest(final String instrumentTxnId,
        final RowTransactionType transactionType,
        final Double amount, final String fileId) {
        return StratosRowSignalRequest.builder()
            .sourceId(fileId)
            .rowSignalContext(EdcRowSignalContext.builder()
                .bankTransactionId("tmp txn id")
                .cgst(new BigDecimal(0))
                .igst(new BigDecimal(0))
                .sgst(new BigDecimal(0))
                .mid("mid 1234")
                .transactionId(instrumentTxnId)
                .transactionType(transactionType)
                .transactionDate(new Date(1))
                .transactionAmount(new BigDecimal(amount))
                .pgTransactionId("pmtmp")
                .chargebackAcceptedAmount(new BigDecimal(amount))
                .instrumentType("abcd")
                .charges(new BigDecimal(0))
                .build()).build();
    }


    public InputStream pgPreArbLevelChargebackCSVFileStream(String pgName)
        throws Exception {
        return pgPreArbLevelChargebackCSVFileStream(pgName, "12-12-2021");
    }

    public InputStream pgPreArbLevelChargebackCSVFileStream(String pgName, String date)
        throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            PgPreArbChargebackBean.builder()
                .transactionId("PG1234")
                .cbReceivedDate(date)
                .amount("1.0")
                .sourceOfCb(pgName)
                .build()
        ));
    }

    public File getFile(final String fileId, final FileState fileState) {
        return File.builder()
            .fileId(fileId)
            .fileName(IdGenerator.generate("FileName").getId())
            .fileState(fileState)
            .fileType(FileType.YES)
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .userType(UserType.SYSTEM)
            .gandalfUserId("ABCD")
            .key(StorageUtils.primaryKey())
            .rowCount(1)
            .build();
    }

    public MerchantProfile getMerchantProfile(String merchantId, String merchantType) {
        return MerchantProfile.builder()
            .merchantId(merchantId)
            .mcc("mcc")
            .merchantType(MerchantType.valueOf(merchantType))
            .address(
                Address.builder().area("area")
                    .city("city")
                    .state("state")
                    .pinCode("12345")
                    .building("building")
                    .street("street")
                    .locality("local")
                    .country("INDIA")
                    .formattedAddress("formated Address")
                    .landmark("landMark")
                    .lineOne("LineOne")
                    .lineTwo("lineTwo")
                    .build())
            .activatedAt(Date.from(Instant.now()))
            .fullName("Stratos")
            .category("category")
            .subCategory("subCategory")
            .superCategory("superCategory")
            .onboardingType(OnboardingType.SELF)
            .fullName("STRATOS")
            .build();
    }

    public CommentDisputeMetadata getCommentDisputeMetadata(final String transactionReferenceId,
        final String disputeWorkflowId) {

        return CommentDisputeMetadata.builder()
            .key(PrimaryKey.builder()
                .partitionId(202101)
                .build())
            .disputeMetadataId(idHelper.disputeMetaDataId(transactionReferenceId))
            .transactionReferenceId(transactionReferenceId)
            .disputeWorkflowId(disputeWorkflowId)
            .commentType(CommentType.APPROVE_RECOVER_CHARGEBACK_COMMENT)
            .comment("Approved")
            .commenterEmailId("<EMAIL>")
            .build();
    }

    public InputStream pgFirstLevelChargebackCSVFileStreamFromPgName(String pgName)
        throws Exception {

        return pgFirstLevelChargebackCSVFileStreamFromPgName(pgName, "12-12-2021");
    }

    public InputStream pgFirstLevelChargebackCSVFileStreamFromPgName(final String pgName,
        final String date)
        throws Exception {
        return FileTestUtils.getBeansAsInputStream(Collections.singletonList(
            PgFirstLevelChargebackBean.builder()
                .bankDisputedAmount("1.0")
                .bankRefNo("ABCD2")
                .pgId("rrn1234")
                .reasonCode("10.3")
                .source(pgName)
                .transactionId("PG1234")
                .disputeReason("Service Chargeback")
                .cbReceivedDate(date)
                .build()
        ));
    }

    public InputStream edcChargebackCSVFileStream() throws JsonProcessingException {
        return FileTestUtils.getBeansAsInputStream(Collections.singletonList(
            EdcChargebackBean.builder()
                .bankDisputedAmount("1.0")
                .disputeReason("Service Chargeback")
                .rrn("RRN")
                .externalTerminalId("TERMINALID")
                .cbReceivedDate("01-01-2021")
                .externalMerchantId("MERCHANTID")
                .tenant("BijliPay")
                .source("Axis Bank")
                .build()));
    }

    public InputStream pgFirstLevelChargebackCSVFileStreamFromPgTransactionId(
        final String pgTransactionId)
        throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            PgFirstLevelChargebackBean.builder()
                .bankDisputedAmount("1.0")
                .bankRefNo("ABCD2")
                .pgId("rrnn1234")
                .reasonCode("")
                .source("HDFC Bank")
                .transactionId(pgTransactionId)
                .disputeReason("Service Chargeback")
                .cbReceivedDate("01-01-2021")
                .build()
        ));
    }

    public InputStream UPI_CB_CSV_StreamWithCreditEntry() throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            UPIChargebackBean.builder()
                .adjDate("15-03-2018")
                .txnDate("15-02-2018")
                .adjType("Representment Raise")
                .beneficiary("YES")
                .adjAmount("100.0")
                .rrn("RRN1234")
                .compensationAmount("0.0")
                .upiTransactionId("YBL123")
                .build()
        ));
    }

    public InputStream UPI_FIRST_LEVEL_CSV_StreamWithCreditEntry() throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            UPIChargebackBean.builder()
                .adjDate("15-03-2018")
                .txnDate("15-02-2018")
                .adjType("Representment Raise")
                .beneficiary("YES")
                .adjAmount("1.0")
                .rrn("RRN1234")
                .compensationAmount("0.0")
                .upiTransactionId("YBL123")
                .txnAmount("1.0")
                .build()
        ));
    }

    public InputStream UPI_PRE_ARB_CSV_StreamWithDebitEntry() throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            UPIChargebackBean.builder()
                .adjDate("16-03-2018")
                .txnDate("15-02-2018")
                .adjType("Pre-Arbitration Acceptance")
                .beneficiary("YES")
                .adjAmount("1.0")
                .rrn("RRN1234")
                .compensationAmount("0.0")
                .upiTransactionId("YBL123")
                .txnAmount("1.0")
                .build()
        ));
    }

    public InputStream UPI_PRE_ARB_CSV_Stream_Raise() throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            UPIChargebackBean.builder()
                .adjDate("16-03-2018")
                .txnDate("15-02-2018")
                .adjType("Pre-Arbitration Raise")
                .beneficiary("YES")
                .adjAmount("1.0")
                .rrn("RRN1234")
                .compensationAmount("0.0")
                .upiTransactionId("YBL123")
                .txnAmount("1.0")
                .build()
        ));
    }

    public InputStream UPIFirstLevelChargebackCSVFileStream() throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            UPIChargebackBean.builder()
                .adjDate("15-03-2018")
                .txnDate("15-02-2018")
                .adjType("Chargeback Raise")
                .beneficiary("YES")
                .adjAmount("1.0")
                .rrn("RRN1234")
                .compensationAmount("0.0")
                .upiTransactionId("YBL123")
                .txnAmount("1.0")
                .build()
        ));
    }

    public InputStream WalletFirstLevelChargebackCSVFileStream(String disputeAmount, String txnAmount) throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            WalletChargebackBean.builder()
                .adjDate("15-03-2018")
                .txnDate("15-02-2018")
                .adjType("Chargeback Raise")
                .remitter("PHP")
                .beneficiery("YES")
                .adjAmount(disputeAmount)
                .rrn("RRN1234")
                .compensationAmount("0.0")
                .upiTransactionId("YBL123")
                .txnAmount(txnAmount)
                .build()
        ));
    }

    public InputStream UPI_PRE_ARB_FOR_GIVEN_DISPUTE_CSV_Stream_Raise(Dispute dispute)
        throws Exception {
        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            UPIChargebackBean.builder()
                .adjDate("16-03-2018")
                .txnDate("15-02-2018")
                .adjType("Pre-Arbitration Raise")
                .beneficiary("YES")
                .adjAmount("1.0")
                .rrn(dispute.getRrn())
                .compensationAmount("0.0")
                .upiTransactionId("YBL123")
                .txnAmount(String.valueOf(dispute.getTransactionAmount()))
                .build()
        ));
    }

    public InputStream UPIDifferedChargebackCSVFileStream(String adjType) throws Exception {

        return FileTestUtils.getBeansAsInputStream(Arrays.asList(
            UPIChargebackBean.builder()
                .adjDate("16-03-2018")
                .txnDate("15-02-2018")
                .adjType(adjType)
                .beneficiary("YES")
                .adjAmount("1.0")
                .rrn("RRN1234")
                .compensationAmount("0.0")
                .upiTransactionId("YBL123")
                .txnAmount("1.0")
                .build()
        ));
    }

    public static String getPaymentPayApiResponse(boolean success) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/toa/sampleResponses/paymentPayApiResponse.json")
            .replaceAll("@@success@@", Boolean.toString(success));
    }

    public static String getKillSwitchResponse(String projectId, String templateId, String toaType,
        long duration) {
        return ResourceUtils.getResourceString(
                "fixtures/disputeWorkflow/toa/sampleResponses/killSwitchResponseList.json")
            .replaceAll("@@projectId@@", projectId)
            .replaceAll("@@templateId@@", templateId)
            .replaceAll("@@toaType@@", toaType)
            .replaceAll("@@duration@@", Long.toString(duration));
    }

    public static String getKillSwitchEngageResponse() {
        return ResourceUtils.getResourceString(
            "fixtures/disputeWorkflow/toa/sampleResponses/killSwitchResponse.json");
    }

    public static CommentContext getCommentContext(String comment) {
        return CommentContext.builder()
            .comment(comment)
            .build();
    }

    public static CommentContext getCommentContext() {
        return getCommentContext("Test Comment");
    }

    public static Dispute getToaDispute() {
        return Dispute.builder()
            .currentDisputeStage(DisputeStage.FIRST_LEVEL)
            .disputeId("dispute-id-1234")
            .disputeReferenceId("reference-1234")
            .disputeType(DisputeType.P2PM_TOA)
            .disputeIssuer(DisputeIssuer.NPCI_DEEMED_FAILED_TXN)
            .instrumentTransactionId("inst1234")
            .key(StorageUtils.primaryKey())
            .merchantId(MERCHANT_ID)
            .merchantTransactionId("MTXN1234")
            .disputeCategory(DisputeCategory.TOA)
            .transactionAmount(100)
            .transactionReferenceId("TXN1234")
            .build();
    }

    public static Dispute getToaDispute(DisputeType disputeType, DisputeIssuer disputeIssuer) {
        return Dispute.builder()
                .currentDisputeStage(DisputeStage.FIRST_LEVEL)
                .disputeId("dispute-id-1234")
                .disputeReferenceId("reference-1234")
                .disputeType(disputeType)
                .disputeIssuer(disputeIssuer)
                .instrumentTransactionId("inst1234")
                .key(StorageUtils.primaryKey())
                .merchantId(MERCHANT_ID)
                .merchantTransactionId("MTXN1234")
                .disputeCategory(DisputeCategory.TOA)
                .transactionAmount(100)
                .transactionReferenceId("TXN1234")
                .build();
    }

    public static FinancialDisputeWorkflow getToaDisputeWorkflow(Dispute dispute) {
        return FinancialDisputeWorkflow.builder()
            .transactionReferenceId(dispute.getTransactionReferenceId())
            .raisedAt(LocalDateTime.of(2022, 1, 1, 10, 10))
            .respondBy(LocalDateTime.of(2022, 1, 1, 10, 10).plusDays(5))
            .key(StorageUtils.primaryKey())
            .gandalfUserId("stratos")
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .disputeWorkflowId("dispute-1234")
            .userType(UserType.SYSTEM)
            .disputeType(dispute.getDisputeType())
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeSourceType(SourceType.NEURON)
            .disputeSourceId("neuron-event-id-1234")
            .disputedAmount(dispute.getTransactionAmount())
            .currentState(DisputeWorkflowState.RECEIVED)
            .currentEvent(DisputeWorkflowEvent.CREATE_ENTRY)
            .disputeId(dispute.getDisputeId())
            .build();
    }

    public String getNetPeTransactionDetailResponse(String sourceId, String txnId) {

        return ResourceUtils.getResourceString("fixtures/disputeWorkflow/chargeback/netbanking/netpeTransactionDetails.json")
            .replaceAll("@@sourceId@@", sourceId)
            .replaceAll("@@paymentsTxnId@@", txnId);
    }

    public String getNBContent(String source, String txnId, Double amount) throws Exception {

        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(NetBankingChargebackBean.builder()
            .cbReceivedDate("2023-10-09")
            .transactionDate("2023-10-08")
            .bankReferenceId("445578")
            .amount(amount)
            .source(source)
            .transactionId(txnId)
            .disputeReason("Service Chargeback")
            .build());
    }

    public String getRefundResponse(String refundStatus, Long amount) {

        return ResourceUtils.getResourceString("fixtures/disputeWorkflow/chargeback/netbanking/refundResponse.json")
            .replaceAll("@@refundStatus@@", refundStatus)
            .replaceAll("@@amount@@", Long.toString(amount));

    }

    public String getPaymentResponseBasedOnInstrument(String instrumentType, Long amount, String transactionId) {

        return ResourceUtils.getResourceString("fixtures/disputeWorkflow/toa/notionalCreditToa/paymentDetailBasedOnInstrument.json")
                .replaceAll("@@paymentsTxnId@@", transactionId)
                .replaceAll("@@amount@@", Long.toString(amount))
                .replaceAll("@@instrumentType@@", instrumentType);
    }

    public Set<FraudActionHolder> getTestFraudActionHolders(List<FraudAction> fraudActions){
        return fraudActions.stream().map(fraudAction -> FraudActionHolder.builder().fraudAction(fraudAction).build()).collect(Collectors.toSet());
    }

    public FraudAction getAllowFraudAction(){
        return new AllowFraudAction();
    }
    public FraudAction getBlockFraudAction(){
        return new BlockFraudAction();
    }

    public WalletDisputeRequest getWalletDisputeRequest(
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage disputeStage,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory disputeCategory,
        String transactionId, int amount){
        return WalletDisputeRequest.builder()
            .transactionType(TransactionType.WALLET)
            .transactionId(transactionId)
            .disputedAmount(amount)
            .disputeData(DisputeData.builder()
                .disputeCategory(disputeCategory)
                .disputeStage(disputeStage)
                .disputeType(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.CHARGEBACK)
                .build())
            .reasonCode("123123")
            .build();
    }
}
