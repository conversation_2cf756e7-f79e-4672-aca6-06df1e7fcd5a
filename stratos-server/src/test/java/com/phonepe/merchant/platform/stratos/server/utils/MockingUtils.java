package com.phonepe.merchant.platform.stratos.server.utils;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.phonepe.merchant.platform.stratos.models.accountingevents.AccountingEventType;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.AllowFraudAction;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.BlockFraudAction;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.SuspectFraudAction;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.services.KratosService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AccountingEventUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.models.common.enums.ResponseCode;
import com.phonepe.models.mandates.merchant.enums.MerchantMandateType;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.instrument.PaymentInstrument;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.docstore.model.response.DocStoreUploadResponse;
import com.phonepe.platform.docstore.model.response.UploadResponseContext;
import com.phonepe.ruleengine.model.integration.FraudAction;
import com.phonepe.ruleengine.model.integration.actions.NoOp;
import com.phonepe.services.warden.core.models.responses.instance.WardenWorkflowInstance;
import com.phonepe.tstore.client.TstoreClient;
import com.phonepe.warden.workflow.models.enums.WorkflowInstanceState;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.io.InputStream;
import java.util.List;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import lombok.experimental.UtilityClass;
import org.mockito.Mockito;

@UtilityClass
public class MockingUtils {


    private final String REVERSAL_UNBLOCK = "/v1/chargeback/update/reversal/flag/%s/unblock";
    private final String EDC_REVERSAL_UNBLOCK = "/v2/chargeback/reject/%s";

    private final String EDC_REVERSAL_BLOCK = "/v2/chargeback/initiate/%s";
    private final String TRANSACTION_DETAIL = "/v2/transactions/transaction/%s/detail";
    private final String MERCHANT_TRANSACTIONS_STATUS = "/v1/merchants/%s/%s/status";
    private final String PAYMENT_PAY_API = "/v1/transactions/pay";
    private final String PAYMENT_PAY_API_V2 = "/v2/transactions/toa/pay";
    private final String MIDDLEMAN_MERCHANT_DETAILS = "/v1/chargeback/details/%s/%s/%s";
    private final String EDC_TRANSACTION_DETAILS = "/v1/chargeback/edcTransactions/%s/%s/%s/%s";
    private final String EDC_TRANSACTION_DETAILS_FROM_TRANSACTION_ID = "/v2/chargeback/edcTransactions/%s";
    private final String REVERSAL_BLOCK = "/v1/chargeback/update/reversal/flag/%s/block";
    private final String RGCS_REVERSALS_ORIGINALID = "/v1/chargeback/all/rgcs/reversals/originalId/%s";
    private final String UPI_TRANSACTION_DETAIL = "/v1/upi/upiTransaction/detail";
    private final String TRANSACTION_EXISTS = "/v1/transaction/exists/%s/%s";
    private final String INGESTION = "/v2/ingestion/%s";
    private final String DOCUMENT_DOWNLOAD = "/v1/documents/([a-zA-Z0-9_]*)\\?download=true";
    private final String UDIR_RAISE_OUTGOING_COMPLAINTS = "/v1/udir/raise/outgoing/complaint";
    private final String PG_INTERNAL_TRANSACTION = "/pg-transport/v1/internal/transaction/%s";
    private final String KILL_SWITCH_STRATOS_LIST = "/apis/killswitches/v1/stratos";
    private final String KILL_SWITCH_ENGAGE = "/apis/killswitches/v1/%s/%s/engage";
    private final String KILL_SWITCH_DEACTIVATE = "/apis/killswitches/v1/%s/%s/deactivate";

    private final String INGESTION_BULK = "/ingestion/v1/bulk";
    private final String WARDEN_CREATE_WORKFLOW_ENDPOINT = "/v4/workflow/STRATOS";
    private final String WARDEN_GET_WORKFLOW_DETAILS_ENDPOINT = "/v4/workflow/STRATOS/%s";

    private final String DOCUMENT_STRATOS = "/v1/documents/stratos";
    private final String DOCUMENT_STRATOS_V2 = "/v2/documents/stratos";
    private final String NETPE_TRANSACTION_STATUS = "/apis/netpe/v1/transactions/status/%s";
    private final String RO_REFUND_API = "/v1/refund/STRATOS";
    private final String RO_REFUND_STATUS = "/v1/refund/STRATOS/status";
    private final String WALLET_TRANSACTION_DETAILS_FROM_UPI_ID ="/upi/wallet/transaction/phonepe/v1/upiTransaction/detail";
    private final String ZENCAST_CLIENT_API = "/v1/communication/send/email/multicast";


    private void mockPostFunction(final int statusCode, final String url, final String body) {

        WireMock.stubFor(
            WireMock.post(WireMock.urlPathMatching(url))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(statusCode)
                    .withBody(body)));
    }

    private void mockPostFunction(final int statusCode, final String url, byte[] body) {
        WireMock.stubFor(
            WireMock.post(WireMock.urlPathMatching(url))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(statusCode)
                    .withBody(body)));
    }

    private void mockPostFunction(final int statusCode, final String url) {
        WireMock.stubFor(
            WireMock.post(WireMock.urlMatching(url))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(statusCode)));
    }

    private void mockGetFunction(final int statusCode, final String url, final String body) {

        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(url))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(statusCode)
                    .withBody(body)));
    }

    private void mockGetFunction(final int statusCode, final String url, final String body, final int delay) {

        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(url))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withFixedDelay(delay)
                    .withStatus(statusCode)
                    .withBody(body)));
    }

    private void mockGetFunction(final int statusCode, final String url) {
        WireMock.stubFor(
            WireMock.get(WireMock.urlMatching(url))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(statusCode)));
    }

    private void mockGetFunction(final int statusCode, final String url, byte[] body) {
        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(url))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(statusCode)
                    .withBody(body)));
    }

    public void setupPaymentsApiFullRefundExistsMockingForRedeemMandate(
        final String originalTransactionId,
        final long amount, final String upiTxnId) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversalAndRedeemMandateContext(amount,
                originalTransactionId));
        setUpPaymentForReversalRelatedApis(originalTransactionId);
        setUpGetMiddleManMerchantDetails("12345", "12345", "REDEM12345",
            "MERCHANT_ID_FROM_MMS", MerchantMandateType.BILLPAY_V2);
    }

    public void setupPaymentsApiFullRefundExistsMockingForRedeemMandateWithMmsFailure(
        final String originalTransactionId,
        final long amount, final String upiTxnId) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversalAndRedeemMandateContext(amount,
                originalTransactionId));
        setUpPaymentForReversalRelatedApis(originalTransactionId);
        setUpGetMiddleManMerchantDetailsFailure("12345", "12345",
            "REDEM12345");
    }

    public void setupPaymentsApiFullRefundExistsMockingForRedeemMandateWithMmsGivingNull(
        final String originalTransactionId,
        final long amount, final String upiTxnId) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversalAndRedeemMandateContext(amount,
                originalTransactionId));
        setUpPaymentForReversalRelatedApis(originalTransactionId);
        setUpGetMiddleManMerchantDetailsGivingNull("12345", "12345",
            "REDEM12345");
    }

    public void setupPaymentsApiFullRefundExistsMockingForPayMandate(
        final String originalTransactionId,
        final long amount, final String upiTxnId) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversalAndPayMandateContext(amount,
                originalTransactionId));
        setUpPaymentForReversalRelatedApis(originalTransactionId);
    }

    public void setUpPaymentForReversalRelatedApis(final String originalTransactionId) {
        mockGetFunction(200, String.format(
            RGCS_REVERSALS_ORIGINALID,
            originalTransactionId), TestDataUtils.getNonRgcsTransactionResponse());

        mockPostFunction(200, UPI_TRANSACTION_DETAIL, MapperUtils.serializeToBytes(
            GenericResponse.<String>builder()
                .success(true)
                .data(originalTransactionId)
                .build()));

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());
    }

    public void setUpZencastApi() throws JsonProcessingException {
        mockPostFunction(200, ZENCAST_CLIENT_API, new ObjectMapper().writeValueAsString(GenericResponse.builder()
            .success(true)
            .build()));
    }

    public void setUpZencastApiForFailing() throws JsonProcessingException {
        mockPostFunction(500, ZENCAST_CLIENT_API, new ObjectMapper().writeValueAsString(GenericResponse.builder()
            .success(false)
            .build()));
    }

    public void setupPaymentsApiFullRefundExistsMocking(final String originalTransactionId,
        final long amount, final String txnId) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversal(amount, originalTransactionId));

        setUpPaymentForReversalRelatedApis(originalTransactionId);
    }

    public void setUpPaymentsApiFullRefundExistsMockingPaidFromNB(
        final String originalTransactionId,
        final long amount, final String netpeTxnId
    ) {
        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversalPaidFromNB(amount,
                originalTransactionId));

        mockPostFunction(200, String
                .format(REVERSAL_BLOCK, originalTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());
    }

    public void setupPaymentsApiFullRefundExistsMockingPaidFromDebitCard(
        final String originalTransactionId,
        final long amount, final String pgTxnId) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversalPaidFromDebitCard(amount,
                originalTransactionId));

        mockPostFunction(200, String
                .format(REVERSAL_BLOCK, originalTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());
    }

    public void setupPaymentsApiNoRefundExistsMockingPaidFromDebitCard(
        final String originalTransactionId,
        final long amount) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithoutReversalPaidFromDebitCard(amount,
                originalTransactionId));

        mockPostFunction(200, String
                .format(REVERSAL_BLOCK, originalTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());

    }
    public void setupPaymentsApiWithFailureResponse(
            final String originalTransactionId) {
        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getFailureTransactionDetail(100,
                    originalTransactionId)
            );
    }



    public void setupPaymentsApiNoRefundExistsMocking(final String originalTransactionId,
        final long amount, final String upiTxnId) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithoutReversal(amount,
                originalTransactionId));

        mockPostFunction(200, String
                .format(REVERSAL_BLOCK, originalTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());

        mockGetFunction(200, String.format(
            RGCS_REVERSALS_ORIGINALID,
            originalTransactionId), TestDataUtils.getNonRgcsTransactionResponse());

        mockPostFunction(200, UPI_TRANSACTION_DETAIL, MapperUtils.serializeToBytes(
            GenericResponse.<String>builder()
                .success(true)
                .data(originalTransactionId)
                .build()));

    }

    public void setUpiTransactionDetail(final String originalTransactionId, final long amount) {
        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversal(amount,
                originalTransactionId));
    }

    public void setDeemFailureUpiTransactionDetail(final String originalTransactionId,
        final long amount) {
        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getDeemFailureTransactionDetailWithFullReversal(amount,
                originalTransactionId));
    }

    public void setMerchantTransactionsStatus(final String merchantId, final String merchantOrderId,
        MerchantTransactionState paymentState, final long amount) {
        mockGetFunction(200,
            String.format(MERCHANT_TRANSACTIONS_STATUS, merchantId, merchantOrderId),
            TestDataUtils.getMerchantTransactionStatus(merchantOrderId, paymentState.name(),
                amount));
    }

    public void setMerchantTransactionsStatus(final String merchantId, final String merchantOrderId,
                                              MerchantTransactionState paymentState, final long amount, final int delay) {
        mockGetFunction(200,
                String.format(MERCHANT_TRANSACTIONS_STATUS, merchantId, merchantOrderId),
                TestDataUtils.getMerchantTransactionStatus(merchantOrderId, paymentState.name(),
                        amount), delay);
    }

    public void setUpPaymentPayApi(boolean success, int statusCode) {
        mockPostFunction(statusCode, PAYMENT_PAY_API,
            TestDataUtils.getPaymentPayApiResponse(success)
        );
    }

    public void setupMerchantServiceApiMocking(final String merchantId) {

        mockGetFunction(200, String.format("/v2/profiles/%s", merchantId),
            TestDataUtils.getMerchantProfileWithOnlyMcc());
    }

    public void setupPlutusEventIngestionApiMocking(final DisputeWorkflow disputeWorkflow) {

        mockPostFunction(204, String.format(INGESTION,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_RECOVERY.getEventIdSuffix()));

        mockPostFunction(204, String.format(INGESTION,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_PENALTY_RECOVERY.getEventIdSuffix()));

        mockPostFunction(204, String.format(INGESTION,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_RECOVERY_REVERSAL.getEventIdSuffix()));

        mockPostFunction(204, String.format(INGESTION,
            AccountingEventUtils.getHoldEventId(disputeWorkflow.getTransactionReferenceId(),
            EventGenerationType.HOLD_RECOVERY, TestDataUtils.transactionAmount)));

        mockPostFunction(204, String.format(INGESTION,
            AccountingEventUtils.getHoldEventId(disputeWorkflow.getTransactionReferenceId(),
            EventGenerationType.HOLD_RECOVERY_REVERSAL, TestDataUtils.transactionAmount)));

        mockPostFunction(204, String.format(INGESTION,
            AccountingEventUtils.getHoldEventId(disputeWorkflow.getTransactionReferenceId(),
                EventGenerationType.HOLD_RECOVERY,
        TestDataUtils.transactionAmount-TestDataUtils.partialAcceptanceAmount)));

        mockPostFunction(204, String.format(INGESTION,
            AccountingEventUtils.getHoldEventId(disputeWorkflow.getTransactionReferenceId(),
                EventGenerationType.HOLD_RECOVERY_REVERSAL,
                TestDataUtils.transactionAmount-TestDataUtils.partialAcceptanceAmount)));

    }

    public void setupPlutusStatusCheckApiMocking(final DisputeWorkflow disputeWorkflow) {
        FinancialDisputeWorkflow dw = DisputeWorkflowUtils.getFinancialDisputeWorkflow(disputeWorkflow);

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_RECOVERY.getEventIdSuffix(),
            AccountingEventType.UPI_CHARGEBACK_RECOVERY));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_PENALTY_RECOVERY.getEventIdSuffix(),
            AccountingEventType.UPI_CHARGEBACK_PENALTY_RECOVERY));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_RECOVERY_REVERSAL.getEventIdSuffix(),
            AccountingEventType.UPI_CHARGEBACK_RECOVERY_REVERSAL));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_RECOVERY.getEventIdSuffix(),
            AccountingEventType.PG_CHARGEBACK_RECOVERY));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_PENALTY_RECOVERY.getEventIdSuffix(),
            AccountingEventType.PG_CHARGEBACK_PENALTY_RECOVERY));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_RECOVERY_REVERSAL.getEventIdSuffix(),
            AccountingEventType.PG_CHARGEBACK_RECOVERY_REVERSAL));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_RECOVERY_REVERSAL.getEventIdSuffix(),
            AccountingEventType.NB_CHARGEBACK_RECOVERY_REVERSAL));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            disputeWorkflow.getDisputeWorkflowId() +
                EventGenerationType.CHARGEBACK_RECOVERY.getEventIdSuffix(),
            AccountingEventType.NB_CHARGEBACK_RECOVERY));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            AccountingEventUtils.getHoldEventId(disputeWorkflow.getTransactionReferenceId(),
            EventGenerationType.HOLD_RECOVERY, TestDataUtils.transactionAmount),
            AccountingEventType.AMOUNT_ON_HOLD));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            AccountingEventUtils.getHoldEventId(disputeWorkflow.getTransactionReferenceId(),
            EventGenerationType.HOLD_RECOVERY_REVERSAL, TestDataUtils.transactionAmount),
            AccountingEventType.AMOUNT_ON_HOLD_REVERSAL));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            AccountingEventUtils.getHoldEventId(disputeWorkflow.getTransactionReferenceId(),
                EventGenerationType.HOLD_RECOVERY,
        TestDataUtils.transactionAmount-TestDataUtils.partialAcceptanceAmount),
            AccountingEventType.AMOUNT_ON_HOLD));

        mockGetFunction(200, String.format(TRANSACTION_EXISTS,
            AccountingEventUtils.getHoldEventId(disputeWorkflow.getTransactionReferenceId(),
                EventGenerationType.HOLD_RECOVERY_REVERSAL,
                TestDataUtils.transactionAmount-TestDataUtils.partialAcceptanceAmount),
            AccountingEventType.AMOUNT_ON_HOLD_REVERSAL));

    }

    public void setupEventIngestionApiMocking() {

        mockPostFunction(200, INGESTION_BULK);
    }

    public void setupTstoreClient(final TstoreClient tstoreClient) {
        Mockito.when(tstoreClient.putEntityAndNotify(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(true);
    }


    public void setupPgTransportTransactionDetailMocking(final String pgTransactionId,
        final String paymentsTransactionId) {

        mockGetFunction(200, String.format(PG_INTERNAL_TRANSACTION,
            pgTransactionId), TestDataUtils.getPgTransactionDetailsResponse(paymentsTransactionId));

    }


    public void setupPaymentsRaiseComplaintMocking() {

        mockPostFunction(202, UDIR_RAISE_OUTGOING_COMPLAINTS);
    }


    public void setupPaymentsRaiseComplaintTimeoutMocking() {
        WireMock.stubFor(
            WireMock.post(WireMock.urlPathMatching(UDIR_RAISE_OUTGOING_COMPLAINTS))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(202)
                    .withFixedDelay(3100)));
    }

    public void setupPaymentTransactionDetail(final String originalTransactionId,
        final long amount, final String paymentState) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getDeemedTransactionDetail(amount,
                originalTransactionId, paymentState));

    }

    public void setupPaymentDrcTransactionDetail(final String originalTransactionId,
        final long amount) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getDrcTransactionDetail(amount,
                originalTransactionId));

    }


    public void setupFailedPaymentsRaiseComplaintMocking() {
        mockPostFunction(404, UDIR_RAISE_OUTGOING_COMPLAINTS);

    }


    public void setupP2MExternalTransactionDetail(final String originalTransactionId,
        final long amount, final String receiverType, final String mcc) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getP2MTransactionDetail(amount,
                originalTransactionId, receiverType, mcc));
    }
    public void setupWardenDetails(
            final String wfId,
            WorkflowInstanceState state,
            String fileId
            ) {
        mockGetFunction(200,
            String.format(WARDEN_GET_WORKFLOW_DETAILS_ENDPOINT, wfId),
                TestDataUtils.getWardenDetails(wfId,state, fileId));
    }

    public void setupWardenWorkflowCreationSuccess(){
        setupWardenWorkflowCreation(WorkflowInstanceState.CREATED,200);
    }
    public void setupWardenWorkflowCreationFailed(){
        setupWardenWorkflowCreation(WorkflowInstanceState.FAILED,400);
    }
    public void setupWardenWorkflowCreation(WorkflowInstanceState state, int responseConde) {
        mockPostFunction(responseConde, WARDEN_CREATE_WORKFLOW_ENDPOINT,
            MapperUtils.serializeToBytes(
            WardenWorkflowInstance
                .builder()
                .workflowId(TestDataUtils.WARDEN_WF_ID)
                .state(state)
            .build()
        ));
    }

    public void setupDocstoreUploadApiMocking() {
        setupDocstoreUploadApiMocking(true, 200);
    }

    public void setupDocstoreUploadApiMocking(boolean status, int responseConde) {
        mockPostFunction(responseConde, DOCUMENT_STRATOS, MapperUtils.serializeToBytes(
            DocStoreUploadResponse
                .builder()
                .success(status)
                .context(
                    UploadResponseContext
                        .builder()
                        .id(IdGenerator.generate("ID").getId())
                        .build()
                )
        ));

    }
    public void setupDocstoreUploadPublicApiMocking() {
        setupDocstoreUploadPublicApiMocking(true, 200);
    }
    public void setupDocstoreUploadPublicApiMocking(boolean status, int responseConde) {
        String id = IdGenerator.generate("ID").getId();
        mockPostFunction(responseConde, DOCUMENT_STRATOS_V2, MapperUtils.serializeToBytes(
            DocStoreUploadResponse
                .builder()
                .success(status)
                .context(
                    UploadResponseContext
                        .builder()
                        .id(id)
                        .publicUrlPath(id)
                        .build()
                )
        ));

    }


    public void setupDocstoreGetFileForCreateChargebackApiMocking(final InputStream inputStream)
        throws Exception {
        mockGetFunction(200, DOCUMENT_DOWNLOAD, inputStream.readAllBytes());

    }

    public void setupDocstoreGetFileForCreateChargebackApiMocking(final String pgName)
        throws Exception {
        setupDocstoreGetFileForCreateChargebackApiMocking(pgName, "12-12-2021");

    }

    public void setupDocstoreGetFileForEdcCreateChargebackApiMocking() throws Exception {
        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.edcChargebackCSVFileStream()
                .readAllBytes());
    }

    public void setupEdcTransactionsFromMerchantTransactionId(final String transactionId, final long amount,
        final long reversedAmounts, final String merchantTransactionId) {
        mockGetFunction(200,
            String.format(EDC_TRANSACTION_DETAILS_FROM_TRANSACTION_ID, merchantTransactionId),
            TestDataUtils.getEdcTransactionDetails("MERCHANTID", "TERMINALID", "RRN",
                transactionId, amount, reversedAmounts, merchantTransactionId));
    }

    public void setupDocstoreGetFileForCreateChargebackApiMocking(final String pgName,
        final String data)
        throws Exception {
        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.pgFirstLevelChargebackCSVFileStreamFromPgName(pgName, data)
                .readAllBytes());

    }

    public void setupDocstoreGetFileForCreateChargebackApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPIFirstLevelChargebackCSVFileStream().readAllBytes());

    }

    public void setupDocstoreGetFileForCreateChargebackApiMockingForWallet(String disputeAmount, String txnAmount) throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.WalletFirstLevelChargebackCSVFileStream(disputeAmount, txnAmount).readAllBytes());

    }

    public void setupDocstoreGetFileForChargebackCreditApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPI_CB_CSV_StreamWithCreditEntry().readAllBytes());

    }

    public void setupDocstoreGetFileFirstLevelForChargebackCreditApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPI_FIRST_LEVEL_CSV_StreamWithCreditEntry().readAllBytes());

    }

    public void setupDocstoreGetFileFirstLevelDifferedChargeBackRaiseApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Chargeback Raise").readAllBytes());
    }

    public void setupDocstoreGetFileFirstLevelDifferedChargeBackRepresentmentRaiseApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Re-presentment Raise").readAllBytes());
    }

    public void setUpDocstoreGetPreArbDifferedChargebackRaiseApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Pre-Arbitration Raise").readAllBytes());
    }

    public void setUpDocstoreGetPreArbDifferedChargebackAcceptanceApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPIDifferedChargebackCSVFileStream("Differed Pre-Arbitration Acceptance").readAllBytes());

    }


    public void setupGetMerchantProfileApiMocking(final String merchantId,
        final String merchantType) {

        WireMock.stubFor(
            WireMock.get(WireMock.urlMatching(
                    String.format("/v2/profiles/%s", merchantId)))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200).withBody(
                        MapperUtils.serializeToBytes(
                            TestDataUtils.getMerchantProfile(merchantId, merchantType))
                    )
                )

        );
    }

    public void setupUnblockSuccessApiMocking(final String originalTransactionId) {

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());

    }

    public void setupPlutusEventIngestionApiMocking(final String accountingEventId) {

        WireMock.stubFor(
            WireMock
                .post(WireMock.urlMatching(String.format(INGESTION, accountingEventId)))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(204)));
    }

    public void setEdcTransactionDetails(final String tenant, final String merchantId,
        final String terminalId, final String rrn, final String transactionId, final long amount,
        final long reversedAmounts, final String merchantTransactionId) {
        mockGetFunction(200,
            String.format(EDC_TRANSACTION_DETAILS, tenant, merchantId, terminalId, rrn),
            TestDataUtils.getEdcTransactionDetails(merchantId, terminalId, rrn, transactionId,
                amount, reversedAmounts, merchantTransactionId));
    }

    public void  setEdcTransactionDetails(final String merchantId, final String transactionId, final long amount,
        final long reversedAmounts, final String merchantTransactionId){
        setEdcTransactionDetails(TestDataUtils.EDC_TENANT_ID,merchantId,"TERMINALID","RRN", transactionId, amount, reversedAmounts, merchantTransactionId);
    }

    public void setUpPaymentForEdc(final String originalTransactionId) {
        mockGetFunction(200, String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getPaymentTransactionsDetailsForEdc(originalTransactionId));
    }

    public void setEdcUnBlockReversals(final String merchantTransactionId) {
        mockPostFunction(200,
            String.format(EDC_REVERSAL_UNBLOCK, merchantTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());
    }

    public void setEdcBlockReversals(final String merchantTransactionId) {
        mockPostFunction(200,
            String.format(EDC_REVERSAL_BLOCK, merchantTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());
    }

    public void setUpGetMiddleManMerchantDetails(final String mandateId,
        final String userId, final String executionId, final String merchantId,
        final MerchantMandateType merchantMandateType) {
        mockGetFunction(200,
            String.format(MIDDLEMAN_MERCHANT_DETAILS, userId, mandateId, executionId),
            TestDataUtils.getMiddleManMerchantDetails(merchantId, merchantMandateType.toString()));
    }

    public void setUpGetMiddleManMerchantDetailsFailure(
        final String mandateId,
        final String userId, final String executionId) {
        mockGetFunction(200,
            String.format(MIDDLEMAN_MERCHANT_DETAILS, userId, mandateId, executionId),
            TestDataUtils.getMiddleManMerchantDetailsForFailure());
    }

    public void setUpGetMiddleManMerchantDetailsGivingNull(
        final String mandateId,
        final String userId, final String executionId) {
        mockGetFunction(200,
            String.format(MIDDLEMAN_MERCHANT_DETAILS, userId, mandateId, executionId),
            TestDataUtils.getMiddleManMerchantDetailsForNull());
    }

    public void setupPaymentsApiMockingWithoutUnblock(final String originalTransactionId,
        final long amount) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithoutReversal(amount,
                originalTransactionId));

        mockPostFunction(200, String
                .format(REVERSAL_BLOCK, originalTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());


    }

    public void setupPaymentsApiMockingMerchantAcceptAbsorb(final String originalTransactionId,
        final long amount) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithoutReversal(amount,
                originalTransactionId));

        mockPostFunction(200, String
                .format(REVERSAL_BLOCK, originalTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());


    }

    public void setupPaymentsApiMockingRgcsTransaction(final String originalTransactionId,
        final long amount) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithFullReversal(amount,
                originalTransactionId));

        mockGetFunction(200, String.format(
            RGCS_REVERSALS_ORIGINALID,
            originalTransactionId), TestDataUtils.getValidRgcsTransactionResponse());

    }

    public void setupPaymentsApiMockingSubmitFulfillmentDocument(final String originalTransactionId,
        final long amount) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithoutReversal(amount,
                originalTransactionId));

        mockPostFunction(200, String
                .format(REVERSAL_BLOCK, originalTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());

    }

    public void setupPaymentsApiMockingSubmitPartialFulfillmentDocument(
        final String originalTransactionId, final long amount) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getTransactionDetailWithoutReversal(amount,
                originalTransactionId));

        mockPostFunction(200, String
                .format(REVERSAL_BLOCK, originalTransactionId),
            TestDataUtils.getSuccessBlockUnblockResponse());

        mockPostFunction(200, String
            .format(REVERSAL_UNBLOCK,
                originalTransactionId), TestDataUtils.getSuccessBlockUnblockResponse());


    }

    public void setupPaymentsApiMockingForWallet(
        final String originalTransactionId, final long amount, String type, String date) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getPaymentTransactionsDetailsForWallet(
                originalTransactionId, amount, type, date));

        String transactionId = IdGenerator.generate("UW").getId();

        mockPostFunction(200, UPI_TRANSACTION_DETAIL, MapperUtils.serializeToBytes(
            GenericResponse.<String>builder()
                .success(true)
                .data(originalTransactionId)
                .build()));

        mockPostFunction(200, WALLET_TRANSACTION_DETAILS_FROM_UPI_ID,  TestDataUtils.getTransactionsDetailsForUpiIdFromWalletService(
            originalTransactionId, amount, type, transactionId));
    }

    public void setupPaymentsApiMockingForWalletException(
        final String originalTransactionId) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            "123");
    }

    public void setupPaymentsApiMockingForWalletForExternalUSer(
        final String originalTransactionId, final long amount, String type, String date) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getPaymentTransactionsDetailsForWalletForExternalUser(
                originalTransactionId, amount, type, date));

        String transactionId = IdGenerator.generate("UW").getId();

        mockPostFunction(200, UPI_TRANSACTION_DETAIL, MapperUtils.serializeToBytes(
            GenericResponse.<String>builder()
                .success(true)
                .data(originalTransactionId)
                .build()));

        mockPostFunction(200, WALLET_TRANSACTION_DETAILS_FROM_UPI_ID,  TestDataUtils.getTransactionsDetailsForUpiIdFromWalletServiceForExternalUSer(
            originalTransactionId, amount, type, transactionId));
    }
    public void setupPaymentsApiMockingForWalletWrongUpiId(
        final String originalTransactionId, final long amount, String type, String date) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getPaymentTransactionsDetailsForWallet(
                originalTransactionId, amount, type, date));

        mockPostFunction(200, UPI_TRANSACTION_DETAIL, MapperUtils.serializeToBytes(
            GenericResponse.<String>builder()
                .success(true)
                .data(originalTransactionId)
                .build()));

        mockPostFunction(200, WALLET_TRANSACTION_DETAILS_FROM_UPI_ID,
            MapperUtils.serializeToBytes(GenericResponse.<String>builder()
                .success(false)
                .data(originalTransactionId)
                .build()));
    }

    public void setupPaymentsApiMockingForInternalRailWallet(
        final String originalTransactionId, final long amount, String type, String date) {

        mockGetFunction(200,
            String.format(TRANSACTION_DETAIL, originalTransactionId),
            TestDataUtils.getPaymentTransactionsDetailsForInternalRailWallet(
                originalTransactionId, amount, type, date));

        String transactionId = IdGenerator.generate("UW").getId();

        mockPostFunction(200, UPI_TRANSACTION_DETAIL, MapperUtils.serializeToBytes(
            GenericResponse.<String>builder()
                .success(true)
                .data(originalTransactionId)
                .build()));

        mockPostFunction(200, WALLET_TRANSACTION_DETAILS_FROM_UPI_ID,  TestDataUtils.getTransactionsDetailsForUpiIdFromWalletService(
            originalTransactionId, amount, type, transactionId));
    }

    public void setupDocstoreGetFileForCreateChargebackPreArbApiMocking()
        throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.pgPreArbLevelChargebackCSVFileStream("HDFC Bank")
                .readAllBytes());

    }

    public void setupDocstoreGetFileForCreateChargebackPreArbApiMocking(final String pgName)
        throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.pgPreArbLevelChargebackCSVFileStream(pgName).readAllBytes());

    }

    public void mockRefundApiWithPaymentInstrument(final String originalTransactionId,
        final PaymentInstrument paymentInstrument) {
        final GenericResponse<TransactionDetail> detailResponse = MapperUtils.deserialize(
            TestDataUtils.getTransactionDetailWithoutReversal(100,
                originalTransactionId), TypeReferences.TRANSACTION_DETAILS);
        detailResponse.getData().getReceivedPayment().setReceivedIn(List.of(paymentInstrument));
        mockGetFunction(200, String.format(TRANSACTION_DETAIL, originalTransactionId)
            , MapperUtils.serializeToBytes(detailResponse));

    }

    public void setupDocstoreGetFileForPreArbDebitChargebackApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPI_PRE_ARB_CSV_StreamWithDebitEntry().readAllBytes());

    }

    public void setupDocstoreGetFileForPreArbCreateChargebackApiMocking() throws Exception {

        mockGetFunction(200, DOCUMENT_DOWNLOAD,
            TestDataUtils.UPI_PRE_ARB_CSV_Stream_Raise().readAllBytes());

    }

    public void setupDocstoreGetFileForPreArbCreateChargebackForGivenDisputeApiMocking(
        Dispute dispute) throws Exception {
        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching("/v1/documents/([a-zA-Z0-9_]*)\\?download=true"))
                .willReturn(aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody(
                        TestDataUtils.UPI_PRE_ARB_FOR_GIVEN_DISPUTE_CSV_Stream_Raise(dispute)
                            .readAllBytes()
                    )
                )
        );
    }

    public void disableKillSwitchMocking() {
        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(KILL_SWITCH_STRATOS_LIST))
                .willReturn(WireMock.aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody("[]")
                )
        );
    }

    public void enableKillSwitchMocking(String templateId, String toaType) {
        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(KILL_SWITCH_STRATOS_LIST))
                .willReturn(WireMock.aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody(TestDataUtils
                        .getKillSwitchResponse("stratos", templateId, toaType, 1000))
                )
        );
    }

    public void enableKillSwitchMocking(DisputeType disputeType) {
        enableKillSwitchMocking("stratos-toa_hold", disputeType.name());
    }

    public void engagingKillSwitchMocking() {
        WireMock.stubFor(
            WireMock.post(WireMock.urlPathMatching(
                    String.format(KILL_SWITCH_ENGAGE, "stratos", "stratos-toa_hold")))
                .willReturn(WireMock.aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody(TestDataUtils
                        .getKillSwitchEngageResponse())
                )
        );
    }

    public static void deactivateKillSwitchMocking() {
        WireMock.stubFor(
            WireMock.put(WireMock.urlPathMatching(String.format(KILL_SWITCH_DEACTIVATE, "stratos",
                    "d78f5b5e-056f-4c30-8770-30124d3fd808")))
                .willReturn(WireMock.aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody(TestDataUtils
                        .getKillSwitchEngageResponse())
                )
        );
    }

    private ServiceUserPrincipal mockOlympusUserPermission(final String permissionListFixerPath,
        final OlympusIMClient olympusIMClient) {
        final var merchantOpsPermissions = ResourceUtils.getResource(permissionListFixerPath, new TypeReference<List<String>>() {
        });
        Mockito.when(olympusIMClient.verifyPermission(Mockito.any(), Mockito.anyString()))
            .thenAnswer((invocation) -> merchantOpsPermissions.contains(invocation.getArgument(1, String.class)));

        return TestDataUtils.getOlympusUser();
    }

    public void mockKratos(final KratosService kratosService, final KratosRecommendedAction recommendedAction){

        FraudAction fraudAction;
        switch (recommendedAction) {
            case NOOP -> fraudAction = new NoOp();
            case ALLOW -> fraudAction= new AllowFraudAction();
            case BLOCK -> fraudAction= new BlockFraudAction(ResponseCode.BF_049);
            case SUSPECT -> fraudAction= new SuspectFraudAction(ResponseCode.PC_002);
            default -> throw new RuntimeException("Not a valid enum");
        }

        Mockito.when(kratosService
                .chargebackRecommendAction(Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(fraudAction);
    }

    public ServiceUserPrincipal getAndMockFraOpsUser(OlympusIMClient olympusIMClient) {
        return mockOlympusUserPermission(
            "fixtures/disputeWorkflow/chargeback/permissions/fraOps.json", olympusIMClient);
    }
    public void mockOlympusPermission(final String permission,
        final OlympusIMClient olympusIMClient) {
        Mockito.when(olympusIMClient.verifyPermission(Mockito.any(),
                Mockito.eq(permission)))
            .thenReturn(true);
    }

    public ServiceUserPrincipal getAndMockMerchantOpsUser(OlympusIMClient olympusIMClient){
        return mockOlympusUserPermission(
            "fixtures/disputeWorkflow/chargeback/permissions/merchantOps.json", olympusIMClient);
    }

    public ServiceUserPrincipal getAndMockFinanceManagerUser(OlympusIMClient olympusIMClient){
        return mockOlympusUserPermission(
            "fixtures/disputeWorkflow/chargeback/permissions/financeManager.json", olympusIMClient);
    }

    public ServiceUserPrincipal getAndMockFinanceOpsUser(OlympusIMClient olympusIMClient){
        return mockOlympusUserPermission(
            "fixtures/disputeWorkflow/chargeback/permissions/financeOps.json", olympusIMClient);
    }

    public ServiceUserPrincipal getAndMockMerchantManagerUser(OlympusIMClient olympusIMClient){
        return mockOlympusUserPermission(
            "fixtures/disputeWorkflow/chargeback/permissions/merchantManager.json",
            olympusIMClient);
    }
    public ServiceUserPrincipal getAndMockCallbackComponent(OlympusIMClient olympusIMClient){
        return mockOlympusUserPermission(
                "fixtures/disputeWorkflow/chargeback/permissions/callback.json", olympusIMClient);
    }

    public void setUpNetPeTransactionDetailsMocking(String netpeId, String txnId) {

        mockGetFunction(200,
            String.format(NETPE_TRANSACTION_STATUS, netpeId),
            TestDataUtils.getNetPeTransactionDetailResponse(netpeId, txnId));

    }

    public void setUpRefundResponseMocking(String refundStatus, Long amount) {

        mockPostFunction(200, RO_REFUND_API, TestDataUtils.getRefundResponse(refundStatus, amount));

    }

    public void setUpRefundStatusMocking(String refundStatus, Long amount, String merchantId, String refundId) {

        mockPostFunction(200, RO_REFUND_STATUS, TestDataUtils.getRefundResponse(refundStatus, amount));
    }

    public void setUpPaymentDetailMockingBasedOnInstrument(String instrumentType, Long amount, String transactionId) {

        mockGetFunction(200, String.format(TRANSACTION_DETAIL,transactionId),
                TestDataUtils.getPaymentResponseBasedOnInstrument(instrumentType, amount, transactionId));

    }

    public void setUpPaymentPayApiV2(boolean success, int statusCode) {
        mockPostFunction(statusCode, PAYMENT_PAY_API_V2,
                TestDataUtils.getPaymentPayApiResponse(success)
        );
    }
}
