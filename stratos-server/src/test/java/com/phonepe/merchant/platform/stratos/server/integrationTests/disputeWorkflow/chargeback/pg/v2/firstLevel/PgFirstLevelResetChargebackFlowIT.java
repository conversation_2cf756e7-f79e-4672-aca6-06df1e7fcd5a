package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.PgBaseTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;

public class PgFirstLevelResetChargebackFlowIT extends PgBaseTest {

    @ParameterizedTest
    @EnumSource( names = {"NOOP","ALLOW"})
    void testAcceptedReset(KratosRecommendedAction recommendedAction) {

        createEntrySetup(recommendedAction);
        assertRefundBlock();
        //merchant accepted chargeback
        assertTriggerEvent(MERCHANT_ACCEPT_CHARGEBACK, MERCHANT_ACCEPTED_CHARGEBACK);
        testResetChargeback();
    }

    @ParameterizedTest
    @EnumSource( names = {"NOOP","ALLOW"})
    void testRepresentmentCompletedReset(KratosRecommendedAction recommendedAction) {

        createEntrySetup(KratosRecommendedAction.ALLOW);
        assertRefundBlock();

        assertTriggerEvent(RECEIVE_FULFILMENT_DOCUMENTS, FULFILMENT_DOCUMENTS_RECEIVED);
        testResetChargeback();

    }
}
