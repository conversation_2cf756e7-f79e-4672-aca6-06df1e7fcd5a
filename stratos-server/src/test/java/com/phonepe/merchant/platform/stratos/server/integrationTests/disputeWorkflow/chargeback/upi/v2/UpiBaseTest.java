package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.upi.v2;

import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCaseV2;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import org.junit.jupiter.api.Assertions;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_ACCEPTANCE;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PARTIAL_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_CREDIT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.ACCEPTANCE_COMPLETED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.END;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;

public abstract class UpiBaseTest extends ChargebackBaseTestCaseV2 {

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.UPI_CHARGEBACK;
    }

    protected void testFromRepresentmentCompletedToEnd() {
        testFromRepresentmentCompletedToCreditReceived();
        AssertionUtils.assertDisputeWorkflowStateEquals(
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            END,20L, TimeUnit.SECONDS);
    }

    protected void testFromRepresentmentCompletedToCreditReceived() {

        // Test scenario where refund amount is lesser than expected amount
        // Then workflow breaks with error and refund row moves to error row
        final var lessAmountRefundCreditTransitionContext = TestDataUtils
            .getRefundCreditTransitionContext(disputeWorkflow.getDisputedAmount() - 50L);

        serviceUserPrincipal = MockingUtils.getAndMockMerchantOpsUser(olympusIMClient);

        final var stratosError = Assertions.assertThrows(DisputeException.class, () ->
            disputeService.triggerEvent(serviceUserPrincipal.getUserAuthDetails(),
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                RECEIVE_CREDIT, lessAmountRefundCreditTransitionContext));
        Assertions.assertEquals(StratosErrorCodeKey.INVALID_CREDIT_AMOUNT,
            stratosError.getErrorCode());

        triggerCreditSignal();
    }

    protected void assertAcceptanceToDebitSignal(KratosRecommendedAction recommendedAction) {
        assertAcceptanceCompleted(recommendedAction);
        triggerDebitSignal();
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                END,
                20L, TimeUnit.SECONDS);

    }
    protected void assertAcceptanceToEndFlow(KratosRecommendedAction recommendedAction) {
        assertAcceptanceCompleted(recommendedAction);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dispute.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
                END,
                20L, TimeUnit.SECONDS);
    }
}
