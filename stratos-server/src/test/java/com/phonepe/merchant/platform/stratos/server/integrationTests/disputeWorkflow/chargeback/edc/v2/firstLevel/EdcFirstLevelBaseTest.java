package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.edc.v2.firstLevel;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCase;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCaseV2;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;

import java.util.Set;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.REPRESENTMENT_COMPLETED;

public abstract class EdcFirstLevelBaseTest extends ChargebackBaseTestCaseV2 {

    @Override
    protected void baseTestSetup() {
        MockingUtils.setEdcTransactionDetails(dispute.getMerchantId(),
            dispute.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(), 0, dispute.getMerchantTransactionId());
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(dispute.getTransactionReferenceId(),
            disputeWorkflow.getDisputedAmount(), 0, dispute.getMerchantTransactionId());
        MockingUtils.setEdcBlockReversals(dispute.getMerchantTransactionId());
        MockingUtils.setEdcUnBlockReversals(dispute.getMerchantTransactionId());
    }

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.FIRST_LEVEL;
    }

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.EDC_CHARGEBACK;
    }

    @Override
    protected void assertRefundBlock(){
        final var permissibleEvents = Set
                .of(RECEIVE_FULFILMENT_DOCUMENTS,
                        MERCHANT_ACCEPT_CHARGEBACK,
                        NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL);
        assertRefundBlock(permissibleEvents);
    }

}
