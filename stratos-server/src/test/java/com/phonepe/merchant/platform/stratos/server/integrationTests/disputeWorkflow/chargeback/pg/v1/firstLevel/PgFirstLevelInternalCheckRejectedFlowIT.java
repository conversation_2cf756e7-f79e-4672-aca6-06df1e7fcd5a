package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.firstLevel;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v1.PgBaseTest;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import java.util.Set;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

public class PgFirstLevelInternalCheckRejectedFlowIT extends PgBaseTest {

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testFullRefundExistsAndRepresentmentCompleted(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDispute(getDisputeType(), getDisputeStage(), disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        assertFromCreateToRepresentmentRequirement(Set.of(COMPLETE_PG_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_PG_REPRESENTMENT, PG_REPRESENTMENT_COMPLETED);

    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(CATEGORY_ARGS)
    void testInternalMidToRepresentment(DisputeCategory disputeCategory) {

        dispute = TestDataUtils.getDisputeWithInternalMerchant(getDisputeType(), getDisputeStage(),
            disputeCategory);
        disputeWorkflow = TestDataUtils.getDisputeWorkflow(dispute);

        MockingUtils.setupPaymentsApiFullRefundExistsMocking(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputedAmount(),
            "YBL1234");

        assertFromCreateToInternalMidRepresentmentRequired(Set.of(COMPLETE_PG_REPRESENTMENT));
        assertTriggerEvent(COMPLETE_PG_REPRESENTMENT, PG_REPRESENTMENT_COMPLETED);


    }
}
