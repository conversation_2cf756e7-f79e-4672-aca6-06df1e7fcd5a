package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services.impls;


import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.NpciChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.server.BaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.WalletDisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeIssuer;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.SourceType;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services.ChargebackService;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.merchants.MerchantType;
import com.phonepe.models.payments.banking.card.CardIssuer;
import com.phonepe.models.payments.pay.instrument.*;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class ChargebackServiceImplTest extends BaseTest {

    private ChargebackService chargebackService;
    private DisputeWorkflowRepository disputeWorkflowRepository;
    private DisputeRepository disputeRepository;
    private WalletDisputeMetadataRepository walletDisputeMetadataRepository;
    public DisputeMetadataHelper disputeMetadataHelper;

    public static final String DISPUTE_TYPE_ARGS = "provideArgumentWithDisputeTypeExceptWallet";
    static Stream<Arguments> provideArgumentWithDisputeTypeExceptWallet() {
        return Stream.of(
            Arguments.of(DisputeType.UPI_CHARGEBACK),
            Arguments.of(DisputeType.EDC_CHARGEBACK),
            Arguments.of(DisputeType.PG_CHARGEBACK),
            Arguments.of(DisputeType.UDIR_OUTGOING_COMPLAINT),
            Arguments.of(DisputeType.UDIR_INCOMING_COMPLAINT),
            Arguments.of(DisputeType.P2PM_TOA),
            Arguments.of(DisputeType.BBPS_TAT_BREACH_TOA),
            Arguments.of(DisputeType.NB_CHARGEBACK),
            Arguments.of(DisputeType.NOTIONAL_CREDIT_TOA)
        );
    }

    @BeforeEach
    void setup() {
        chargebackService = guiceInjector.getInstance(ChargebackService.class);
        disputeWorkflowRepository = guiceInjector.getInstance(DisputeWorkflowRepository.class);
        disputeRepository = guiceInjector.getInstance(DisputeRepository.class);
        walletDisputeMetadataRepository = guiceInjector.getInstance(WalletDisputeMetadataRepository.class);
        disputeMetadataHelper = guiceInjector.getInstance(DisputeMetadataHelper.class);
        truncateDb();
    }


    @Test
    public void testDownload() throws IOException {
        mocks("multiReversalTransactionDetails.json");
        createDisputeWorkflow(DisputeType.UPI_CHARGEBACK);

        MockingUtils.setupGetMerchantProfileApiMocking("IRCTCINAPP",
            MerchantType.ONLINE_MERCHANT.name());

        final var response = chargebackService.download(FileFormat.CSV, DateRangeFilter.builder()
            .dateRange(DateRange.builder()
                .startDate(LocalDate.now().minusDays(2))
                .endDate(LocalDate.now())
                .build())
            .disputeTypes(Set.of(DisputeTypeDto.UPI_CHARGEBACK))
            .build());
        final CsvMapper mapper = new CsvMapper();
        final CsvSchema schema = CsvSchema.emptySchema().withHeader();
        final List<Map<String, String>> list = new LinkedList<Map<String, String>>();
        final MappingIterator<Map<String, String>> iterator = mapper.readerWithTypedSchemaFor(
                Map.class)
            .with(schema)
            .readValues(response);
        while (iterator.hasNext()) {
            list.add(iterator.next());
        }
        Assertions.assertEquals(1, list.size());
        Assertions.assertEquals("TXN1234", list.get(0).get("Payment Reference Id"));
        Assertions.assertEquals("area",list.get(0).get("Area"));
        Assertions.assertEquals("city",list.get(0).get("City"));
        Assertions.assertEquals("state",list.get(0).get("State"));
        Assertions.assertEquals("12345",list.get(0).get("Pin Code"));
        Assertions.assertEquals(CardIssuer.MASTER_CARD.getDisplayName(),list.get(0).get("Network"));
        Assertions.assertEquals(11, Arrays.stream(list.get(0).get("Reversal States").split(Constants.SPLIT_MULTIVALUE_SEPARATOR)).count());
        Assertions.assertEquals(DisputeIssuer.YES_NPCI.toString(),
            list.get(0).get("Chargeback Issuer"));
        Assertions.assertEquals(7, Arrays.stream(list.get(0).get("Reversal UTR Ids")
                .split(Constants.SPLIT_MULTIVALUE_SEPARATOR)).filter(value -> value.equals("NA"))
            .count());
        Assertions.assertEquals(1, Arrays.stream(list.get(0).get("Reversal UTR Ids")
                .split(Constants.SPLIT_MULTIVALUE_SEPARATOR)).filter(value -> value.equals("xyz1"))
            .count());
        Assertions.assertEquals(1, Arrays.stream(list.get(0).get("Reversal UTR Ids")
                .split(Constants.SPLIT_MULTIVALUE_SEPARATOR)).filter(value -> value.equals("xyz2"))
            .count());
        Assertions.assertEquals(1, Arrays.stream(list.get(0).get("Reversal UTR Ids")
                .split(Constants.SPLIT_MULTIVALUE_SEPARATOR)).filter(value -> value.equals("xyz3"))
            .count());
        Assertions.assertEquals(1, Arrays.stream(list.get(0).get("Reversal UTR Ids")
                .split(Constants.SPLIT_MULTIVALUE_SEPARATOR)).filter(value -> value.equals("xyz4"))
            .count());
        Assertions.assertEquals(1, Arrays.stream(list.get(0).get("Chargeback Category")
                .split(Constants.SPLIT_MULTIVALUE_SEPARATOR)).filter(value -> value.equals("SERVICE_CHARGEBACK"))
            .count());

        Assertions.assertEquals("", list.get(0).get("Error Message"));

    }

    @Test
    void testDownloadWhenPaymentFails() throws IOException{
        createDisputeWorkflow(DisputeType.UPI_CHARGEBACK);
        final var response = chargebackService.download(FileFormat.CSV, DateRangeFilter.builder()
            .dateRange(DateRange.builder()
                .startDate(LocalDate.now().minusDays(2))
                .endDate(LocalDate.now())
                .build())
            .disputeTypes(Set.of(DisputeTypeDto.UPI_CHARGEBACK))
            .build());
        final CsvMapper mapper = new CsvMapper();
        final CsvSchema schema = CsvSchema.emptySchema().withHeader();
        final List<Map<String, String>> list = new LinkedList<Map<String, String>>();
        final MappingIterator<Map<String, String>> iterator = mapper.readerWithTypedSchemaFor(
                Map.class)
            .with(schema)
            .readValues(response);
        while (iterator.hasNext()) {
            list.add(iterator.next());
        }

        Assertions.assertEquals(1, list.size());
        Assertions.assertEquals("TXN1234", list.get(0).get("Payment Reference Id"));
        Assertions.assertEquals(DisputeIssuer.YES_NPCI.toString(),
            list.get(0).get("Chargeback Issuer"));
        List.of("Original Transaction Date",
            "Original Transaction State",
            "Reversal Transaction Ids",
            "Reversal UTR Ids",
            "Reversal States",
            "Reversal Dates",
            "Reversal Amounts")
            .forEach(filed ->
                Assertions.assertEquals("", list.get(0).get(filed), filed +" empty assert failed"));
        Assertions.assertEquals("errorCode: COMMUNICATION_ERROR, serviceName: PaymentsTxnlClient, message: , serviceErrorCode: ", list.get(0).get("Error Message"));
    }

    @Test
    void testDownloadForEdc()  throws IOException{
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId("TXN1234", 100, 0, "MTXN1234");
        createDisputeWorkflow(DisputeType.EDC_CHARGEBACK);
        MockingUtils.setupGetMerchantProfileApiMocking("IRCTCINAPP",
            MerchantType.ONLINE_MERCHANT.name());
        final var response = chargebackService.download(FileFormat.CSV, DateRangeFilter.builder()
            .dateRange(DateRange.builder()
                .startDate(LocalDate.now().minusDays(2))
                .endDate(LocalDate.now())
                .build())
            .disputeTypes(Set.of(DisputeTypeDto.EDC_CHARGEBACK))
            .build());

        final CsvMapper mapper = new CsvMapper();
        final CsvSchema schema = CsvSchema.emptySchema().withHeader();
        final List<Map<String, String>> list = new LinkedList<Map<String, String>>();
        final MappingIterator<Map<String, String>> iterator = mapper.readerWithTypedSchemaFor(
                Map.class)
            .with(schema)
            .readValues(response);
        while (iterator.hasNext()) {
            list.add(iterator.next());
        }
        Assertions.assertEquals(1, list.size());
        Assertions.assertEquals("TXN1234", list.get(0).get("Payment Reference Id"));
        Assertions.assertEquals(DisputeIssuer.YES_NPCI.toString(),
            list.get(0).get("Chargeback Issuer"));
        Assertions.assertEquals("area",list.get(0).get("Area"));
        Assertions.assertEquals("city",list.get(0).get("City"));
        Assertions.assertEquals("state",list.get(0).get("State"));
        Assertions.assertEquals("12345",list.get(0).get("Pin Code"));
    }
    private DisputeWorkflow createDisputeWorkflow(final DisputeType disputeType){
        FinancialDisputeWorkflow disputeWorkflow = FinancialDisputeWorkflow.builder()
            .transactionReferenceId("TXN1234")
            .raisedAt(LocalDateTime.now())
            .respondBy(LocalDateTime.now().plusDays(15))
            .key(StorageUtils.primaryKey())
            .gandalfUserId("user-1234")
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .disputeWorkflowId("dispute-1234")
            .userType(UserType.USER)
            .disputeType(disputeType)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeSourceType(SourceType.FILE)
            .disputeSourceId("file-1234")
            .disputedAmount(100)
            .currentState(DisputeWorkflowState.REFUND_BLOCKED)
            .currentEvent(DisputeWorkflowEvent.BLOCK_REFUND)
            .disputeId("dispute-id-1234")
            .build();
        disputeWorkflowRepository.save(disputeWorkflow);
        disputeRepository.save(Dispute.builder()
            .currentDisputeStage(DisputeStage.FIRST_LEVEL)
            .disputeId("dispute-id-1234")
            .disputeReferenceId("reference-1234")
            .disputeType(disputeType)
            .disputeIssuer(DisputeIssuer.YES_NPCI)
            .instrumentTransactionId("inst1234")
            .key(StorageUtils.primaryKey())
            .merchantId("IRCTCINAPP")
            .merchantTransactionId("MTXN1234")
            .rrn("rrn1234")
            .disputeCategory(DisputeCategory.SERVICE_CHARGEBACK)
            .transactionAmount(100)
            .transactionReferenceId("TXN1234")
            .build());
        return disputeWorkflow;
    }

    private void createWalletDisputeMetadata(DisputeWorkflow disputeWorkflow){
        walletDisputeMetadataRepository.save(disputeMetadataHelper.toWalletDisputeMetadata(
            disputeWorkflow, "123"));
    }

    @Test
    public void testDownloadWhenOnlyFailedReversedTransactionsPresent() throws IOException {
        mocks("multiReversalFailedTransactionDetails.json");
        createDisputeWorkflow(DisputeType.UPI_CHARGEBACK);

        MockingUtils.setupGetMerchantProfileApiMocking("IRCTCINAPP",
                MerchantType.ONLINE_MERCHANT.name());

        final var response = chargebackService.download(FileFormat.CSV, DateRangeFilter.builder()
                .dateRange(DateRange.builder()
                        .startDate(LocalDate.now().minusDays(2))
                        .endDate(LocalDate.now())
                        .build())
                .disputeTypes(Set.of(DisputeTypeDto.UPI_CHARGEBACK))
                .build());
        final CsvMapper mapper = new CsvMapper();
        final CsvSchema schema = CsvSchema.emptySchema().withHeader();
        final List<Map<String, String>> list = new LinkedList<Map<String, String>>();
        final MappingIterator<Map<String, String>> iterator = mapper.readerWithTypedSchemaFor(
                        Map.class)
                .with(schema)
                .readValues(response);
        while (iterator.hasNext()) {
            list.add(iterator.next());
        }
        Assertions.assertEquals(1, Arrays.stream(list.get(0).get("Reversal States").split(Constants.SPLIT_MULTIVALUE_SEPARATOR)).count());
    }

    private void mocksEdc(final String fileName){
        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(
                    String.format("/v2/transactions/transaction/%s/detail", "TXN1234")))
                .willReturn(WireMock.aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody(TestDataUtils.getTransactionDetailWithMultiReversal("TXN1234", fileName))));
    }

    @Test
    void testNpciDownload() throws IOException {
        mocksForNpciDownload("externalRailWalletTransactionDetails.json", "WALLET");
        DisputeWorkflow disputeWorkflow = createDisputeWorkflow(DisputeType.WALLET_CHARGEBACK);
        createWalletDisputeMetadata(disputeWorkflow);
        final var response = chargebackService.npciDownload(FileFormat.CSV, DateRangeFilter.builder()
            .dateRange(DateRange.builder()
                .startDate(LocalDate.now().minusDays(2))
                .endDate(LocalDate.now())
                .build())
            .disputeTypes(Set.of(DisputeTypeDto.WALLET_CHARGEBACK))
            .build());
        final CsvMapper mapper = new CsvMapper();
        final CsvSchema schema = CsvSchema.emptySchema().withHeader();
        final List<Map<String, String>> list = new LinkedList<Map<String, String>>();
        final MappingIterator<Map<String, String>> iterator = mapper.readerWithTypedSchemaFor(
                Map.class)
            .with(schema)
            .readValues(response);
        while (iterator.hasNext()) {
            list.add(iterator.next());
        }
        Assertions.assertEquals(1, list.size());
        Assertions.assertEquals("TXN1234", list.get(0).get("Bankadjref"));
        Assertions.assertEquals("B", list.get(0).get("flag"));
        Assertions.assertEquals("123", list.get(0).get("Reason"));
        Assertions.assertEquals("rrn1234", list.get(0).get("shser"));
        Assertions.assertEquals("1",
            list.get(0).get("adjamt"));
    }

    @Test
    void testNpciChargebackFilter() throws IOException {
        mocksForNpciDownload("externalRailWalletTransactionDetails.json", "WALLET");
        DisputeWorkflow disputeWorkflow = createDisputeWorkflow(DisputeType.WALLET_CHARGEBACK);
        createWalletDisputeMetadata(disputeWorkflow);
        final List<NpciChargebackSummary> response = chargebackService.npciChargebackFilter(DateRangeFilter.builder()
            .dateRange(DateRange.builder()
                .startDate(LocalDate.now().minusDays(2))
                .endDate(LocalDate.now())
                .build())
            .disputeTypes(Set.of(DisputeTypeDto.WALLET_CHARGEBACK))
            .build());

        Assertions.assertEquals(1, response.size());
        Assertions.assertEquals("TXN1234", response.get(0).getBankadjref());
        Assertions.assertEquals("B", response.get(0).getFlag());
        Assertions.assertEquals(1L, response.get(0).getAdjamt());
        Assertions.assertEquals("rrn1234", response.get(0).getShser());
        Assertions.assertEquals("123", response.get(0).getReason());
    }

    @Test
    void testNpciDownloadForWalletForInternalMerchant() throws IOException {
        mocksForNpciDownload("internalRailWalletTransactionDetails.json", "UPI");
        DisputeWorkflow disputeWorkflow = createDisputeWorkflow(DisputeType.WALLET_CHARGEBACK);
        createWalletDisputeMetadata(disputeWorkflow);
        Assertions.assertThrows(DisputeException.class, ()->{
            chargebackService.npciDownload(FileFormat.CSV, DateRangeFilter.builder()
                .dateRange(DateRange.builder()
                    .startDate(LocalDate.now().minusDays(2))
                    .endDate(LocalDate.now())
                    .build())
                .disputeTypes(Set.of(DisputeTypeDto.WALLET_CHARGEBACK))
                .build());
        });
    }

    private void mocks(final String fileName) {
        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(
                    String.format("/v2/transactions/transaction/%s/detail", "TXN1234")))
                .willReturn(WireMock.aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody(TestDataUtils.getTransactionDetailWithMultiReversal("TXN1234", fileName))));

        // Refund Mocks
        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806943",
            AccountPaymentInstrument
                .builder()
                .utr("xyz1")
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806965",
            ExternalVpaPaymentInstrument
                .builder()
                .utr("xyz2")
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806975",
            DebitCardPaymentInstrument
                .builder()
                .arn("xyz3")
                .cardIssuer(CardIssuer.MASTER_CARD)
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806985",
            CreditCardPaymentInstrument
                .builder()
                .arn("xyz4")
                .cardIssuer(CardIssuer.MASTER_CARD)
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806995",
            ExternalWalletPaymentInstrument
                .builder()
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806905",
            WalletPaymentInstrument
                .builder()
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806906",
            NetBankingPaymentInstrument
                .builder()
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806907",
            BnplPaymentInstrument
                .builder()
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806908",
            GiftCardPaymentInstrument
                .builder()
                .build());

        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806909",
                CreditLinePaymentInstrument
                    .builder()
                    .build());
        MockingUtils.mockRefundApiWithPaymentInstrument("T2107142232477090806910",
                NcmcPaymentInstrument
                    .builder()
                    .build());
    }

    private void mocksForNpciDownload(String fileName, String type) {
        WireMock.stubFor(
            WireMock.get(WireMock.urlPathMatching(
                    String.format("/v2/transactions/transaction/%s/detail", "TXN1234")))
                .willReturn(WireMock.aResponse()
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withStatus(200)
                    .withBody(TestDataUtils.getPaymentTransactionsDetailsForWalletWithFileName("TXN1234", 123L, type,
                        String.valueOf(LocalDateTime.now()), fileName))));
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource(DISPUTE_TYPE_ARGS)
    void testNpciEnrichChargebackSummaryExceptWallet(DisputeType disputeType){

        DisputeWorkflow disputeWorkflow = FinancialDisputeWorkflow.builder()
            .disputeType(disputeType)
            .build();
        Assertions.assertThrows(DisputeException.class, ()->{
            chargebackService.enrichNpciChargebackSummaryRow(disputeWorkflow);});
    }
}