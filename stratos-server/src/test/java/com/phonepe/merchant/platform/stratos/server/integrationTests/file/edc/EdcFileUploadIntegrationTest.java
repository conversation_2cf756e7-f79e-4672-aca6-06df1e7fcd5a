package com.phonepe.merchant.platform.stratos.server.integrationTests.file.edc;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.resources.FileResource;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import java.util.concurrent.TimeUnit;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class EdcFileUploadIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private FileResource fileResource;
    private DisputeService disputeService;

    @BeforeEach
    void setUpGuiceInjection() {
        fileResource = guiceInjector.getInstance(FileResource.class);
        disputeService = guiceInjector.getInstance(DisputeService.class);
        truncateDb();
    }

    @Test
    public void test() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var merchantTransactionId = IdGenerator.generate("MT").getId();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForEdcCreateChargebackApiMocking();
        MockingUtils.setEdcTransactionDetails("BijliPay", "MERCHANTID", "TERMINALID", "RRN",
            transactionReferenceId, 100, 100, merchantTransactionId);
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(transactionReferenceId, 100, 100, merchantTransactionId);
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.setEdcUnBlockReversals(merchantTransactionId);
        final var userDetailsOlympus = HumanUserDetails.builder()
            .userId("test-user")
            .build();
        final var userAuthDetails = UserAuthDetails.builder()
            .userDetails(userDetailsOlympus)
            .build();
        final var serviceUserPrincipal = ServiceUserPrincipal.builder()
            .userAuthDetails(userAuthDetails)
            .build();

        var response = fileResource.upload(DisputeTypeDto.EDC_CHARGEBACK,
            FileTypeDto.EDC_FIRST_LEVEL, TestDataUtils.edcChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);
        final var disputeWorkflow = disputeService.
            getDisputeWorkflow(transactionReferenceId, DisputeType.EDC_CHARGEBACK,
                DisputeStage.FIRST_LEVEL);

        Assertions.assertTrue(disputeWorkflow instanceof FinancialDisputeWorkflow);
        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REPRESENTMENT_REQUIRED,
            5L, TimeUnit.SECONDS);

        disputeService.triggerEvent(
            userAuthDetails,
            transactionReferenceId,
            disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowEvent.COMPLETE_REPRESENTMENT,
            Constants.EMPTY_TRANSITION_CONTEXT);

        AssertionUtils.assertDisputeWorkflowStateEquals(
            transactionReferenceId, disputeWorkflow.getDisputeWorkflowId(),
            DisputeWorkflowState.REPRESENTMENT_COMPLETED,
            5L, TimeUnit.SECONDS);

        var dispute = disputeService.getDispute(transactionReferenceId, DisputeType.EDC_CHARGEBACK,
            DisputeStage.FIRST_LEVEL);
        Assertions.assertNotNull(dispute.getRrn());
        Assertions.assertNotNull(dispute.getDisputeCategory());


    }

    @Test
    public void testDuplicateFileUpload() throws Exception {
        final var transactionReferenceId = IdGenerator.generate("T").getId();
        final var merchantTransactionId = IdGenerator.generate("MT").getId();
        MockingUtils.setupDocstoreUploadApiMocking();
        MockingUtils.setupDocstoreGetFileForEdcCreateChargebackApiMocking();
        MockingUtils.setEdcTransactionDetails("BijliPay", "MERCHANTID", "TERMINALID", "RRN",
            transactionReferenceId, 100, 100, merchantTransactionId);
        MockingUtils.setupEdcTransactionsFromMerchantTransactionId(transactionReferenceId, 100, 100, merchantTransactionId);
        MockingUtils.setupEventIngestionApiMocking();
        MockingUtils.setEdcUnBlockReversals(transactionReferenceId);
        final var userDetailsOlympus = HumanUserDetails.builder()
            .userId("test-user")
            .build();
        final var userAuthDetails = UserAuthDetails.builder()
            .userDetails(userDetailsOlympus)
            .build();
        final var serviceUserPrincipal = ServiceUserPrincipal.builder()
            .userAuthDetails(userAuthDetails)
            .build();
        var response = fileResource.upload(DisputeTypeDto.EDC_CHARGEBACK,
            FileTypeDto.EDC_FIRST_LEVEL, TestDataUtils.edcChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName").fileName("file.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertFileProcessed(response.getId(), 20, TimeUnit.SECONDS);

        MockingUtils.setupDocstoreUploadApiMocking();
        var secondFileResponse = fileResource.upload(DisputeTypeDto.EDC_CHARGEBACK,
            FileTypeDto.EDC_FIRST_LEVEL, TestDataUtils.edcChargebackCSVFileStream(),
            FormDataContentDisposition.name("randomName1").fileName("file2.csv")
                .build(),"AuthToken", serviceUserPrincipal);

        AssertionUtils.assertRowStateEquals(RowState.FAILED, secondFileResponse.getId(), 15,
            TimeUnit.SECONDS);
    }
}
