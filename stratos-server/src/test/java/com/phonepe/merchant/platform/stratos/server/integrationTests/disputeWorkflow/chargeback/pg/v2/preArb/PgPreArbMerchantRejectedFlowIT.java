package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.preArb;


import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.pg.v2.firstLevel.PgFirstLevelMerchantRejectedFlowIT;

/*
PG Pre arb is same as first level hence same test cases
*/
public class PgPreArbMerchantRejectedFlowIT extends PgFirstLevelMerchantRejectedFlowIT {

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.PRE_ARBITRATION;
    }
}
