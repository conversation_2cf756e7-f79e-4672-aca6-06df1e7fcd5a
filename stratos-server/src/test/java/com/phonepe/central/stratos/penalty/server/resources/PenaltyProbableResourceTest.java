package com.phonepe.central.stratos.penalty.server.resources;

import com.phonepe.central.stratos.penalty.growth.GrowthUnit;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.request.PenaltyProbableRequest;
import com.phonepe.central.stratos.penalty.server.service.PenaltyAuthorizationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyProbableService;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import javax.ws.rs.core.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class PenaltyProbableResourceTest extends ErrorConfiguratorBaseTest {

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;
    @Mock
    private PenaltyAuthorizationService penaltyAuthorizationService;

    @Mock
    private ServiceUserPrincipal serviceUserPrincipal;

    private PenaltyProbableResource penaltyProbableResource;
    @Mock
    private PenaltyProbableService penaltyProbableService;

    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyProbableResource = new PenaltyProbableResource(penaltyProbableService, penaltyAuthorizationService);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testRegisterProbableNotAuthorized() {
        Response result = penaltyProbableResource.registerProbable(serviceUserPrincipal,
                PenaltyProbableRequest.builder()
                        .build());
        Assertions.assertEquals(Response.Status.FORBIDDEN.getStatusCode(), result.getStatus());
    }

    @Test
    public void testRegisterProbableValidInput() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToRegisterProbable(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        Response result = penaltyProbableResource.registerProbable(serviceUserPrincipal,
                PenaltyProbableRequest.builder()
                        .build());
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), result.getStatus());
        Mockito.verify(penaltyProbableService, Mockito.times(1))
                .registerProbable(Mockito.any());
    }

    @Test
    public void testRegisterProbableValidInputWithException() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToRegisterProbable(Mockito.any(), Mockito.any()))
                .thenThrow(new RuntimeException("Test Exception"));

        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyProbableResource.registerProbable(serviceUserPrincipal, PenaltyProbableRequest.builder()
                    .build());
        });
        Mockito.verify(penaltyProbableService, Mockito.times(0))
                .registerProbable(Mockito.any());
    }

    @Test
    public void testSearchNotAuthorisedProbables() {
        Response result = penaltyProbableResource.getProbableBy(serviceUserPrincipal, "1234567890", 10, 1);
        Assertions.assertEquals(Response.Status.FORBIDDEN.getStatusCode(), result.getStatus());
    }

    @Test
    public void testSearchValidTenantProbables() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToViewProbable(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        Response result = penaltyProbableResource.getProbableBy(serviceUserPrincipal, "1234567890", 10, 1);
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), result.getStatus());
        Mockito.verify(penaltyProbableService, Mockito.times(1))
                .getPaginatedPenaltyProbables(Mockito.any(), Mockito.anyInt());
    }

    @Test
    public void testSearchValidTenantProbablesWithException() {
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToViewProbable(Mockito.any(), Mockito.any()))
                .thenThrow(new RuntimeException("Test Exception"));

        Assertions.assertThrows(RuntimeException.class, () -> {
            Response result = penaltyProbableResource.getProbableBy(serviceUserPrincipal, "1234567890", 10, 1);
        });
        Mockito.verify(penaltyProbableService, Mockito.times(0))
                .getPaginatedPenaltyProbables(Mockito.any(), Mockito.anyInt());
    }

    @Test
    public void testReconcileByDateRangeISMoreThanOneDay() {
        Response response = penaltyProbableResource.reconcileByRange(serviceUserPrincipal, DateRangeRequest.builder()
                .toDate(PenaltyUtil.getEndDate(GrowthUnit.DAY))
                .fromDate(PenaltyUtil.getEndDate(GrowthUnit.WEEK))
                .build());
        Assertions.assertEquals(Response.Status.BAD_REQUEST.getStatusCode(), response.getStatus());
    }


    @Test
    public void testReconcileByDateRange() {
        Response response = penaltyProbableResource.reconcileByRange(serviceUserPrincipal, DateRangeRequest.builder()
                .toDate(PenaltyUtil.getEndDate(GrowthUnit.DAY))
                .fromDate(PenaltyUtil.getEndDate(GrowthUnit.DAY))
                .build());
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        Mockito.verify(penaltyProbableService, Mockito.times(1))
                .reconcile(Mockito.any(DateRangeRequest.class));
    }

    @Test
    public void testReconcileByDateRangeWithException() {
        Assertions.assertThrows(Exception.class, () -> {
            Response response = penaltyProbableResource.reconcileByRange(serviceUserPrincipal, null);
        });
        Mockito.verify(penaltyProbableService, Mockito.times(0))
                .reconcile(Mockito.any(DateRangeRequest.class));
    }

    @Test
    public void testReconcileByProbableId() {
        Response response = penaltyProbableResource.reconcileByProbableId(serviceUserPrincipal, "probableId");
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        Mockito.verify(penaltyProbableService, Mockito.times(1))
                .reconcile(Mockito.anyString());
    }

    @Test
    public void testReconcileByProbableIdWithException() {
        Mockito.when(penaltyProbableService.reconcile(Mockito.anyString()))
                .thenThrow(new RuntimeException("Test Exception"));
        Assertions.assertThrows(Exception.class, () -> {
            Response response = penaltyProbableResource.reconcileByProbableId(serviceUserPrincipal, "probableId");
        });
        Mockito.verify(penaltyProbableService, Mockito.times(1))
                .reconcile(Mockito.anyString());
    }

    @Test
    public void testRemoveByProbableId() {
        Response response = penaltyProbableResource.removeProbable(serviceUserPrincipal, "probableId");
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        Mockito.verify(penaltyProbableService, Mockito.times(1))
                .removeProbableFromStorage(Mockito.anyString());
    }

    @Test
    public void testRemoveByProbableIdWithException() {

        Mockito.doThrow(new RuntimeException("Test Exception"))
                .when(penaltyProbableService)
                .removeProbableFromStorage(Mockito.anyString());
        Assertions.assertThrows(Exception.class, () -> {
            Response response = penaltyProbableResource.removeProbable(serviceUserPrincipal, "probableId");
        });
        Mockito.verify(penaltyProbableService, Mockito.times(1))
                .removeProbableFromStorage(Mockito.anyString());
    }


}
