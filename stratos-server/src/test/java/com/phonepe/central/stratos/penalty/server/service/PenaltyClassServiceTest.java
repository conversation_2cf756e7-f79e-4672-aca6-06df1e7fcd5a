package com.phonepe.central.stratos.penalty.server.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassState;
import com.phonepe.central.stratos.penalty.meta.PenaltyCriteria;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.recovery.PenalizedEntity;
import com.phonepe.central.stratos.penalty.recovery.PenalizedEntityType;
import com.phonepe.central.stratos.penalty.recovery.impl.MerchantPenalizedEntity;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassBasicUpdateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCreateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCriteriaUpdateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassSearchRequest;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.central.stratos.penalty.server.config.PenaltyTenantVersionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ClientPenaltyResolutionType;
import com.phonepe.central.stratos.penalty.server.config.client.PassThroughProbableClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.disbursement.PenaltyDisbursementCategoryType;
import com.phonepe.central.stratos.penalty.server.config.disbursement.PenaltyDisbursementConfig;
import com.phonepe.central.stratos.penalty.server.config.disbursement.toa.MerchantPenaltyToADisbursementConfig;
import com.phonepe.central.stratos.penalty.server.convertor.PenaltyClassConvertors;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassDetailEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyClassDetailRepository;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyClassRepository;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.services.WardenService;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.services.warden.core.models.responses.config.WardenWorkflowConfig;
import com.phonepe.services.warden.core.models.responses.instance.WardenWorkflowInstance;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */

@ExtendWith(MockitoExtension.class)
public class PenaltyClassServiceTest extends ErrorConfiguratorBaseTest {

    @Mock
    private PenaltyClassRepository penaltyClassRepository;
    @Mock
    private PenaltyClassDetailRepository penaltyClassDetailRepository;
    @Mock
    private EscalationService escalationService;

    private Map<String, PenaltyTenantVersionConfig> penaltyValidationConfigsMap =new HashMap<>();
    @Mock
    private WardenService wardenService;

    private PenaltyClassService penaltyClassService;

    @Mock
    private EventIngester eventIngester;

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;


    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyClassService = new PenaltyClassService(penaltyClassRepository, penaltyClassDetailRepository,
                escalationService, penaltyValidationConfigsMap, wardenService, eventIngester);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testGetClassConfigsWithValidInput() {
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();

        when(penaltyClassRepository.getClassConfigs(any(), any())).thenReturn(List.of(penaltyClassEntity));
        when(escalationService.getEscalations("classId")).thenReturn(List.of(PenaltyTestObjectUtil.getEscalationResponse()));
        List<PenaltyClass> result = penaltyClassService.getClassConfigs(PenaltyClassSearchRequest.builder()
                .penaltyClassId(penaltyClassEntity.getPenaltyClassId())
                .tenantName(penaltyClassEntity.getTenant_name())
                .build(), List.of(PenaltyTestObjectUtil.getTenantInfo()));
        assertEquals(1, result.size());
        verify(penaltyClassRepository, times(1)).getClassConfigs(any(), any());
        verify(escalationService, times(1)).getEscalations("classId");
    }

    @Test
    public void testGetClassConfigsWithEmptyEntityList() {
        when(penaltyClassRepository.getClassConfigs(any(), any())).thenReturn(Collections.emptyList());

        List<PenaltyClass> result = penaltyClassService.getClassConfigs(mock(PenaltyClassSearchRequest.class),
                List.of(mock(TenantInfo.class)));

        assertEquals(0, result.size());
        verify(penaltyClassRepository, times(1)).getClassConfigs(any(), any());
        verify(escalationService, never()).getEscalations(any());
    }

    @Test
    public void testGetClassConfigsWithNullEscalationList() {
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();

        when(penaltyClassRepository.getClassConfigs(any(), any())).thenReturn(List.of(penaltyClassEntity));
        when(escalationService.getEscalations("classId")).thenReturn(Collections.emptyList());
        List<PenaltyClass> result = penaltyClassService.getClassConfigs(PenaltyClassSearchRequest.builder()
                .penaltyClassId(penaltyClassEntity.getPenaltyClassId())
                .tenantName(penaltyClassEntity.getTenant_name())
                .build(), List.of(TenantInfo.builder()
                        .name(penaltyClassEntity.getName())
                        .subCategory(penaltyClassEntity.getTenant_subcategory_name())
                .build()));

        assertEquals(1, result.size());
        verify(penaltyClassRepository, times(1)).getClassConfigs(any(), any());
        verify(escalationService, times(1)).getEscalations("classId");
    }

    @Test
    public void testGetClassConfigsWithException() {
        when(penaltyClassRepository.getClassConfigs(any(), any())).thenThrow(new RuntimeException("Error"));
        try {
            penaltyClassService.getClassConfigs(mock(PenaltyClassSearchRequest.class), List.of(mock(TenantInfo.class)));
        } catch (Exception e) {
            assertEquals("Error", e.getMessage());
        }
        verify(penaltyClassRepository, times(1)).getClassConfigs(any(), any());
    }

    @Test
    public void testGetClassWithPenaltyClassIdNotPresent() {
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Optional<PenaltyClass> penaltyClassConfig = penaltyClassService.getClassFor(
                penaltyClassEntity.getPenaltyClassId());
        assertFalse(penaltyClassConfig.isPresent());
    }

    @Test
    public void testGetClassWithPenaltyClassIdPresent() {
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassRepository.getClassConfig(penaltyClassEntity.getPenaltyClassId()))
                .thenReturn(Optional.of(penaltyClassEntity));
        Optional<PenaltyClass> penaltyClassConfig = penaltyClassService.getClassFor(
                penaltyClassEntity.getPenaltyClassId());
        assertTrue(penaltyClassConfig.isPresent());
    }

    @Test
    public void testGetTenantInfo() {
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassRepository.getClassConfig(penaltyClassEntity.getPenaltyClassId()))
                .thenReturn(Optional.of(penaltyClassEntity));
        TenantInfo tenantInfo = penaltyClassService.getTenantInfo(penaltyClassEntity.getPenaltyClassId());
        assertEquals(penaltyClassEntity.getTenant_name(), tenantInfo.getName());
    }

    @Test
    public void testGetTenantInfoNotPresent() {
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassRepository.getClassConfig(penaltyClassEntity.getPenaltyClassId()))
                .thenReturn(Optional.empty());
        assertThrows(RuntimeException.class, () -> {
            penaltyClassService.getTenantInfo(penaltyClassEntity.getPenaltyClassId());
        });
    }

    @Test
    public void testCreateClassConfigWhenTenantHasSameName(){
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassRepository.getClassConfigs(PenaltyClassSearchRequest.builder()
                        .tenantName(PenaltyTestObjectUtil.getTenantInfo().getName())
                        .tenantSubCategory(PenaltyTestObjectUtil.getTenantInfo().getSubCategory())
                        .build(), List.of(PenaltyTestObjectUtil.getTenantInfo())))
                .thenReturn(List.of(penaltyClassEntity));
        PenaltyClassCreateRequest createRequest = PenaltyTestObjectUtil.getPenaltyClassCreateRequest();
        assertThrows(RuntimeException.class, () -> {
            penaltyClassService.createConfig(createRequest);
        });

    }

    @Test
    public void testCreateClassConfigWhenAlready(){
        PenaltyClassCreateRequest createRequest = null;
        assertFalse(penaltyClassService.createConfig(createRequest).isPresent());
    }

    @Test
    public void testValidPenaltyClassConfigStoreWithVersionIncrement(){
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        penaltyClassEntity.setName("TempNameForVersion");
        Mockito.when(penaltyClassRepository.getClassConfigs(PenaltyClassSearchRequest.builder()
                        .tenantName(PenaltyTestObjectUtil.getTenantInfo().getName())
                        .tenantSubCategory(PenaltyTestObjectUtil.getTenantInfo().getSubCategory())
                        .build(), List.of(PenaltyTestObjectUtil.getTenantInfo())))
                .thenReturn(List.of(penaltyClassEntity));
        PenaltyClassCreateRequest createRequest = PenaltyTestObjectUtil.getPenaltyClassCreateRequest();
        Mockito.when(penaltyClassRepository.save(Mockito.any(PenaltyClassEntity.class),Mockito.any())).thenReturn(Optional.of(penaltyClassEntity));
        Optional<PenaltyClass> result = penaltyClassService.createConfig(createRequest);
        Mockito.verify(penaltyClassRepository, times(1)).save(Mockito.any(PenaltyClassEntity.class),Mockito.any());
    }

    @Test
    public void testValidPenaltyClassConfigBasicUpdate() {
        TenantInfo tenantInfo = PenaltyTestObjectUtil.getTenantInfo();
        PenaltyClassEntity storedEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        PenaltyClassBasicUpdateRequest updateRequest = PenaltyTestObjectUtil.getPenaltyClassBasicUpdate();

        when(penaltyClassRepository.getClassConfig(eq(updateRequest.getPenaltyClassId()), anyList()))
                .thenReturn(Optional.of(storedEntity));

        when(penaltyClassRepository.save(any(PenaltyClassEntity.class)))
                .thenReturn(Optional.of(storedEntity));

        String result = penaltyClassService.updateClass(updateRequest, List.of(tenantInfo));

        assertNotNull(result);
        assertEquals(updateRequest.getPenaltyClassId(), result);
        assertEquals("description_updated", storedEntity.getDescription());
    }


    @Test
    public void testValidPenaltyClassConfigCriteriaUpdate(){
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        MapperUtils.init(objectMapper);
        PenaltyClassCriteriaUpdateRequest updateRequest = PenaltyTestObjectUtil.getPenaltyCriteriaUpdate();
        updateRequest.setDetails(List.of(PenaltyTestObjectUtil.getPenaltyClassDetail(), PenaltyTestObjectUtil.getPenaltyClassDetail()));
        when(penaltyClassRepository.getClassConfig(eq(updateRequest.getPenaltyClassId()), anyList()))
                .thenReturn(Optional.of(penaltyClassEntity));
        when(penaltyClassDetailRepository.getClassDetailsConfig(updateRequest.getPenaltyClassId()))
                .thenReturn(List.of(PenaltyClassConvertors.convert(PenaltyTestObjectUtil.getPenaltyClassDetail())));
        String result = penaltyClassService.updateClass(updateRequest, List.of(PenaltyTestObjectUtil.getTenantInfo()));
        assertEquals(updateRequest.getPenaltyClassId(), result);
        Mockito.verify(penaltyClassDetailRepository, times(1)).save(Mockito.any(PenaltyClassDetailEntity.class));
    }

    @Test
    public void testClientResolutionConfigNotPresent(){
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassRepository.getClassConfig(penaltyClassEntity.getPenaltyClassId(), List.of(PenaltyTestObjectUtil.getTenantInfo())))
                .thenReturn(Optional.of(penaltyClassEntity));
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClassService.getClientResolutionConfig(penaltyClassEntity.getPenaltyClassId());
        });
    }

    @Test
    public void testClientResolutionConfigPresent(){

        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassRepository.getClassConfig(penaltyClassEntity.getPenaltyClassId()))
                .thenReturn(Optional.of(penaltyClassEntity));
        String key = penaltyClassEntity.getTenant_name()
                + ":" + penaltyClassEntity.getTenant_subcategory_name()
                + ":" + penaltyClassEntity.getVersion();
        penaltyValidationConfigsMap.put(key,PenaltyTenantVersionConfig.builder()
                .clientPenaltyResolutionConfig(PassThroughProbableClientPenaltyResolutionConfig.builder().build())
                .disbursementConfig(MerchantPenaltyToADisbursementConfig.builder()
                        .build()).build());
        ClientPenaltyResolutionConfig resolutionConfig = penaltyClassService.getClientResolutionConfig(
                penaltyClassEntity.getPenaltyClassId());
        assertEquals(resolutionConfig.getType(), ClientPenaltyResolutionType.PASS_THROUGH_PROBABLE);
    }

    @Test
    public void testDisbursementConfigPresent(){

        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassRepository.getClassConfig(penaltyClassEntity.getPenaltyClassId()))
                .thenReturn(Optional.of(penaltyClassEntity));
        String key = penaltyClassEntity.getTenant_name()
                + ":" + penaltyClassEntity.getTenant_subcategory_name()
                + ":" + penaltyClassEntity.getVersion();
        penaltyValidationConfigsMap.put(key,PenaltyTenantVersionConfig.builder()
                .clientPenaltyResolutionConfig(PassThroughProbableClientPenaltyResolutionConfig.builder().build())
                .disbursementConfig(MerchantPenaltyToADisbursementConfig.builder()
                        .build()).build());
        PenaltyDisbursementConfig disbursementConfig = penaltyClassService.getClassDisbursementConfig(
                penaltyClassEntity.getPenaltyClassId());
        assertEquals(disbursementConfig.getType(), PenaltyDisbursementCategoryType.MERCHANT_PENALTY);
    }

    @Test
    public void testPenalisedEntityPresent(){

        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassRepository.getClassConfig(penaltyClassEntity.getPenaltyClassId()))
                .thenReturn(Optional.of(penaltyClassEntity));
        String key = penaltyClassEntity.getTenant_name()
                + ":" + penaltyClassEntity.getTenant_subcategory_name()
                + ":" + penaltyClassEntity.getVersion();
        penaltyValidationConfigsMap.put(key,PenaltyTenantVersionConfig.builder()
                .clientPenaltyResolutionConfig(PassThroughProbableClientPenaltyResolutionConfig.builder().build())
                .disbursementConfig(MerchantPenaltyToADisbursementConfig.builder()
                        .build()).penaltyRecoveryConfig(MerchantPenalizedEntity.builder()
                        .merchantId("merchantId")
                        .build()).build());
        PenalizedEntity penalisedEntity = penaltyClassService.getPenalizedEntity(
                penaltyClassEntity.getPenaltyClassId());
        assertEquals(penalisedEntity.getType(), PenalizedEntityType.MERCHANT_PENALIZED_ENTITY);
    }

    @Test
    public void testPenaltyClassDetailsAbovePenaltyCapEmpty() {
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        Mockito.when(penaltyClassDetailRepository.getClassDetailsConfig(penaltyClassEntity.getPenaltyClassId(),100L))
                .thenReturn(List.of());
        assertThrows(RuntimeException.class, () -> {
            List<PenaltyClassDetail> result = penaltyClassService.getClassDetailAbovePenaltyCap(
                    penaltyClassEntity.getPenaltyClassId(), 100L);
        });

    }

    @Test
    public void testPenaltyClassDetailsAbovePenaltyCap() {
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        MapperUtils.init(new ObjectMapper());
        PenaltyCriteria criteria = PenaltyTestObjectUtil.getPenaltyClassDetail()
                .getCriteria();
        criteria.setLeeway(null);
        Mockito.when(penaltyClassDetailRepository.getClassDetailsConfig(penaltyClassEntity.getPenaltyClassId(),100L))

                .thenReturn(List.of(PenaltyClassDetailEntity.builder()
                                .label("label")
                                .penaltyCap(1000L)
                                .criteriaConfigData(MapperUtils.serializeToString(criteria))
                                .growthConfigData(MapperUtils.serializeToString(PenaltyTestObjectUtil.getPenaltyClassDetail().getGrowthRate()))
                        .build()));

        List<PenaltyClassDetail> result = penaltyClassService.getClassDetailAbovePenaltyCap(
                penaltyClassEntity.getPenaltyClassId(), 100L);
        assertEquals(1, result.size());

    }

    @Test
    public void testActivatePenaltyClassSuccess() {
        PenaltyClass penaltyClass = PenaltyTestObjectUtil.getPenaltyClass();
        penaltyClassService.activatePenaltyClass(penaltyClass);
        verify(penaltyClassRepository, times(1)).activatePenaltyClass("classId");
        assertEquals(PenaltyClassState.ACTIVE, penaltyClass.getState());
    }

    @Test
    public void testActivatePenaltyClassException() {
        PenaltyClass penaltyClass = PenaltyClass.builder()
                .id("classId")
                .name("TestClass")
                .build();

        doThrow(new RuntimeException("Database error")).when(penaltyClassRepository)
                .activatePenaltyClass("classId");

        Exception exception = assertThrows(RuntimeException.class, () -> {
            penaltyClassService.activatePenaltyClass(penaltyClass);
        });
    }


    @Test
    public void testCreateWardenCallbackConfigOnboardForClassWithValidInput() {
        TenantInfo tenantInfo = PenaltyTestObjectUtil.getTenantInfo();
        Integer version = 1;
        String authToken = "authToken";
        List<UserDetails> userDetails = List.of(mock(UserDetails.class));
        String workflowType = "PENALTY_CLASS_CREATION_Tenant1_SubCategory1_1";

        WardenWorkflowConfig wardenWorkflowConfig = mock(WardenWorkflowConfig.class);
        WardenWorkflowInstance wardenWorkflowInstance = mock(WardenWorkflowInstance.class);

        when(wardenService.createNewWardenWorkflowConfig(anyString(), anyString(), anyList()))
                .thenReturn(wardenWorkflowConfig);
        PenaltyClassEntity penaltyClassEntity = PenaltyTestObjectUtil.getPenaltyClassEntity();
        when(penaltyClassRepository.getClassConfigs(any(), any())).thenReturn(List.of(penaltyClassEntity));

        when(wardenService.raisePenaltyClassCreation(Mockito.any(PenaltyClass.class), anyString(), anyString()))
                .thenReturn(wardenWorkflowInstance);

        WardenWorkflowInstance result = penaltyClassService.createWardenCallbackConfigOnboardForClass(
                tenantInfo, version, authToken, userDetails);

        assertNotNull(result);
        assertEquals(wardenWorkflowInstance, result);
        verify(wardenService, times(1)).createNewWardenWorkflowConfig(anyString(), anyString(), anyList());
        verify(wardenService, times(1)).raisePenaltyClassCreation(Mockito.any(PenaltyClass.class), anyString(), anyString());
    }

    @Test
    public void testDuplicateWhenClassNotFound(){
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClassService.duplicate("penaltyClassId");
        });
    }

    @Test
    public void testDuplicateEmptyCheck(){
        Mockito.when(penaltyClassRepository.getClassConfig("penaltyClassId")).thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClassEntity()));
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClassService.duplicate("penaltyClassId");
        });
    }



}
