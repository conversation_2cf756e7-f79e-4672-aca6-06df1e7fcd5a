package com.phonepe.central.stratos.penalty.server.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.inject.Inject;
import com.phonepe.central.stratos.notification.EmailCommunicationRequest;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassState;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.request.PenaltyProbableRequest;
import com.phonepe.central.stratos.penalty.response.PenaltyProbableResponse;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.central.stratos.penalty.server.aerospike.AeroSpikeRepository;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyClassRepository;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.logging.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PenaltyProbableServiceTest extends LoadOnlyOnClassLevelBaseTest {
    @Inject
    private PenaltyProbableService penaltyProbableService;

    @Inject
    public PenaltyClassRepository penaltyClassRepository;

    @Mock
    private Logger mockLogger;
    @Mock
    private AeroSpikeRepository aeroSpikeRepository;
    @Mock
    private PenaltyService penaltyService;
    @Mock
    private ClockworkService clockworkService;
    @Mock
    private PenaltyClientService penaltyClientService;
    @Mock
    private PenaltyEvaluationService penaltyEvaluationService;
    @Mock
    private PenaltyClassService penaltyClassService;
    @Mock
    private NotificationService notificationService;
    @Mock
    private EventIngester eventIngester;


    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyProbableService = new PenaltyProbableService(aeroSpikeRepository, penaltyService, clockworkService,
                penaltyClientService, penaltyEvaluationService, penaltyClassService, notificationService, eventIngester);
        Mockito.when(penaltyClassService.getTenantInfo("classId"))
                .thenReturn(TenantInfo.builder()
                        .name("testTenant")
                        .subCategory("testSubCategory")
                        .build());
    }

    @Test
    void testPenaltyProbableNotification() throws JsonProcessingException {
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
            .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        PenaltyProbable penaltyProbable = PenaltyTestObjectUtil.getPenaltyProbable();
        Assertions.assertDoesNotThrow(
            () -> penaltyProbableService.sendNotificationForPenaltyDueShortly(
                List.of(penaltyProbable),
                EmailCommunicationRequest.builder().emailIDs(
                    Set.of("emailIds")).build(), DateRangeRequest.builder()
                    .toDate(new Date())
                    .fromDate(new Date())
                    .build()));
    }

    @Test
    public void testRegisterProbableWithAlreadyPresent() {
        PenaltyProbableRequest penaltyProbableRequest = PenaltyTestObjectUtil.getPenaltyProbableRequest();
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(PenaltyTestObjectUtil.getPenaltyProbable());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyProbableService.registerProbable(penaltyProbableRequest);
        });
    }

    @Test
    public void testRegisterProbableAlreadyPresentForNotActiveClassOnly() {
        PenaltyProbableRequest penaltyProbableRequest = PenaltyTestObjectUtil.getPenaltyProbableRequest();
        PenaltyClass penaltyClass = PenaltyTestObjectUtil.getPenaltyClass();
        penaltyClass.setState(PenaltyClassState.CREATED);
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyProbableService.registerProbable(penaltyProbableRequest);
        });
    }

    @Test
    public void testRegisterProbableAlreadyPresentForNoClassFoundToCheckActive() {
        PenaltyProbableRequest penaltyProbableRequest = PenaltyTestObjectUtil.getPenaltyProbableRequest();
        PenaltyClass penaltyClass = PenaltyTestObjectUtil.getPenaltyClass();
        penaltyClass.setState(PenaltyClassState.CREATED);
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
                .thenReturn(Optional.empty());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyProbableService.registerProbable(penaltyProbableRequest);
        });
    }


    @Test
    void testSendNotificationForPenaltyClassNotPresent(){
        PenaltyProbable penaltyProbable = PenaltyTestObjectUtil.getPenaltyProbable();
        final var errorCode = Assertions.assertThrows(DisputeException.class,
            () -> penaltyProbableService.
                sendNotificationForPenaltyDueShortly(List.of(penaltyProbable),
                    EmailCommunicationRequest.builder()
                        .emailIDs(Set.of("phonepe.com"))
                        .build(), DateRangeRequest.builder()
                        .fromDate(new Date())
                        .toDate(new Date())
                        .build()));
        Assertions.assertEquals("penalty class not present", errorCode.getMessage());
    }

    @Test
    void testPenaltySendNotificationForFailure() {
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
            .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        PenaltyProbable penaltyProbable = PenaltyTestObjectUtil.getPenaltyProbable();
        Mockito.doThrow(DisputeException.class).when(notificationService).sendNotification(Mockito.any(),
            Mockito.any(), Mockito.any(), Mockito.any());
        Assertions.assertDoesNotThrow(
            () -> penaltyProbableService.sendNotificationForPenaltyDueShortly(
                List.of(penaltyProbable),
                EmailCommunicationRequest.builder()
                    .emailIDs(Set.of("phonepe.com"))
                    .build(), DateRangeRequest.builder()
                    .fromDate(new Date())
                    .toDate(new Date())
                    .build()));
        Mockito.verify(eventIngester, Mockito.times(1)).generateEvent(
            Mockito.any()
        );
    }

    @Test
    void testRegisterProbableWithPenaltyAlreadyExist() {
        PenaltyProbableRequest penaltyProbableRequest = PenaltyTestObjectUtil.getPenaltyProbableRequest();
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Mockito.when(penaltyService.getPenaltyForProbableId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(PenaltyTestObjectUtil.getPenaltyInstance());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyProbableService.registerProbable(penaltyProbableRequest);
        });
    }

    @Test
    void testPenaltySendNotificationForRuntimeException() {
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
            .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        PenaltyProbable penaltyProbable = PenaltyTestObjectUtil.getPenaltyProbable();
        Mockito.doThrow(RuntimeException.class).when(notificationService).sendNotification(Mockito.any(),
            Mockito.any(), Mockito.any(), Mockito.any());
        Assertions.assertDoesNotThrow(
            () -> penaltyProbableService.sendNotificationForPenaltyDueShortly(
                List.of(penaltyProbable),
                EmailCommunicationRequest.builder()
                    .emailIDs(Set.of("phonepe.com"))
                    .build(), DateRangeRequest.builder()
                    .fromDate(new Date())
                    .toDate(new Date())
                    .build()));
        Mockito.verify(eventIngester, Mockito.times(1)).generateEvent(
            Mockito.any());
    }

    @Test
    void testRegisterProbableWithPenaltyClassCriteriaNotMet() {
        PenaltyProbableRequest penaltyProbableRequest = PenaltyTestObjectUtil.getPenaltyProbableRequest();
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Mockito.when(penaltyService.getPenaltyForProbableId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(penaltyEvaluationService.isQualifiedForCriteria(Mockito.any(PenaltyProbable.class)))
                .thenReturn(false);
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyProbableService.registerProbable(penaltyProbableRequest);
        });
    }

    @Test
    public void testRegisterProbableWithPenaltyClassCriteriaMet() {
        PenaltyProbableRequest penaltyProbableRequest = PenaltyTestObjectUtil.getPenaltyProbableRequest();
        penaltyProbableRequest.setDueDate(PenaltyUtil.addDaystoDate(new Date(),2));
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(penaltyClassService.getClassFor(Mockito.anyString()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        Mockito.when(penaltyService.getPenaltyForProbableId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(penaltyEvaluationService.isQualifiedForCriteria(Mockito.any(PenaltyProbable.class)))
                .thenReturn(true);
        Assertions.assertDoesNotThrow(() -> {
            penaltyProbableService.registerProbable(penaltyProbableRequest);
        });
        Mockito.verify(clockworkService, Mockito.times(1))
                .scheduleProbableAtDueTime(Mockito.any(PenaltyProbable.class));
    }

    @Test
    public void testReconcileWithException() {
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(null);
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyProbableService.reconcile(PenaltyTestObjectUtil.getPenaltyProbable()
                    .getProbableId());
        });
    }

    @Test
    public void testReconcileWithNoException() {
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(PenaltyTestObjectUtil.getPenaltyProbable());
        Assertions.assertDoesNotThrow(() -> {
            penaltyProbableService.reconcile(PenaltyTestObjectUtil.getPenaltyProbable()
                    .getProbableId());
        });
        Mockito.verify(aeroSpikeRepository, Mockito.times(1))
                .delete(Mockito.anyString());
        Mockito.verify(penaltyService, Mockito.times(1))
                .createNewPenalty(Mockito.any(PenaltyProbable.class));
    }

    @Test
    public void testReconcileWithProbableResolved() {
        Mockito.when(aeroSpikeRepository.get(Mockito.anyString()))
                .thenReturn(PenaltyTestObjectUtil.getPenaltyProbable());
        Mockito.when(penaltyClientService.isProbableResolved(Mockito.any(PenaltyProbable.class))).thenReturn(Boolean.TRUE);
        Assertions.assertThrows(DisputeException.class, () -> {
            penaltyProbableService.reconcile(PenaltyTestObjectUtil.getPenaltyProbable()
                    .getProbableId());
        });
        Mockito.verify(penaltyService, Mockito.times(0))
                .createNewPenalty(Mockito.any(PenaltyProbable.class));
    }

    @Test
    public void testDateRangeReconcileWithException() {
        Mockito.when(aeroSpikeRepository.getDueProbableEntriesBetween(Mockito.any(), Mockito.any()))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyProbable()));

        penaltyProbableService.reconcile(DateRangeRequest.builder()
                .fromDate(PenaltyTestObjectUtil.getPenaltyProbable()
                        .getDueDate())
                .toDate(PenaltyTestObjectUtil.getPenaltyProbable()
                        .getDueDate())
                .build());
        Mockito.verify(aeroSpikeRepository, Mockito.times(0))
                .delete(Mockito.anyString());
        Mockito.verify(penaltyService, Mockito.times(0))
                .createNewPenalty(Mockito.any(PenaltyProbable.class));
    }
    @Test
    public void testDateRangeReconcile() {
        Mockito.when(aeroSpikeRepository.getDueProbableEntriesBetween(Mockito.any(), Mockito.any()))
                .thenReturn(List.of(PenaltyTestObjectUtil.getPenaltyProbable()));

        penaltyProbableService.reconcile(DateRangeRequest.builder()
                .fromDate(PenaltyTestObjectUtil.getPenaltyProbable()
                        .getDueDate())
                .toDate(PenaltyTestObjectUtil.getPenaltyProbable()
                        .getDueDate())
                .build());
        Mockito.verify(aeroSpikeRepository, Mockito.times(0))
                .delete(Mockito.anyString());
        Mockito.verify(penaltyService, Mockito.times(0))
                .createNewPenalty(Mockito.any(PenaltyProbable.class));
    }

    @Test
    public void testGetPaginatedPenaltyProbables() {
        PenaltyProbable probableOne = PenaltyTestObjectUtil.getPenaltyProbable();
        probableOne.setPenaltyClassId("1234567890");
        PenaltyProbable probableTwo = PenaltyTestObjectUtil.getPenaltyProbable();
        Mockito.when(aeroSpikeRepository.getPaginatedProbableEntries(Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(List.of(probableOne,probableTwo));
        Mockito.when(penaltyClassService.getClassFor(PenaltyTestObjectUtil.getPenaltyProbable().getPenaltyClassId()))
                .thenReturn(Optional.of(PenaltyTestObjectUtil.getPenaltyClass()));
        List<PenaltyProbableResponse> result = penaltyProbableService.getPaginatedPenaltyProbables("1234567890", 10);
        Assertions.assertEquals(1, result.size());
    }

    @Test
    public void testAsActiveProbableExistAgainstClass(){
        boolean result = penaltyProbableService.isActiveProbableExistAgainstClass("1234567890");
        Assertions.assertFalse(result, "Expected no active probable against class ID 1234567890");
    }


}
