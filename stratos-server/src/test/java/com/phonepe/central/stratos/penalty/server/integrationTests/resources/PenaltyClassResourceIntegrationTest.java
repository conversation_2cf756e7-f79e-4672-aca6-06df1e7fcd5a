package com.phonepe.central.stratos.penalty.server.integrationTests.resources;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCreateRequest;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyClassDetailRepository;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyClassRepository;
import com.phonepe.central.stratos.penalty.server.resources.PenaltyClassResource;
import com.phonepe.central.stratos.penalty.server.service.EscalationService;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.services.WardenService;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.authz.enums.TenantType;
import com.phonepe.olympus.im.models.user.HumanUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import io.dropwizard.testing.junit5.DropwizardExtensionsSupport;

import java.util.*;
import javax.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@Slf4j
@ExtendWith(DropwizardExtensionsSupport.class)
public class PenaltyClassResourceIntegrationTest extends LoadOnlyOnClassLevelBaseTest {

    private PenaltyClassResource penaltyClassResource;
    private PenaltyClassRepository penaltyClassRepository;
    private PenaltyClassDetailRepository penaltyClassDetailRepository;
    private EscalationService escalationService;
    private  WardenService wardenService;
    private final ServiceUserPrincipal serviceUserPrincipal = com.phonepe.merchant.platform.stratos.server.
            integrationTests.utils.TestDataUtils.getOlympusUser();

    @BeforeEach
    public void setUpInjectionAndInitialization() {
        penaltyClassResource = guiceInjector.getInstance(PenaltyClassResource.class);
        penaltyClassDetailRepository = guiceInjector.getInstance(PenaltyClassDetailRepository.class);
        penaltyClassRepository = guiceInjector.getInstance(PenaltyClassRepository.class);
        escalationService = guiceInjector.getInstance(EscalationService.class);
    }

    @Test
    public void testGetAllTenantsSuccess(){
        truncateDb();
        String tenantName1 = "Test Tenant 1";
        String tenantName2 = "Test Tenant 2";
        String tenantName3 = "Test Tenant 3";
        String penaltyClassId1 = "PC_1";
        String penaltyClassId2 = "PC_2";
        String assertionMessage = "Get all tenants api response did not match expected result";

        var getAllTenantsResponse = penaltyClassResource.getAllTenants(serviceUserPrincipal);
        assertEquals(getAllTenantsResponse.getEntity(), Set.of(), assertionMessage);

        penaltyClassRepository.save(com.phonepe.central.stratos.penalty.server.utils.TestDataUtils.getPenaltyClassEntity(penaltyClassId1, tenantName1));
        getAllTenantsResponse = penaltyClassResource.getAllTenants(serviceUserPrincipal);
        assertEquals(getAllTenantsResponse.getEntity(), Set.of(tenantName1), assertionMessage);

        penaltyClassRepository.save(com.phonepe.central.stratos.penalty.server.utils.TestDataUtils.getPenaltyClassEntity(penaltyClassId1, tenantName2));
        getAllTenantsResponse = penaltyClassResource.getAllTenants(serviceUserPrincipal);
        assertEquals(getAllTenantsResponse.getEntity(), Set.of(tenantName1, tenantName2), assertionMessage);

        penaltyClassRepository.save(com.phonepe.central.stratos.penalty.server.utils.TestDataUtils.getPenaltyClassEntity(penaltyClassId2, tenantName3));
        getAllTenantsResponse = penaltyClassResource.getAllTenants(serviceUserPrincipal);
        assertEquals(getAllTenantsResponse.getEntity(), Set.of(tenantName1, tenantName2, tenantName3), assertionMessage);
    }


    @Test
    public void testGetPenaltiesByTenant(){
        truncateDb();
        String tenantName1 = "Test Tenant 1";
        String penaltyClassId1 = "PC_1";
        String penaltyClassId2 = "PC_2";
        String assertionMessage = "Get penalties by tenant api response did not match expected result";

        var getPenaltiesByTenantResponse = penaltyClassResource.getPenaltiesByTenant(serviceUserPrincipal, tenantName1)
                .getEntity();
        assertEquals(getPenaltiesByTenantResponse, List.of(), assertionMessage);

        penaltyClassRepository.save(com.phonepe.central.stratos.penalty.server.utils.TestDataUtils.getPenaltyClassEntity(penaltyClassId1, tenantName1));
        getPenaltiesByTenantResponse = penaltyClassResource.getPenaltiesByTenant(serviceUserPrincipal, tenantName1)
                .getEntity();
        assertEquals(getPenaltiesByTenantResponse, List.of(penaltyClassId1), assertionMessage);

        penaltyClassRepository.save(com.phonepe.central.stratos.penalty.server.utils.TestDataUtils.getPenaltyClassEntity(penaltyClassId2, tenantName1));
        getPenaltiesByTenantResponse = penaltyClassResource.getPenaltiesByTenant(serviceUserPrincipal, tenantName1)
                .getEntity();
        assertEquals(getPenaltiesByTenantResponse, List.of(penaltyClassId1,penaltyClassId2),
                assertionMessage);
    }

    @Test
    public void testPenaltyClassCreationWhenTenantISNotAuthorized() {
        ServiceUserPrincipal serviceUserPrincipal = TestDataUtils.getOlympusUser();
        PenaltyClassCreateRequest createPenaltyClassRequest = PenaltyTestObjectUtil.getPenaltyClassCreateRequest();
        Response response = penaltyClassResource.createPenaltyClassConfig(serviceUserPrincipal, "authToken",
                createPenaltyClassRequest);
        assertEquals(Response.Status.FORBIDDEN.getStatusCode(), response.getStatus());
    }

    public ServiceUserPrincipal getOlympusUser() {
        final var userAuthDetails = getOlympusAuthDetails();
        Map<TenantType, Map<String, String>> permissionMap =new HashMap<>();
        userAuthDetails.setEncodedTenantPermissions(permissionMap);
        return ServiceUserPrincipal.builder()
                .userAuthDetails(userAuthDetails)
                .build();
    }

    public UserAuthDetails getOlympusAuthDetails() {
        Map<String, Map<TenantType, Map<String, String>>> encodedPermission =new HashMap<>();
        Map<String, String> permission = new HashMap<>();
        permission.put("CIG_STRATOS_CIG:STRATOS_DISPUTE_WORKFLOW:DISPUTE_WORKFLOW_CREATE", "true");
        return UserAuthDetails.builder()
                .encodedTenantPermissions(new LinkedHashMap<>())
                .userDetails(HumanUserDetails.builder()
                        .userId("UID")
                        .build()
                ).encodedTenantPermissionsForComponentInstances(encodedPermission)
                .build();
    }


}
