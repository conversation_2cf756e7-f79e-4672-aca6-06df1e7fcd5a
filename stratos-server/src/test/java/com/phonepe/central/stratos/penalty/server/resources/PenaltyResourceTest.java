package com.phonepe.central.stratos.penalty.server.resources;


import com.phonepe.central.stratos.penalty.growth.GrowthUnit;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltyBasicSearchRequest;
import com.phonepe.central.stratos.penalty.request.penalty.PenaltyDateRangeSearchRequest;
import com.phonepe.central.stratos.penalty.server.service.PenaltyAuthorizationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyService;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.List;
import javax.ws.rs.core.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PenaltyResourceTest extends ErrorConfiguratorBaseTest {

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;

    private PenaltyResource penaltyResource;
    @Mock
    private PenaltyService penaltyService;
    @Mock
    private PenaltyAuthorizationService penaltyAuthorizationService;

    @Mock
    private ServiceUserPrincipal serviceUserPrincipal;

    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyResource = new PenaltyResource(penaltyService, penaltyAuthorizationService);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testGetPenaltiesNotAuthorizedTenant(){
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToViewPenaltyInstance(Mockito.any(), Mockito.any()))
                .thenReturn(false);
        Response response = penaltyResource.getPenalties(serviceUserPrincipal, PenaltyBasicSearchRequest.builder()
                .penaltyClassId("testPenaltyClassId")
                .penaltyId("testPenaltyId")
                .build());
        Assertions.assertEquals(Response.Status.FORBIDDEN.getStatusCode(), response.getStatus());

    }

    @Test
    public void testGetPenaltiesAuthorizedTenant(){
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToViewPenaltyInstance(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        Mockito.when(penaltyService.fetchTenantPenalties(Mockito.any())).thenReturn(List.of());
        Response response = penaltyResource.getPenalties(serviceUserPrincipal, PenaltyBasicSearchRequest.builder()
                .penaltyClassId("testPenaltyClassId")
                .penaltyId("testPenaltyId")
                .build());
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());

    }

    @Test
    public void testGetPenaltiesAuthorizedTenantWithException(){
        Mockito.when(penaltyAuthorizationService.isTenantAuthorizedToViewPenaltyInstance(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        Mockito.when(penaltyService.fetchTenantPenalties(Mockito.any())).thenThrow(new RuntimeException("Test Exception"));
        Assertions.assertThrows(Exception.class, () -> {
            penaltyResource.getPenalties(serviceUserPrincipal, PenaltyBasicSearchRequest.builder()
                    .penaltyClassId("testPenaltyClassId")
                    .penaltyId("testPenaltyId")
                    .build());
        });
    }


    @Test
    public void testReconcileByDateRangeISMoreThanOneDay(){
        Response response = penaltyResource.reconcileByDateRange(serviceUserPrincipal,
                PenaltyDateRangeSearchRequest.builder()
                        .dateRangeRequest(DateRangeRequest.builder()
                                .toDate(PenaltyUtil.getEndDate(GrowthUnit.DAY))
                                .fromDate(PenaltyUtil.getEndDate(GrowthUnit.WEEK))
                                .build())
                        .build());
        Assertions.assertEquals(Response.Status.BAD_REQUEST.getStatusCode(), response.getStatus());
    }

    @Test
    public void testReconcileByDateRange(){
        Response response = penaltyResource.reconcileByDateRange(serviceUserPrincipal,
                PenaltyDateRangeSearchRequest.builder()
                        .dateRangeRequest(DateRangeRequest.builder()
                                .toDate(PenaltyUtil.getEndDate(GrowthUnit.DAY))
                                .fromDate(PenaltyUtil.getEndDate(GrowthUnit.DAY))
                                .build())
                        .build());
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        Mockito.verify(penaltyService, Mockito.times(1)).reconcile(Mockito.any());
    }

    @Test
    public void testReconcileByDateRangeWithException(){
        Assertions.assertThrows(Exception.class, () -> {
            Response response = penaltyResource.reconcileByDateRange(serviceUserPrincipal,
                    null);
        });
        Mockito.verify(penaltyService, Mockito.times(0)).reconcile(Mockito.any());
    }

    @Test
    public void testReconcilePyPenaltyId(){
        Response result = penaltyResource.reconcileByPenaltyId(serviceUserPrincipal, "penaltyClassId", "penaltyId");
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), result.getStatus());

    }

    @Test
    public void testReconcilePyPenaltyIdWithException(){
        Mockito.doThrow(new RuntimeException()).when(penaltyService).reconcile(Mockito.anyString(), Mockito.anyString());
        Assertions.assertThrows(Exception.class, () -> {
            Response result = penaltyResource.reconcileByPenaltyId(serviceUserPrincipal, "penaltyClassId", "penaltyId");
        });

    }

    @Test
    public void testGetDisbursement(){
        Response result = penaltyResource.getDisbursementDetails(serviceUserPrincipal, "penaltyClassId", "penaltyId");
        Assertions.assertEquals(Response.Status.OK.getStatusCode(), result.getStatus());
    }

    @Test
    public void testGetDisbursementWithException(){
        Mockito.when(penaltyService.getDisbursement(Mockito.anyString(), Mockito.anyString()))
                .thenThrow(new RuntimeException("Test Exception"));
        Assertions.assertThrows(Exception.class, () -> {
            Response result = penaltyResource.getDisbursementDetails(serviceUserPrincipal, "penaltyClassId", "penaltyId");
        });
    }

}
