package com.phonepe.central.stratos.penalty.server;

import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.PenaltyStatus;
import com.phonepe.central.stratos.penalty.beneficiary.Beneficiary;
import com.phonepe.central.stratos.penalty.beneficiary.BeneficiaryType;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursementState;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevel;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevelConfig;
import com.phonepe.central.stratos.penalty.escalation.EscalationMatrix;
import com.phonepe.central.stratos.penalty.escalation.EscalationType;
import com.phonepe.central.stratos.penalty.growth.GrowthUnit;
import com.phonepe.central.stratos.penalty.growth.PenaltyGrowthRate;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.PenaltyConversionDateFixedAmountCalculation;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassState;
import com.phonepe.central.stratos.penalty.meta.PenaltyCriteria;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.PenaltyProbableRequest;
import com.phonepe.central.stratos.penalty.request.TransactionContext;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassBasicUpdateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCreateRequest;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCriteriaUpdateRequest;
import com.phonepe.central.stratos.penalty.response.EscalationResponse;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyDisbursementEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity.Fields;
import com.phonepe.growth.mustang.composition.impl.Conjunction;
import com.phonepe.growth.mustang.criteria.impl.DNFCriteria;
import com.phonepe.growth.mustang.detail.impl.RangeDetail;
import com.phonepe.growth.mustang.predicate.impl.IncludedPredicate;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */

@UtilityClass
public class PenaltyTestObjectUtil {

    public PenaltyClassEntity getPenaltyClassEntity(){
        return PenaltyClassEntity.builder()
                .penaltyClassId("classId")
                .name("name")
                .version(1)
                .description("description")
                .tenant_name(getTenantInfo().getName())
                .tenant_subcategory_name(getTenantInfo().getSubCategory())
                .state(PenaltyClassState.CREATED)
                .build();
    }

    public EscalationResponse getEscalationResponse(){
        return EscalationResponse.builder()
                .escalationType(EscalationType.PENALTY)
                .id(12L)
                .escalationLevel(EscalationLevel.L1)
                .tenantInfo(getTenantInfo())
                .emailDl(Set.of("<EMAIL>"))
                .escalationMappingId("mappingId")
                .build();
    }

    public  TenantInfo getTenantInfo() {
        return TenantInfo.builder()
                .name("name")
                .subCategory("subCategory")
                .build();
    }

    public PenaltyClassCreateRequest getPenaltyClassCreateRequest() {

        return PenaltyClassCreateRequest.builder()
                .name("name")
                .description("description")
                .tenant(getTenantInfo())
                .escalationMatrix(getEscalationMatrix())
                .details(List.of(getPenaltyClassDetail()))
                .build();
    }

    public PenaltyClassBasicUpdateRequest getPenaltyClassBasicUpdate() {

        return PenaltyClassBasicUpdateRequest.builder()
                .name("name")
                .description("description_updated")
                .escalationMatrix(getEscalationMatrix())
                .penaltyClassId("classId")
                .state(PenaltyClassState.CREATED)
                .build();
    }

    public PenaltyClassCriteriaUpdateRequest getPenaltyCriteriaUpdate() {

        return PenaltyClassCriteriaUpdateRequest.builder()
                .penaltyClassId("classId")
                .details(List.of(getPenaltyClassDetail()))
                .build();
    }

    public PenaltyClassDetail getPenaltyClassDetail() {
        return PenaltyClassDetail.builder()
                .penaltyCap(1000L)
                .label("label")
                .growthRate(PenaltyGrowthRate.builder()
                        .calculation(PenaltyConversionDateFixedAmountCalculation.builder()
                                .amountPath("amountPath")
                                .isDefault(true)
                                .amount(BigDecimal.valueOf(10L))
                                .build())
                        .unit(GrowthUnit.DAY)
                        .build())
                .criteria(PenaltyCriteria.builder()
                        .leeway(Duration.ofMillis(1L))
                        .trigger(DNFCriteria.builder()
                                .id("trigger")
                                .conjunction(Conjunction.builder()
                                        .predicate(IncludedPredicate.builder()
                                                .lhs(Fields.transactionAmount)
                                                .detail(RangeDetail.builder()
                                                        .lowerBound(100L)
                                                        .upperBound(1000L)
                                                        .build())
                                                .build())
                                        .build())
                                .build())
                        .disQualifier(DNFCriteria.builder()
                                .id("disqualifiedTrigger")
                                .conjunction(Conjunction.builder()
                                        .predicate(IncludedPredicate.builder()
                                                .lhs(Fields.transactionAmount)
                                                .detail(RangeDetail.builder()
                                                        .lowerBound(1L)
                                                        .upperBound(90L)
                                                        .build())
                                                .build())
                                        .build())
                                .build())
                        .build())

                .build();
    }

    public EscalationMatrix getEscalationMatrix(){
        return EscalationMatrix.builder()
                .escalationType(EscalationType.PENALTY)
                .levelConfig(Set.of(EscalationLevelConfig.builder()
                        .emailIds(getEscalationResponse().getEmailDl())
                        .level(EscalationLevel.L1)
                        .build()))
                .build();
    }

    public  Penalty getPenaltyInstance() {
        return Penalty.builder()
                .penaltyId("penaltyId")
                .penaltyClassId("classId")
                .penaltyProbableId("probableId")
                .finalPenaltyAmount(100L)
                .initialPenaltyAmount(50L)
                .beneficiary(Beneficiary.builder()
                        .id("beneficiaryId")
                        .type(BeneficiaryType.USER)
                        .build())
                .qualifiedAt(new Date())
                .createdAt(new Date())
                .updatedAt(new Date())
                .triggeredAt(new Date())
                .closedAt(new Date())
                .transactionId("transactionId")
                .transactionAmount(500L)
                .status(PenaltyStatus.ACTIVE)
                .build();
    }

    public PenaltyEntity getPenaltyEntity(){
        return PenaltyEntity.builder()
                .penaltyId("penaltyId")
                .penaltyClassId("classId")
                .penaltyProbableId("probableId")
                .finalPenaltyAmount(100L)
                .initialPenaltyAmount(50L)
                .beneficiaryId("beneficiaryId")
                .beneficiaryType(BeneficiaryType.USER)
                .qualifiedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .triggeredAt(LocalDateTime.now())
                .closedAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .transactionId("transactionId")
                .transactionAmount(500L)
                .status(PenaltyStatus.ACTIVE)
                .build();
    }

    public PenaltyDisbursementEntity getPenaltyDisbursementEntity() {
        return PenaltyDisbursementEntity.builder()
                .penaltyId("penaltyId")
                .penaltyClassId("classId")
                .transactionId("transactionId")
                .status(PenaltyDisbursementState.COMPLETED)
                .disbursementAmount(120L)
                .disbursementId("disbursementId")
                .disbursementTransactionId("disbursementTransactionId")
                .beneficiaryId("beneficiaryId")
                .id(12L)
                .createdAt(LocalDateTime.now())
                .build();
    }

    public Set<EscalationLevelConfig> getEscalationLevelConfig() {
        return Set.of(EscalationLevelConfig.builder().level(EscalationLevel.L1).emailIds(Set.of("<EMAIL>","<EMAIL>")).build(),
                EscalationLevelConfig.builder().level(EscalationLevel.L2).emailIds(Set.of("<EMAIL>","<EMAIL>")).build(),
                EscalationLevelConfig.builder().level(EscalationLevel.L3).emailIds(Set.of("<EMAIL>","<EMAIL>")).build(),
                EscalationLevelConfig.builder().level(EscalationLevel.L4).emailIds(Set.of("<EMAIL>","<EMAIL>")).build());
    }

    public  PenaltyProbable getPenaltyProbable() {
        return PenaltyProbable.builder()
                .probableId("probableId")
                .beneficiary(Beneficiary.builder()
                        .id("beneficiaryId")
                        .type(BeneficiaryType.USER)
                        .build())
                .penaltyClassId("classId")
                .initialPenaltyAmount(100L)
                .transactionId("transactionId")
                .transactionAmount(500L)
                .dueDate(new Date())
                .triggeredAt(new Date())
                .build();
    }

    public  PenaltyProbableRequest getPenaltyProbableRequest() {
        return PenaltyProbableRequest.builder()
                .penaltyClassId("classId")
                .beneficiary(Beneficiary.builder()
                        .id("beneficiaryId")
                        .type(BeneficiaryType.USER)
                        .build())
                .initialPenaltyAmount(100L)
                .transactionContext(TransactionContext.builder()
                        .transactionId("transactionId")
                        .transactionAmount(500L)
                        .build())
                .dueDate(new Date())
                .triggeredAt(new Date())
                .build();
    }

    public PenaltyClass getPenaltyClass(){
        return PenaltyClass.builder()
            .id("classId")
            .name("name")
            .version(1)
            .description("description")
            .tenant(getTenantInfo())
            .escalationMatrix(
                EscalationMatrix.builder()
                    .escalationType(EscalationType.PENALTY)
                    .levelConfig(Set.of(
                        EscalationLevelConfig.builder()
                            .emailIds(Set.of("phonepe.com"))
                            .level(EscalationLevel.L1)
                            .build()
                    ))
                    .build()
            )
            .state(PenaltyClassState.ACTIVE).build();
    }
}
