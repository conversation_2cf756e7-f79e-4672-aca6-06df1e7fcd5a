package com.phonepe.central.stratos.penalty.server.integrationTests.resources;

import static com.phonepe.central.stratos.penalty.server.aerospike.AeroSpikeRepository.CONTENT;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;

import com.aerospike.client.query.IndexType;
import com.phonepe.central.stratos.escalation.request.EscalateEntityRequest;
import com.phonepe.central.stratos.notification.EmailCommunicationRequest;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.PenaltyStatus;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevel;
import com.phonepe.central.stratos.penalty.escalation.EscalationType;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.server.aerospike.AeroSpikeRepository;
import com.phonepe.central.stratos.penalty.server.aerospike.PenaltyAerospikeSet;
import com.phonepe.central.stratos.penalty.server.convertor.PenaltyConvertors;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.EscalationRepository;
import com.phonepe.central.stratos.penalty.server.mariadb.repository.PenaltyRepository;
import com.phonepe.central.stratos.penalty.server.queue.actors.EscalatedNotificationActor;
import com.phonepe.central.stratos.penalty.server.queue.messages.EscalatedEntityNotificationQueueMessage;
import com.phonepe.central.stratos.penalty.server.resources.EscalationResource;
import com.phonepe.central.stratos.penalty.server.service.EscalationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyProbableService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyService;
import com.phonepe.central.stratos.penalty.server.util.PenaltyUtil;
import com.phonepe.central.stratos.penalty.server.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatcher;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;


@ExtendWith(MockitoExtension.class)
public class EscalationResourceIntergrationTest extends LoadOnlyOnClassLevelBaseTest {

    private EscalationRepository escalationRepository;

    private EscalationResource escalationResource;

    private PenaltyRepository penaltyRepository;

    private static final AeroSpikeRepository aeroSpikeRepository = guiceInjector.getInstance(AeroSpikeRepository.class);

    private PenaltyProbableService penaltyProbableService;

    private PenaltyService penaltyService;

    private EscalationService escalationService;

    private EscalatedNotificationActor escalatedNotificationActor;

    private static final String penaltyClassId1 = "PENALTY_CLASS_ID_1";

    private static final PenaltyAerospikeSet aerospikeSet = PenaltyAerospikeSet.PENALTY_PROBABLE;

    private final ServiceUserPrincipal serviceUserPrincipal = com.phonepe.merchant.platform.stratos.server.
            integrationTests.utils.TestDataUtils.getOlympusUser();

    private final List<String> bins = List.of(
            PenaltyProbable.Fields.penaltyClassId,
            PenaltyProbable.Fields.transactionId,
            PenaltyProbable.Fields.dueDate,
            PenaltyProbable.Fields.triggeredAt,
            CONTENT);

    @BeforeAll
    public static void setUpAerospike() {
        aeroSpikeRepository.createSecondaryIndex(PenaltyProbable.Fields.penaltyClassId, IndexType.STRING);
        aeroSpikeRepository.createSecondaryIndex(PenaltyProbable.Fields.dueDate, IndexType.NUMERIC);
    }

    @BeforeEach
    public void init(){
        truncateDb();
        escalationRepository = guiceInjector.getInstance(EscalationRepository.class);
        penaltyRepository = guiceInjector.getInstance(PenaltyRepository.class);
        penaltyService = guiceInjector.getInstance(PenaltyService.class);
        penaltyProbableService = guiceInjector.getInstance(PenaltyProbableService.class);

        escalatedNotificationActor = Mockito.mock(EscalatedNotificationActor.class);
        escalationService = new EscalationService(escalationRepository, ()-> penaltyProbableService,
                ()-> penaltyService, () -> escalatedNotificationActor);
        escalationResource = new EscalationResource(escalationService);
    }

    @Test
    public void testEscalateSuccessL1() throws Exception {
        String emailId1 = "<EMAIL>";
        var expectedCommunicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(Set.of(emailId1))
                .build();
        escalationRepository.save(TestDataUtils.getPenaltyEscalation(penaltyClassId1, EscalationLevel.L1, emailId1));

        Date dueAt1 = PenaltyUtil.addDaystoDate(getToday(9), 2);
        PenaltyProbable penaltyProbable1 = TestDataUtils.getPenaltyProbable(penaltyClassId1, dueAt1);
        aeroSpikeRepository.save(aerospikeSet, penaltyProbable1.getProbableId(), penaltyProbable1);

        EscalateEntityRequest escalateEntityRequest = getEscalateEntityRequest(penaltyClassId1, EscalationLevel.L1, getDateRangeRequest());
        Mockito.doNothing().when(escalatedNotificationActor).publish(Mockito.any());

        escalationResource.escalate(serviceUserPrincipal, escalateEntityRequest);

        var expectedQueueMessage = EscalatedEntityNotificationQueueMessage.<PenaltyProbable>builder()
                .mappingId(penaltyClassId1)
                .escalationType(EscalationType.PENALTY)
                .escalationLevel(EscalationLevel.L1)
                .escalatedEntities(List.of(penaltyProbable1))
                .communicationRequest(expectedCommunicationRequest)
                .build();

        verify(escalatedNotificationActor).publish(argThat(new EscalatedEntityNotificationQueueMessageMatcher(expectedQueueMessage)));
    }

    @Test
    public void testEscalateSuccessL2() throws Exception {
        String emailId2 = "<EMAIL>";
        var expectedCommunicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(Set.of(emailId2))
                .build();
        escalationRepository.save(TestDataUtils.getPenaltyEscalation(penaltyClassId1, EscalationLevel.L2, emailId2));

        Date dueAt1 = PenaltyUtil.addDaystoDate(getToday(9), 1);
        PenaltyProbable penaltyProbable1 = TestDataUtils.getPenaltyProbable(penaltyClassId1, dueAt1);
        aeroSpikeRepository.save(aerospikeSet, penaltyProbable1.getProbableId(), penaltyProbable1);

        EscalateEntityRequest escalateEntityRequest = getEscalateEntityRequest(penaltyClassId1, EscalationLevel.L2, getDateRangeRequest());
        Mockito.doNothing().when(escalatedNotificationActor).publish(Mockito.any());

        escalationResource.escalate(serviceUserPrincipal, escalateEntityRequest);

        var expectedQueueMessage = EscalatedEntityNotificationQueueMessage.<PenaltyProbable>builder()
                .mappingId(penaltyClassId1)
                .escalationType(EscalationType.PENALTY)
                .escalationLevel(EscalationLevel.L2)
                .escalatedEntities(List.of(penaltyProbable1))
                .communicationRequest(expectedCommunicationRequest)
                .build();

        verify(escalatedNotificationActor).publish(argThat(new EscalatedEntityNotificationQueueMessageMatcher(expectedQueueMessage)));
    }

    @Test
    public void testEscalateSuccessL3() throws Exception {
        String emailId3 = "<EMAIL>";
        escalationRepository.save(TestDataUtils.getPenaltyEscalation(penaltyClassId1, EscalationLevel.L3, emailId3));
        var expectedCommunicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(Set.of(emailId3))
                .build();
        DateRangeRequest dateRangeRequest= getDateRangeRequest();
        LocalDateTime triggerTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(9, 0));
        PenaltyEntity penalty1 = TestDataUtils.getPenaltyEntity(penaltyClassId1, PenaltyStatus.ACTIVE, triggerTime);
        penaltyRepository.save(penalty1);

        EscalateEntityRequest escalateEntityRequest = getEscalateEntityRequest(penaltyClassId1, EscalationLevel.L3, dateRangeRequest);
        Mockito.doNothing().when(escalatedNotificationActor).publish(Mockito.any());
        escalationResource.escalate(serviceUserPrincipal, escalateEntityRequest);

        var expectedQueueMessage = EscalatedEntityNotificationQueueMessage.<Penalty>builder()
                .mappingId(penaltyClassId1)
                .escalationType(EscalationType.PENALTY)
                .escalationLevel(EscalationLevel.L3)
                .escalatedEntities(List.of(PenaltyConvertors.convert(penalty1)))
                .communicationRequest(expectedCommunicationRequest)
                .dateRangeRequest(dateRangeRequest)
                .build();

        verify(escalatedNotificationActor).publish(argThat(new EscalatedEntityNotificationQueueMessageMatcher(expectedQueueMessage)));

    }

    @Test
    public void testEscalateSuccessL4() throws Exception {
        String emailId4 = "<EMAIL>";
        escalationRepository.save(TestDataUtils.getPenaltyEscalation(penaltyClassId1, EscalationLevel.L4, emailId4));
        var expectedCommunicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(Set.of(emailId4))
                .build();

        DateRangeRequest dateRangeRequest= getDateRangeRequest();
        LocalDateTime triggerTime = LocalDateTime.of(LocalDate.now().minusDays(7), LocalTime.of(9, 0));
        PenaltyEntity penalty1 = TestDataUtils.getPenaltyEntity(penaltyClassId1, PenaltyStatus.ACTIVE, triggerTime);
        penaltyRepository.save(penalty1);

        EscalateEntityRequest escalateEntityRequest = getEscalateEntityRequest(penaltyClassId1, EscalationLevel.L4,
                dateRangeRequest);
        Mockito.doNothing().when(escalatedNotificationActor).publish(Mockito.any());
        escalationResource.escalate(serviceUserPrincipal, escalateEntityRequest);

        var expectedQueueMessage = EscalatedEntityNotificationQueueMessage.<Penalty>builder()
                .mappingId(penaltyClassId1)
                .escalationType(EscalationType.PENALTY)
                .escalationLevel(EscalationLevel.L4)
                .escalatedEntities(List.of(PenaltyConvertors.convert(penalty1)))
                .communicationRequest(expectedCommunicationRequest)
                .dateRangeRequest(dateRangeRequest)
                .build();

        verify(escalatedNotificationActor).publish(argThat(new EscalatedEntityNotificationQueueMessageMatcher(expectedQueueMessage)));
    }

    public static DateRangeRequest getDateRangeRequest() {
        Date toDate = getToday(10);
        Date fromDate = PenaltyUtil.addDaystoDate(toDate, -1);
        return DateRangeRequest.builder()
                .fromDate(fromDate)
                .toDate(toDate)
                .build();
    }

    private EscalateEntityRequest getEscalateEntityRequest(String mappingId, EscalationLevel escalationLevel,
                                                           DateRangeRequest dateRangeRequest) {
        return EscalateEntityRequest.builder()
                .mappingId(mappingId)
                .escalationLevel(escalationLevel)
                .dateRangeRequest(dateRangeRequest)
                .build();
    }

    private static Date getToday(int hour) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND,0);
        return calendar.getTime();
    }

    public static class EscalatedEntityNotificationQueueMessageMatcher implements ArgumentMatcher<EscalatedEntityNotificationQueueMessage> {
        private final EscalatedEntityNotificationQueueMessage expected;

        public EscalatedEntityNotificationQueueMessageMatcher(EscalatedEntityNotificationQueueMessage expected) {
            this.expected = expected;
        }

        @Override
        public boolean matches(EscalatedEntityNotificationQueueMessage actual) {
            if (actual == null) {
                return false;
            }
            return actual.getMappingId().equals(expected.getMappingId()) &&
                    actual.getEscalationType().equals(expected.getEscalationType()) &&
                    actual.getEscalationLevel().equals(expected.getEscalationLevel()) &&
                    actual.getEscalatedEntities().size() == expected.getEscalatedEntities().size() &&
                    actual.getCommunicationRequest().equals(expected.getCommunicationRequest());
        }

        @Override
        public String toString() {
            return "Matcher for EscalatedEntityNotificationQueueMessage with expected values: " + expected;
        }
    }
}
