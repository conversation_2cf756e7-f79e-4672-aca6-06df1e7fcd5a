package com.phonepe.central.stratos.penalty.server.service;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.inject.Injector;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.server.config.client.NoOpsClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.PassThroughProbableClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.config.client.ServiceClientPenaltyResolutionConfig;
import com.phonepe.central.stratos.penalty.server.generator.DynamicHttpExecutorBuilderFactoryGenerator;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.growth.mustang.composition.impl.Conjunction;
import com.phonepe.growth.mustang.criteria.impl.DNFCriteria;
import com.phonepe.growth.mustang.detail.impl.EqualityDetail;
import com.phonepe.growth.mustang.predicate.impl.IncludedPredicate;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */

@ExtendWith(MockitoExtension.class)
public class PenaltyClientServiceTest extends ErrorConfiguratorBaseTest {

    @Mock
    private PenaltyClassService penaltyClassService;
    @Mock
    private RangerHubConfiguration rangerHubConfiguration;
    private DynamicHttpExecutorBuilderFactoryGenerator httpExecutorBuilderFactoryGenerator;
    @Mock
    private OlympusIMClient olympusIMClient;

    @Mock
    private Injector injector;

    private PenaltyClientService penaltyClientService;

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;


    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        ServiceEndpointProviderFactory serviceEndpointFactory = Mockito.mock(ServiceEndpointProviderFactory.class);

        ObjectMapper mapper = new ObjectMapper();

        MetricRegistry metricRegistry = Mockito.mock(MetricRegistry.class);
        httpExecutorBuilderFactoryGenerator = new DynamicHttpExecutorBuilderFactoryGenerator(serviceEndpointFactory, mapper, metricRegistry);
        penaltyClientService = new PenaltyClientService(penaltyClassService, rangerHubConfiguration,
                httpExecutorBuilderFactoryGenerator, olympusIMClient, null,injector);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testIsPenaltyResolvedWithException() {
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenThrow(new RuntimeException("Test exception"));
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        });
    }

    @Test
    public void testIsPenaltyResolvedWithExceptionWithServiceCall() {
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenReturn(ServiceClientPenaltyResolutionConfig.builder()
                        .rangerHubClientId("clientId")
                        .apiPath("apiPath")
                        .build());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        });
    }

    @Test
    public void testIsPenaltyResolvedWithNoOps() {
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenReturn(NoOpsClientPenaltyResolutionConfig.builder()
                        .build());
        boolean isResolved = penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        Assertions.assertTrue(isResolved);
    }


    @Test
    public void testIsPenaltyResolvedWithPassThrough() {
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenReturn(PassThroughProbableClientPenaltyResolutionConfig.builder()
                        .build());
        boolean isResolved = penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        Assertions.assertTrue(isResolved);
    }

    @Test
    public void testIsPenaltyResolvedWithService() {
        Mockito.when(rangerHubConfiguration.getServices())
                .thenReturn(Set.of(HttpConfiguration.builder()
                        .clientId("rangerHubClientId")
                        .build()));
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenReturn(ServiceClientPenaltyResolutionConfig.builder()
                        .apiPath("apiPath")
                        .rangerHubClientId("rangerHubClientId")
                        .build());
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClientService.isPenaltyResolved("penaltyClassId", "transactionId");
        });
        Mockito.verify(rangerHubConfiguration, Mockito.times(1))
                .getServices();
    }

    //From Here probable

    @Test
    public void testIsProbableResolvedWithException() {
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenThrow(new RuntimeException("Test exception"));
        Assertions.assertThrows(RuntimeException.class, () -> {
            penaltyClientService.isProbableResolved(PenaltyProbable.builder()
                    .penaltyClassId("penaltyClassId")
                    .build());
        });
    }

    @Test
    public void testIsProbableResolvedWithNoOps() {
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenReturn(NoOpsClientPenaltyResolutionConfig.builder()
                        .build());
        boolean isResolved = penaltyClientService.isProbableResolved(PenaltyProbable.builder()
                .penaltyClassId("penaltyClassId")
                .build());
        Assertions.assertTrue(isResolved);
    }

    @Test
    public void testIsProbableResolvedWithPassThrough() {
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenReturn(PassThroughProbableClientPenaltyResolutionConfig.builder()
                        .build());
        boolean isResolved = penaltyClientService.isProbableResolved(PenaltyProbable.builder()
                .penaltyClassId("penaltyClassId")
                .build());
        Assertions.assertFalse(isResolved);
    }

    @Test
    public void testIsProbableResolvedWithService() {
        Mockito.when(rangerHubConfiguration.getServices())
                .thenReturn(Set.of(HttpConfiguration.builder()
                        .clientId("rangerHubClientId")
                        .build()));
        Mockito.when(penaltyClassService.getClientResolutionConfig("penaltyClassId"))
                .thenReturn(ServiceClientPenaltyResolutionConfig.builder()
                        .apiPath("apiPath")
                        .rangerHubClientId("rangerHubClientId")
                        .build());
        Assertions.assertThrows(DisputeException.class, () -> {
            penaltyClientService.isProbableResolved(PenaltyProbable.builder()
                    .penaltyClassId("penaltyClassId")
                    .build());
        });

        Mockito.verify(rangerHubConfiguration, Mockito.times(1))
                .getServices();
    }

    @Test
    public void testResponseMustang() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        MapperUtils.init(objectMapper);



        DNFCriteria triggerDnfCriteria = DNFCriteria.builder()
                .id("trigger")
                .conjunction(Conjunction.builder()
                        .predicate(IncludedPredicate.builder()
                                .detail(EqualityDetail.builder()
                                        .values(Set.of("FAILED"))
                                        .build())
                                .lhs("$.data.sentPayment.paymentState")
                                .build())
                        .build())
                .build();
        JsonNode requestJson = MapperUtils.convertToJsonNode(triggerDnfCriteria);
        System.out.println(requestJson);

    }

    @Test
    public void test() throws ClassNotFoundException {
        Class<?> classValue = Class.forName("com.phonepe.central.stratos.penalty.server.client.NexusClient");
        System.out.println(classValue);
    }


}
