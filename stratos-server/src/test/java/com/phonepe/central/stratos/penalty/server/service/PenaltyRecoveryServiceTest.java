package com.phonepe.central.stratos.penalty.server.service;

import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.recovery.impl.MerchantPenalizedEntity;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.clients.PlutusEventIngestionClient;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
public class PenaltyRecoveryServiceTest  extends ErrorConfiguratorBaseTest {

    @Mock
    private ResourceErrorService<StratosErrorCodeKey> resourceErrorService;

    private PenaltyRecoveryService penaltyRecoveryService;
    @Mock
    private PenaltyClassService penaltyClassService;
    @Mock
    private PlutusEventIngestionClient plutusEventIngestionClient;
    @Mock
    private EventIngester eventIngester;


    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.openMocks(this);
        penaltyRecoveryService = new PenaltyRecoveryService(penaltyClassService, plutusEventIngestionClient,
                eventIngester);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testInitiateRecoveryWithException() {
        Assertions.assertThrows(Exception.class, () -> {
            penaltyRecoveryService.initiateRecovery("penaltyDisbursementId", null, 100L);
        });
    }

    @Test
    public void testInitiateRecoveryWithValidDetail(){
        Penalty penalty = PenaltyTestObjectUtil.getPenaltyInstance();
        Mockito.when(penaltyClassService.getTenantInfo("classId"))
                .thenReturn(TenantInfo.builder()
                        .name("testTenant")
                        .subCategory("testSubCategory")
                        .build());
        Mockito.when(penaltyClassService.getPenalizedEntity(penalty.getPenaltyClassId())).thenReturn(
                MerchantPenalizedEntity.builder()
                        .merchantId("merchantId")
                        .mcc("mcc")
                        .build());
        Mockito.when(plutusEventIngestionClient.sendAccountingEvent(Mockito.any())).thenReturn(true);
        penaltyRecoveryService.initiateRecovery("penaltyDisbursementId", penalty, 100L);
        Mockito.verify(plutusEventIngestionClient, Mockito.times(1)).sendAccountingEvent(Mockito.any());
    }


}
