<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.phonepe.merchant.platform</groupId>
		<artifactId>stratos</artifactId>
        <version>2.0.69-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>stratos-models</artifactId>

	<properties>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<maven.compiler.release>17</maven.compiler.release>
		<maven.release.ignore.snapshots>false</maven.release.ignore.snapshots>
		<crixus-core.version>1.0.42</crixus-core.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>2.0.1.Final</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>6.1.7.Final</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.12.1</version>
		</dependency>

		<dependency>
			<groupId>com.phonepe.payments</groupId>
			<artifactId>upi-client-model</artifactId>
			<version>${upi-client.model.version}</version>
		</dependency>

		<dependency>
			<groupId>com.phonepe.payments</groupId>
			<artifactId>netpe-model</artifactId>
			<version>${netpe.model.version}</version>
		<exclusions>
        <exclusion>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-jcs-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

		<dependency>
			<groupId>com.phonepe.services</groupId>
			<artifactId>refund-orchestrator-models</artifactId>
			<version>${ro.model.version}</version>
		<exclusions>
        <exclusion>
          <groupId>com.phonepe.platform</groupId>
          <artifactId>zeus-models</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

		<dependency>
			<groupId>com.phonepe</groupId>
			<artifactId>error-configurator</artifactId>
			<version>1.3</version>
		</dependency>

		<dependency>
			<groupId>com.tngtech.archunit</groupId>
			<artifactId>archunit</artifactId>
			<version>1.3.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.tngtech.archunit</groupId>
			<artifactId>archunit-junit5</artifactId>
			<version>1.3.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.phonepe.platform.http.v2</groupId>
			<artifactId>http-client-all</artifactId>
		</dependency>
		<dependency>
			<groupId>com.phonepe.services</groupId>
			<artifactId>crixus-core</artifactId>
			<version>${crixus-core.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.phonepe.platform</groupId>
					<artifactId>rosey-dropwizard-config</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.phonepe.platform</groupId>
					<artifactId>requestinfo-bundle</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.phonepe.platform</groupId>
					<artifactId>requestinfo-models</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.phonepe.platform</groupId>
					<artifactId>zeus-models</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.appform.ranger</groupId>
					<artifactId>ranger-discovery-bundle</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.phonepe.services</groupId>
			<artifactId>crixus-core-models</artifactId>
			<version>${crixus-core.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.phonepe.platform</groupId>
					<artifactId>requestinfo-models</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
        </dependency>
    </dependencies>
</project>