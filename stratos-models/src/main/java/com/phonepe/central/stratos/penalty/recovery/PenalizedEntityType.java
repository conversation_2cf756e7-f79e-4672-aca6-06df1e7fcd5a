package com.phonepe.central.stratos.penalty.recovery;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */


@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PenalizedEntityType {
    MERCHANT_PENALIZED_ENTITY(PenalizedEntityType.MERCHANT_PENALIZED_ENTITY_TEXT);
    public static final String MERCHANT_PENALIZED_ENTITY_TEXT = "MERCHANT_PENALIZED_ENTITY";

    @Getter
    private final String value;

}
