package com.phonepe.central.stratos.penalty.meta;

import com.phonepe.central.stratos.penalty.escalation.EscalationMatrix;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyClass {

    @NotBlank
    private String id;
    @NotBlank
    private String name;
    @NotBlank
    private String description;
    @Min(1)
    private int version;
    @NotNull
    private PenaltyClassState state;
    @NotEmpty
    private List<PenaltyClassDetail> details;
    @Valid
    @NotNull
    private EscalationMatrix escalationMatrix;
    @Valid
    @NotNull
    private TenantInfo tenant;

    private Date createdAt;
    private Date updatedAt;

    public boolean isActive() {
        return state.equals(PenaltyClassState.ACTIVE);
    }
}
