package com.phonepe.central.stratos.penalty.probables;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.phonepe.central.stratos.penalty.beneficiary.Beneficiary;
import java.io.Serializable;
import java.util.Date;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class PenaltyProbable implements Serializable {

    @NotBlank
    private String probableId;

    @NotBlank
    private String penaltyClassId;

    @Valid
    @NotNull
    private Beneficiary beneficiary;
    @NotBlank
    private String transactionId;
    @Min(1)
    private long transactionAmount;
    @Min(1)
    private long initialPenaltyAmount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date triggeredAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date dueDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date created;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date updated;
}
