package com.phonepe.central.stratos.penalty.request.penaltyclass;

import com.phonepe.central.stratos.penalty.escalation.EscalationMatrix;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyClassCreateRequest {


    @NotBlank
    private String name;
    @NotBlank
    private String description;

    @Valid
    @NotNull
    private TenantInfo tenant;

    @NotEmpty
    @Valid
    private List<PenaltyClassDetail> details;

    @Valid
    @NotNull
    private EscalationMatrix escalationMatrix;

}
