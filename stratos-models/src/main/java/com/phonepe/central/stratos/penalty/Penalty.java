package com.phonepe.central.stratos.penalty;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.phonepe.central.stratos.penalty.beneficiary.Beneficiary;
import java.util.Date;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Penalty {
    @NotBlank
    private String penaltyId;
    @NotBlank
    private String penaltyClassId;

    // Will only be present for such penalties that have a leeway.
    @Nullable
    private String penaltyProbableId;
    @Valid
    @NotNull
    private Beneficiary beneficiary;
    @NotBlank
    private String transactionId;
    @NotNull
    private PenaltyStatus status;
    @Min(1)
    private long transactionAmount;
    @Min(1)
    private long initialPenaltyAmount;
    @Min(1)
    private long finalPenaltyAmount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date triggeredAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date qualifiedAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date closedAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date updatedAt;
}
