package com.phonepe.central.stratos.penalty.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.phonepe.central.stratos.penalty.beneficiary.Beneficiary;
import io.dropwizard.validation.ValidationMethod;
import java.util.Date;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyProbableRequest {

    @NotEmpty
    private String penaltyClassId;

    @Valid
    @NotNull
    private Beneficiary beneficiary;

    @Min(1)
    private Long initialPenaltyAmount;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date triggeredAt;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date dueDate;

    @NotNull
    @Valid
    private TransactionContext transactionContext;

    @JsonIgnore
    @ValidationMethod(message = "due date should be greater than current time  and also should be greater than triggered date")
    public boolean isValid() {
        return dueDate.after(triggeredAt);
    }
}
