package com.phonepe.central.stratos.penalty.meta;

import com.phonepe.central.stratos.penalty.growth.PenaltyGrowthRate;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyClassDetail {

    @NotBlank
    private String label;
    @Min(1) @Max(1_000_00) // 1K in paise
    private long penaltyCap;
    @Valid
    @NotNull
    private PenaltyCriteria criteria;
    @Valid
    @NotNull
    private PenaltyGrowthRate growthRate;
}
