package com.phonepe.central.stratos.penalty.escalation;

/**
 * <AUTHOR>
 */
public enum EscalationType {
    PENALTY{
        @Override
        public <T> T accept(EscalationTypeVisitor<T> visitor) {
            return visitor.visitPenalty();
        }
    };

    public abstract <T> T accept(EscalationTypeVisitor<T> visitor);

    public interface EscalationTypeVisitor<T> {
        T visitPenalty();
    }
}
