package com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileTypeVisitor;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeReconcileRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class IndefiniteHoldReconcileRequest extends DisputeReconcileRequest {
    public IndefiniteHoldReconcileRequest() {
        super(ReconcileType.INDEFINITE_HOLD_RECONCILE);
    }
    @Override
    public <T> T accept(ReconcileTypeVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
