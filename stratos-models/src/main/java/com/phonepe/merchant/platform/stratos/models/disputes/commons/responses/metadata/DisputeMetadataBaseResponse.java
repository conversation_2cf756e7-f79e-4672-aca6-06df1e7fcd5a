package com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata;

import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeMetadataDto.Names.FRA_ACTION_METADATA;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeMetadataDto;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes(value = {
    @Type(value = DisputeFraActionMetadataResponse.class, name = FRA_ACTION_METADATA)
})
public abstract class DisputeMetadataBaseResponse {

    @NotNull
    private DisputeMetadataDto type;

    @NotEmpty
    private String transactionId;

    @NotEmpty
    private String disputeWorkflowId;

}
