package com.phonepe.merchant.platform.stratos.models.disputes.commons.requests;

import static com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType.Names.FRAUD_REPRESENTMENT_RECONCILE;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType.Names.REFUND_INITIATION_RECONCILE;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType.Names.REFUND_STATUS_RECONCILE;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType.Names.SUSPECTED_FRAUD_RECONCILE;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.FraudRepresentmentDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.IndefiniteHoldReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundInitiationReconRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundStatusReconRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileTypeVisitor;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType.Names.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes(value = {
    @Type(value = SuspectedFraudChargebackDisputeReconcileRequest.class, name = SUSPECTED_FRAUD_RECONCILE),
    @Type(value = RefundStatusReconRequest.class, name = REFUND_STATUS_RECONCILE),
    @Type(value = FraudRepresentmentDisputeReconcileRequest.class, name = FRAUD_REPRESENTMENT_RECONCILE),
    @Type(value = IndefiniteHoldReconcileRequest.class, name = INDEFINITE_HOLD_RECONCILE),
    @Type(value = RefundInitiationReconRequest.class, name = REFUND_INITIATION_RECONCILE)
})
public abstract class DisputeReconcileRequest {

    @NotNull
    private ReconcileType type;

    public abstract <T> T accept(ReconcileTypeVisitor<T> visitor);
}
