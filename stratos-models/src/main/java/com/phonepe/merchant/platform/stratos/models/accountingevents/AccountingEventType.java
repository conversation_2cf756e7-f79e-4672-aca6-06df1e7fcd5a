package com.phonepe.merchant.platform.stratos.models.accountingevents;

public enum AccountingEventType {
    UPI_CHARGEBACK_RECOVERY,
    UPI_CHARGEBACK_RECOVERY_REVERSAL,
    UPI_CHARGE<PERSON>CK_PENALTY_RECOVERY,
    PG_CHARGEBACK_RECOVERY,
    PG_<PERSON>AR<PERSON>BACK_RECOVERY_REVERSAL,
    PG_CHARGEBACK_PENALTY_RECOVERY,
    NB_CHARGEBACK_RECOVERY,
    NB_CHARGEBACK_RECOVERY_REVERSAL,
    MERCHANT_PENALTY_RECOVERY,
    PENALTY_RECOVERY,
    AMOUNT_ON_HOLD,
    AMOUNT_ON_HOLD_REVERSAL;
}
