package com.phonepe.merchant.platform.stratos.models.disputes.commons;

import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.FraudRepresentmentDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.IndefiniteHoldReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundInitiationReconRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundStatusReconRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;

public interface ReconcileTypeVisitor<T> {

    T visit(SuspectedFraudChargebackDisputeReconcileRequest request);
    T visit(RefundStatusReconRequest request);
    T visit(FraudRepresentmentDisputeReconcileRequest request);
    T visit(IndefiniteHoldReconcileRequest request);
    T visit(RefundInitiationReconRequest request);
}
