package com.phonepe.merchant.platform.stratos.models.disputes.toa.responses;

import java.time.LocalDateTime;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class ToaSummary {

    @NotEmpty
    private String disputeId;

    @NotEmpty
    private String disputeWorkflowId;

    @NotEmpty
    private String disputeWorkflowVersion;

    @NotEmpty
    private String disputeType;

    @NotEmpty
    private String disputeStage;

    @NotEmpty
    private String transactionReferenceId;

    @NotEmpty
    private String merchantId;

    @NotEmpty
    private String merchantTransactionId;

    private long disputedAmount;

    @NotEmpty
    private String currentState;

    @NotNull
    private LocalDateTime raisedAt;

    private String disputeIssuer;
    private String disputeCategory;
    private String disputeReferenceId;
    private String creationDate;
    private String updationDate;
}
