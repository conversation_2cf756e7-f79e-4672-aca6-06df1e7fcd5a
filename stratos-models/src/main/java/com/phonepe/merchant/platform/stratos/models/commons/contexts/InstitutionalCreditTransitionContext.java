package com.phonepe.merchant.platform.stratos.models.commons.contexts;

import com.phonepe.merchant.platform.stratos.models.commons.ContextType;
import com.phonepe.merchant.platform.stratos.models.commons.SourceType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S2637")
@EqualsAndHashCode(callSuper = true)
public class InstitutionalCreditTransitionContext extends TransitionContext {

    @NotNull
    private SourceType creditSourceType;

    private String creditSourceId;

    @Min(1)
    private long creditAmount;

    public InstitutionalCreditTransitionContext() {
        super(ContextType.INSTITUTIONAL_CREDIT_CONTEXT);
    }

    @Builder
    public InstitutionalCreditTransitionContext(
        final SourceType creditSourceType,
        final String creditSourceId,
        final long creditAmount) {
        this();
        this.creditSourceType = creditSourceType;
        this.creditSourceId = creditSourceId;
        this.creditAmount = creditAmount;
    }
}
