package com.phonepe.merchant.platform.stratos.models.disputes.commons;

import lombok.experimental.UtilityClass;

public enum ReconcileType {
    SUSPECTED_FRAUD_RECONCILE,
    REFUND_STATUS_RECONCILE,
    FRAUD_REPRESENTMENT_RECONCILE,
    REFUND_INITIATION_RECONCILE,
    INDEFINITE_HOLD_RECONCILE;

    @UtilityClass
    public static class Names {
        public static final String SUSPECTED_FRAUD_RECONCILE = "SUSPECTED_FRAUD_RECONCILE";
        public static final String REFUND_STATUS_RECONCILE = "REFUND_STATUS_RECONCILE";
        public static final String FRAUD_REPRESENTMENT_RECONCILE = "FRAUD_REPRESENTMENT_RECONCILE";
        public static final String INDEFINITE_HOLD_RECONCILE = "INDEFINITE_HOLD_RECONCILE";
        public static final String REFUND_INITIATION_RECONCILE = "REFUND_INITIATION_RECONCILE";
    }
}
