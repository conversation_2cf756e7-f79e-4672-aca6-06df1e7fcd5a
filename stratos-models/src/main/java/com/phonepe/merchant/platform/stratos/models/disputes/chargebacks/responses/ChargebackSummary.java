package com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses;

import java.time.LocalDateTime;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargebackSummary {

    @NotEmpty
    private String disputeId;

    private String disputeFileId;

    @NotEmpty
    private String disputeWorkflowId;

    @NotEmpty
    private String disputeWorkflowVersion;

    @NotEmpty
    private String disputeType;

    @NotEmpty
    private String disputeStage;

    @NotEmpty
    private String transactionReferenceId;

    @NotEmpty
    private String merchantId;

    @NotEmpty
    private String merchantTransactionId;

    private String instrumentTransactionId;

    private String disputeReferenceId;

    private String rrn;

    private String communicationId;

    private long transactionAmount;

    private long disputedAmount;

    private long acceptedAmount;

    private long penaltyAmount;

    @NotEmpty
    private String currentState;

    @NotNull
    private LocalDateTime raisedAt;

    private LocalDateTime respondBy;

    private String disputeIssuer;

    private String disputeCategory;
}
