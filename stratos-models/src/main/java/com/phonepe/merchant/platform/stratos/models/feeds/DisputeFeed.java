package com.phonepe.merchant.platform.stratos.models.feeds;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeFeed {

    @NotEmpty
    private String disputeId;

    @NotEmpty
    private String disputeWorkflowId;

    @NotNull
    private DisputeWorkflowVersionFeed disputeWorkflowVersion;

    @NotNull
    private DisputeTypeFeed disputeType;

    @NotNull
    private DisputeStageFeed disputeStage;

    @NotEmpty
    private String globalPaymentId;

    @NotEmpty
    private String transactionReferenceId;

    @NotEmpty
    private String merchantId;

    @NotEmpty
    private String merchantTransactionId;

    private long transactionAmount;

    private long disputedAmount;

    @Valid
    @NotNull
    private DisputeContext disputeContext;
}
