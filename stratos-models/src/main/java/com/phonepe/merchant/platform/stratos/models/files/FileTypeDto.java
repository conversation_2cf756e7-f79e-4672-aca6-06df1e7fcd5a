package com.phonepe.merchant.platform.stratos.models.files;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum FileTypeDto {
    YES(DisputeTypeDto.UPI_CHARGEBACK),
    APP(DisputeTypeDto.UPI_CHARGEBACK),
    ICP(DisputeTypeDto.UPI_CHARGEBACK),
    PG_FIRST_LEVEL(DisputeTypeDto.PG_CHARGEBACK),
    PG_PRE_ARBITRATION(DisputeTypeDto.PG_CHARGEBACK),
    EDC_FIRST_LEVEL(DisputeTypeDto.EDC_CHARGEBACK),
    NB_FIRST_LEVEL(DisputeTypeDto.NB_CHARGEBACK),
    PHP(DisputeTypeDto.WALLET_CHARGEBACK);

    @Getter
    private DisputeTypeDto disputeType;
}
