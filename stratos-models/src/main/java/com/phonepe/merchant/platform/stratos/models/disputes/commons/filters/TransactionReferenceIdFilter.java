package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType;
import java.util.Collections;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

@Data
@EqualsAndHashCode(callSuper = true)
public class TransactionReferenceIdFilter extends DisputeFilter {

    @NotEmpty
    private List<String> transactionReferenceIds;

    public TransactionReferenceIdFilter() {
        super(DisputeFilterType.TRANSACTION_REFERENCE_ID);
    }

    @Builder
    public TransactionReferenceIdFilter(@Singular final List<String> transactionReferenceIds) {
        this();
        this.transactionReferenceIds = transactionReferenceIds;
    }

    @JsonProperty("transactionReferenceId")
    public void setTransactionReferenceId(final String transactionReferenceId) {
        this.transactionReferenceIds = Collections.singletonList(transactionReferenceId);
    }

    @Override
    public <T> T accept(final DisputeFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
