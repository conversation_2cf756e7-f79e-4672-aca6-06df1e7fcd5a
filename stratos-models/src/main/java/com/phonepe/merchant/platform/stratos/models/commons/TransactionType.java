package com.phonepe.merchant.platform.stratos.models.commons;

import com.phonepe.merchant.platform.stratos.models.commons.visitors.TransactionTypeVisitor;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransactionType {
    UPI ("UPI"){
        @Override
        public <T> T accept(final TransactionTypeVisitor<T> visitor) {
            return visitor.visitUpi();
        }
    },
    PG ("PG"){
        @Override
        public <T> T accept(final TransactionTypeVisitor<T> visitor) {
            return visitor.visitPg();
        }
    },
    EDC ("EDC"){
        @Override
        public <T> T accept(TransactionTypeVisitor<T> visitor) {
            return visitor.visitEdc();
        }
    },
    NET_BANKING("NB") {
        @Override
        public <T> T accept(TransactionTypeVisitor<T> visitor) {
            return visitor.visitNetbanking();
        }
    },
    WALLET("WALLET") {
        @Override
        public <T> T accept(TransactionTypeVisitor<T> visitor) {
            return visitor.visitWallet();
        }
    },
    UDIR_OUTGOING("UDIR_OUTGOING"){
        @Override
        public <T> T accept(final TransactionTypeVisitor<T> visitor) {
            return visitor.visitUdirOutgoing();
        }
    },
    ;


    public abstract <T> T accept(TransactionTypeVisitor<T> visitor);

    private final String name;

    public static TransactionType fromString(String name) {
        for(TransactionType t: TransactionType.values()){
            if(t.getName().equals(name)){
                return t;
            }
        }
        return null;
    }
}
