package com.phonepe.merchant.platform.stratos.models.disputes;

import com.phonepe.merchant.platform.stratos.models.disputes.visitors.DisputeStageDtoVisitor;

/* Dto of DisputeStage Class. Please Update this whenever there is any change in DisputeStage class */
public enum DisputeStageDto {
    FIRST_LEVEL {
        @Override
        public <T> T accept(DisputeStageDtoVisitor<T> visitor) {
            return visitor.visitFirstLevel();
        }
    },
    PRE_ARBITRATION {
        @Override
        public <T> T accept(DisputeStageDtoVisitor<T> visitor) {
            return visitor.visitPreArbitration();
        }
    };

    public abstract <T> T accept(final DisputeStageDtoVisitor<T> visitor);
}
