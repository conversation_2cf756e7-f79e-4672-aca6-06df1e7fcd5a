package com.phonepe.central.stratos.penalty.escalation;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class EscalationLevelTest {

    @Test
    public void testEnumValues() {
        EscalationLevel[] expectedValues = EscalationLevel.values();
        assertEquals(4, expectedValues.length);
    }

    @Test
    public void testNextEscalationLevel() {
        assertEquals(EscalationLevel.L2, EscalationLevel.L1.getNext());
        assertEquals(EscalationLevel.L3, EscalationLevel.L2.getNext());
        assertEquals(EscalationLevel.L4, EscalationLevel.L3.getNext());
        assertNull(EscalationLevel.L4.getNext());
    }

    @Test
    public void testValueOf() {
        assertEquals(EscalationLevel.L1, EscalationLevel.valueOf("L1"));
        assertEquals(EscalationLevel.L2, EscalationLevel.valueOf("L2"));
        assertEquals(EscalationLevel.L3, EscalationLevel.valueOf("L3"));
        assertEquals(EscalationLevel.L4, EscalationLevel.valueOf("L4"));
    }
}
