package com.phonepe.central.stratos.penalty.escalation;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class EscalationTypeTest {

    @Test
    public void testEnumValues() {
        EscalationType[] expectedValues = EscalationType.values();
        assertEquals(1, expectedValues.length);
        assertEquals(EscalationType.PENALTY, expectedValues[0]);
    }

    @Test
    public void testValueOf() {
        assertEquals(EscalationType.PENALTY, EscalationType.valueOf("PENALTY"));
    }
}
