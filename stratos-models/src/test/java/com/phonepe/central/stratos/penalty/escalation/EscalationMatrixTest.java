package com.phonepe.central.stratos.penalty.escalation;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Set;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class EscalationMatrixTest {

    private EscalationMatrix escalationMatrix;

    @BeforeEach
    public void setUp(){
        escalationMatrix = new EscalationMatrix();
        escalationMatrix.setEscalationType(EscalationType.PENALTY);
    }

    @Test
    public void testIfLevelConfigIsNull() {
        escalationMatrix.setLevelConfig(null);

        boolean result = escalationMatrix.isValid();

        assertFalse(result, "EscalationMatrix should be invalid when levelConfig is null");
    }

    @Test
    public void testLevelConfigIncorrectSize() {
        escalationMatrix.setLevelConfig(Set.of(
                EscalationLevelConfig.builder().level(EscalationLevel.L1).build(),
                EscalationLevelConfig.builder().level(EscalationLevel.L2).build()
        ));
        assertFalse(escalationMatrix.isValid());
    }

    @Test
    public void testIfMatrixIsValid() {

        escalationMatrix.setLevelConfig(Set.of(
                EscalationLevelConfig.builder().level(EscalationLevel.L1).build(),
                EscalationLevelConfig.builder().level(EscalationLevel.L2).build(),
                EscalationLevelConfig.builder().level(EscalationLevel.L3).build(),
                EscalationLevelConfig.builder().level(EscalationLevel.L4).build()
        ));

        boolean result = escalationMatrix.isValid();

        assertTrue(result, "EscalationMatrix should be valid when all levels are present");
    }
}
