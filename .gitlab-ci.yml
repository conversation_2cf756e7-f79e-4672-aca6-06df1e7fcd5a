variables:
  STAGE_BRANCH: stage

.default: &default
  only:
    - merge_requests
    - web

.default_tag: &default_tag
  tags:
    - backend-docker-large

image: docker.phonepe.com/ci/ubuntu/jammy/jdk/openjdk/17/maven/3.9.9:latest

stages:
  - build
  - quality
  - deploy
  - merge
  - release

build_package:
  stage: build
  <<: *default
  <<: *default_tag
  when: manual
  allow_failure: false
  script:
    - mvn clean package -U -DskipTests=true -Dmaven.install.skip=true
  except:
    refs:
      - master
      - develop
      - stage

merge_ready:
  stage: quality
  only:
    - merge_requests
  <<: *default_tag
  # This change is required to ensure sonar maven plugin works with old versions of SonarQube server (<9.1)
  before_script:
    - export MAVEN_OPTS="--add-opens java.base/java.lang=ALL-UNNAMED"
  script:
    - mvn clean install -U -Pquality_check -Dproject.version='CI-TEST-${CI_JOB_ID}-SNAPSHOT' -Dsonar.pullrequest.key=$CI_MERGE_REQUEST_IID -Dsonar.pullrequest.branch=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME -Dsonar.pullrequest.base=$CI_MERGE_REQUEST_TARGET_BRANCH_NAME
  artifacts:
    reports:
      junit:
        - stratos-server/target/surefire-reports/TEST-*.xml

deploy_snapshot:
  stage: deploy
  <<: *default
  <<: *default_tag
  when: manual
  allow_failure: false
  script:
    - export MAVEN_OPTS="-Xms3072m -Xmx3072m"
    - mvn deploy -U -Ddocker.build=true -DskipTests=true -Dmaven.install.skip=true
  except:
    refs:
      - master
      - develop
      - stage

build_and_deploy_stage_snapshot:
  stage: deploy
  <<: *default_tag
  script:
    - export MAVEN_OPTS="-Xms3072m -Xmx3072m"
    - "VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)"
    - "mvn versions:set -DgenerateBackupPoms=false -DnewVersion=${VERSION/SNAPSHOT/STAGE-SNAPSHOT}"
    - mvn -U clean package deploy -Ddocker.build=true -DskipTests=true -Dmaven.install.skip=true
  only:
    refs:
      - stage

stage_merge_status_check:
  stage: merge
  <<: *default_tag
  when: manual
  allow_failure: false
  script:
    - git merge-base --is-ancestor ${CI_COMMIT_SHA} origin/${STAGE_BRANCH}
  only:
    - merge_requests
  except:
    refs:
      - master
      - develop
      - stage

merge:
  stage: merge
  <<: *default_tag
  script:
    - mvn clean install -U -Pquality_check
  only:
    refs:
      - develop

release:
  <<: *default_tag
  stage: release
  script:
    - git remote set-url origin "**********************:${CI_PROJECT_PATH}.git"
    - git checkout master
    - git pull
    - git checkout develop
    - git pull
    - mvn -U -Ddocker.build=true jgitflow:release-start jgitflow:release-finish
    - git push --all
    - git push --tags origin
  when: manual
  only:
    refs:
      - develop

include:
  - project: qa/deploywizard
    file: "/config_validation.yml"

validate_prod_mirror_config:
  variables:
    SERVICE_NAME: stratos
    SERVER_MODULE: stratos-server
    CONFIG_PATH: stratos-server/config/stage-stable.yml
    DC_ID: nb6
